# frozen_string_literal: true

require "rails_helper"

RSpec.describe Payout::Export do
  include ActiveSupport::Testing::TimeHelpers

  describe "#call" do
    context "when withdraw requests have valid status" do
      let!(:business) { create(:business, :with_cashback) }

      let!(:user_one) { create(:user, cpf: "***********") }
      let!(:payout_one) do
        create(:payout,
          user_business: business,
          user: user_one,
          receiver_taxpayer_number: "***********",
          total_amount: 22.54,
          status: Payout::Status::LOCKED)
      end

      let!(:user_two) { create(:user, cpf: "***********") }
      let!(:payout_two) do
        create(:payout,
          user_business: business,
          user: user_two,
          total_amount: 144,
          receiver_taxpayer_number: "***********",
          status: Payout::Status::LOCKED)
      end

      before do
        travel_to Time.zone.parse("2022-04-05 16:33:09")
      end

      let(:expected_file_content) do
        <<~CSV
          chave,valor,descricao
          ***********,22.54,Pix Resgate #{business.project_config.name}
          ***********,144.0,Pix Resgate #{business.project_config.name}
        CSV
      end
      let(:current_date) { DateTime.current.strftime("%Y%m%d%H%M%S") }
      let(:expected_return) do
        {
          file: expected_file_content,
          filename: "remessa-#{current_date}.csv"
        }
      end

      it "returns hash with file and filename" do
        exporter_return = Payout::Export.new(
          batches: [payout_one, payout_two],
          format: Payout::Export::Csv
        ).call

        expect(exporter_return).to eq(expected_return)
        expect([payout_one, payout_two].each(&:reload)).to all(be_locked)
      end
    end

    context "when kinds are different" do
      let(:payout_pix) { create(:payout, :pix, :with_user, status: "locked") }
      let(:payout_bank_slip) { create(:payout, :bank_slip, :with_user, status: "locked") }

      it "expect does not block any resources" do
        exporter = Payout::Export.new(
          batches: [payout_pix, payout_bank_slip],
          format: Payout::Export::Csv
        )

        expect(exporter.call).to be_falsey
        expect(exporter.errors.as_json).to eq({
          kinds: ["deve ser único"]
        })
        expect(exporter.errors.full_messages).to eq(["Tipos deve ser único"])
      end
    end

    context "when withdraw requests have invalid status" do
      let(:payout_open) { create(:payout, :with_user, status: "open") }
      let(:payout_in_transfer) { create(:payout, :with_user, status: "in_transfer") }

      it "expected returns errors" do
        exporter = Payout::Export.new(
          batches: [payout_open, payout_in_transfer],
          format: Payout::Export::Csv
        )

        expect(exporter.call).to be_falsey
        expect(exporter.errors.as_json).to eq({statuses: ["devem ser: travadas"]})
        expect(exporter.errors.full_messages).to match_array(["Status devem ser: travadas"])
      end
    end

    context "when arguments is invalids" do
      it "expected returns errors" do
        exporter = Payout::Export.new(
          batches: [],
          format: nil
        )

        expect(exporter.call).to be_falsey
        expect(exporter.errors.as_json).to eq({
          batches: ["não pode ficar em branco"],
          format: ["não pode ficar em branco"]
        })
      end
    end
  end
end
