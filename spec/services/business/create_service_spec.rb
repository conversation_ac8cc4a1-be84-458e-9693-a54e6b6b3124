# frozen_string_literal: true

require "rails_helper"
require "sidekiq/testing"

RSpec.describe Business::CreateService do
  context "when creating whitelabel business" do
    let!(:organization) { create(:organization) }
    let(:params) do
      {
        name: FFaker::Name.name,
        api_secret: SecureRandom.hex,
        cnpj: FFaker::IdentificationBR.cnpj,
        cashback: true,
        currency: "BRL",
        cashback_wallet_destination: Wallet::Kind::CASHBACK,
        cashback_requestable_amount: 20,
        integration_type: Enums::Business::IntegrationType::API,
        user_request_withdrawal: true,
        telemedicine_config: {
          plan: 11001
        }
      }
    end
    let(:cashback_transfer_frequency_params) do
      {
        recurring_type: Enums::CashbackTransferFrequency::WEEKLY,
        interval: 1,
        day_of_week: Date::DAYNAMES.index("Friday")
      }
    end

    it "creates business, organization approvals, default menus and secret keys" do
      Sidekiq::Testing.inline! do
        described_class.call(params:, cashback_transfer_frequency_params:)

        business = Business.last
        expect(business.name).to eq(params[:name])
        expect(business.menus.pluck(:disposition).uniq.count).to eq(5)
        expect(business.menus.where(disposition: Navigation::Disposition::PROFILE_ICON)).to exist
        expect(business.cashback_wallet_destination).to eq(Wallet::Kind::CASHBACK)
        expect(business.cashback_transfer_frequency).to eq(nil)
        expect(CashbackTransferFrequency.count).to eq(0)
        expect(business.project_config).to be_present
        expect(business.giftcard_config).to be_present
        expect(business.mailer_config).to be_present
        expect(business.webhook_config).to be_present
        expect(business.telemedicine_config).to be_present
        expect(business.signature_secret).to be_present
        expect(business.member_referral_config).to be_present
        expect(business.api_secret).to be_present
        expect(business.user_request_withdrawal).to be true
        expect(business.fund_redemption_configs.azul.where(min_amount: 1, multiple: 1).count).to eq(1)
      end
    end

    context "with business as cahsback transfer receiver" do
      before do
        params.merge!(
          cashback_wallet_destination: Wallet::Kind::MEMBERSHIP,
          cashback_requestable_amount: 120
        )
      end

      it "creates the business with cashback transfer frequency" do
        described_class.call(params:, cashback_transfer_frequency_params:)

        business = Business.last
        expect(business.cashback_wallet_destination).to eq(Wallet::Kind::MEMBERSHIP)
        expect(CashbackTransferFrequency.count).to eq(1)
        expect(business.cashback_transfer_frequency).to eq(CashbackTransferFrequency.first)
        expect(business.cashback_transfer_frequency.recurring_type).to eq(Enums::CashbackTransferFrequency::WEEKLY)
        expect(business.cashback_transfer_frequency.interval).to eq(1)
        expect(business.cashback_transfer_frequency.day_of_week).to eq(5)
        expect(business.user_request_withdrawal).to be false
      end
    end

    context "without cashback" do
      it "creates the business with all cashback related params as nil" do
        described_class.call(params: params.except(:cashback))

        business = Business.last
        expect(business.cashback).to be false
        expect(business.cashback_transfer_frequency).to eq(nil)
        expect(business.user_request_withdrawal).to be false
        expect(CashbackTransferFrequency.count).to eq(0)
      end
    end

    context "with main business" do
      let!(:main_business) { create(:business) }
      let(:service) do
        described_class.new(
          params:,
          cashback_transfer_frequency_params:
        )
      end

      before do
        params[:main_business_id] = main_business.id
      end
      it "creates the business as child of main business" do
        expect { service.call }.to change(Business, :count).by(1)

        business = Business.last
        expect(business.main_business).to eq(main_business)
        expect(business.integration_type).to eq(main_business.integration_type)
      end
    end

    context "when transfer receiver is a business" do
      before do
        params[:cashback_wallet_destination] = Wallet::Kind::MEMBERSHIP
      end

      it "creates business, organization approvals, default menus and secret key" do
        Sidekiq::Testing.inline! do
          described_class.call(params:, cashback_transfer_frequency_params:)

          business = Business.last
          expect(business.name).to eq(params[:name])
          expect(business.menus.pluck(:disposition).uniq.count).to eq(5)
          expect(business.menus.where(disposition: Navigation::Disposition::PROFILE_ICON)).to exist
          expect(business.cashback_wallet_destination).to eq(Wallet::Kind::MEMBERSHIP)
          expect(business.cashback_transfer_frequency).not_to eq(nil)
          expect(business.project_config).to be_present
          expect(business.signature_secret).to be_present
          expect(business.api_secret).to be_present
          expect(business.user_request_withdrawal).to be false
        end
      end
    end
  end

  context "when business has giftcard" do
    it "creates business with giftcards menu items active" do
      params = {
        name: FFaker::Name.name,
        api_secret: SecureRandom.hex,
        cnpj: FFaker::IdentificationBR.cnpj,
        giftcard: true,
        integration_type: Enums::Business::IntegrationType::API,
        telemedicine_config: {
          plan: 11001
        }
      }

      expect { described_class.call(params:) }.to change(Business, :count).by(1)
      business = Business.last
      giftcard_menus =
        Navigation
          .kind_menu
          .joins(:business)
          .where(business: {id: business.id})
          .where(feature_flag_rule: Navigation::FeatureFlagRule::GIFT_CARD)
      expect(giftcard_menus).to all be_active
    end
  end

  context "when business does not have giftcard" do
    it "creates business with giftcards menu items inactive" do
      params = {
        name: FFaker::Name.name,
        api_secret: SecureRandom.hex,
        cnpj: FFaker::IdentificationBR.cnpj,
        integration_type: Enums::Business::IntegrationType::API,
        giftcard: false,
        telemedicine_config: {
          plan: 11001
        }
      }

      expect { described_class.call(params:) }.to change(Business, :count).by(1)
      business = Business.last
      giftcard_menus =
        Navigation
          .kind_menu
          .joins(:business)
          .where(business: {id: business.id})
          .where(feature_flag_rule: Navigation::FeatureFlagRule::GIFT_CARD)
      expect(giftcard_menus).not_to include be_active
    end
  end
end
