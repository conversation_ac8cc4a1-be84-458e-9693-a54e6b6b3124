# frozen_string_literal: true

require "rails_helper"

RSpec.describe Business::UpdateService do
  context "when creating whitelabel business" do
    context "when is sub business" do
      let!(:oab) { create(:business, :oab, :with_cashback, must_approve_organization: true, spread_percent: 10) }
      let!(:business) do
        create(
          :business,
          :with_cashback,
          must_approve_organization: true,
          spread_percent: 10,
          main_business_id: oab.id
        )
      end
      let!(:coupon) { create(:cupon, :online, :with_organization) }
      let!(:user) { create(:user, business: oab) }
      let!(:order) do
        create(:order,
          :online,
          redeem_code: coupon.code,
          user:,
          cupon: coupon)
      end
      let!(:cashback) { create(:cashback_record, order:, business: oab) }
      let(:params) do
        {
          name: FFaker::Name.name,
          api_secret: SecureRandom.hex,
          cnpj: FFaker::IdentificationBR.cnpj,
          spread_percent: 20,
          integration_type: Enums::Business::IntegrationType::APP
        }
      end
      it "update business" do
        described_class.call(params:, business:)

        business.reload
        expect(business.name).to eq(params[:name])
        expect(business.api_secret).to eq(params[:api_secret])
        expect(business.cnpj).to eq(params[:cnpj])
        expect(business.spread_percent).to eq(params[:spread_percent])
        expect(business.integration_type).not_to eq(Enums::Business::IntegrationType::API)
      end
    end

    context "when is main business" do
      let!(:business) { create(:business, cashback: false, must_approve_organization: true, spread_percent: 10) }
      let!(:coupon) { create(:cupon, :online, :with_organization) }
      let!(:user) { create(:user, business:) }
      let!(:order) { create(:order, :online, redeem_code: coupon.code, user:, cupon: coupon) }
      let!(:cashback) { create(:cashback_record, order:, business:) }
      let(:params) do
        {
          name: FFaker::Name.name,
          api_secret: SecureRandom.hex,
          cnpj: FFaker::IdentificationBR.cnpj,
          spread_percent: 20,
          integration_type: Enums::Business::IntegrationType::APP
        }
      end

      before do
        allow(Business::CreateWallets).to receive(:call).with(business)
        allow(Business::MigrateCashbackWalletDestination).to receive(:call).with(business)
      end

      it "updates business and creates wallets when enables cashback" do
        params[:cashback] = true
        params[:cashback_requestable_amount] = 20

        described_class.call(params:, business:)

        business.reload
        expect(business.name).to eq(params[:name])
        expect(business.api_secret).to eq(params[:api_secret])
        expect(business.cnpj).to eq(params[:cnpj])
        expect(business.spread_percent).to eq(params[:spread_percent])
        expect(business.integration_type).to eq(Enums::Business::IntegrationType::APP)
        expect(Business::CreateWallets).to have_received(:call).with(business)
        expect(Business::MigrateCashbackWalletDestination).not_to have_received(:call)
      end

      it "updates business and does not create wallets when cashback already enabled" do
        business.update_columns(cashback: true, cashback_requestable_amount: 20)
        params[:cashback] = true

        described_class.call(params:, business:)

        business.reload
        expect(business.name).to eq(params[:name])
        expect(business.api_secret).to eq(params[:api_secret])
        expect(business.cnpj).to eq(params[:cnpj])
        expect(business.spread_percent).to eq(params[:spread_percent])
        expect(business.integration_type).to eq(Enums::Business::IntegrationType::APP)
        expect(Business::CreateWallets).not_to have_received(:call)
        expect(Business::MigrateCashbackWalletDestination).not_to have_received(:call)
      end

      it "updates business and does not migrate to wallet" do
        described_class.call(params:, business:)

        business.reload
        expect(business.name).to eq(params[:name])
        expect(business.api_secret).to eq(params[:api_secret])
        expect(business.cnpj).to eq(params[:cnpj])
        expect(business.spread_percent).to eq(params[:spread_percent])
        expect(business.integration_type).to eq(Enums::Business::IntegrationType::APP)
        expect(Business::CreateWallets).not_to have_received(:call)
        expect(Business::MigrateCashbackWalletDestination).not_to have_received(:call)
      end

      context "when changing cashback wallet destination" do
        let!(:sub_business) do
          create(:business, main_business: business, cashback_wallet_destination: business.cashback_wallet_destination)
        end
        let(:params) { {cashback_wallet_destination: Wallet::Kind::MEMBERSHIP} }

        context "and financial process is ok" do
          it "migrates and changes sub businesses destination" do
            expect { described_class.call(params:, business:) }
              .to change(business, :cashback_wallet_destination).to(Wallet::Kind::MEMBERSHIP)
              .and change { sub_business.reload.cashback_wallet_destination }.to(Wallet::Kind::MEMBERSHIP)

            expect(Business::MigrateCashbackWalletDestination).to have_received(:call).with(business)
          end
        end

        shared_examples "not migratable" do
          it "does not migrate" do
            expect { described_class.call(params:, business:) }
              .to raise_error(ActiveRecord::RecordInvalid, /Não foi possível migrar de carteira pois a gestão financeira não foi finalizada/)
              .and not_change { business.reload.cashback_wallet_destination }
              .and not_change { sub_business.reload.cashback_wallet_destination }

            expect(Business::MigrateCashbackWalletDestination).not_to have_received(:call)
          end
        end

        context "and there is user wallet with balance" do
          let!(:wallet) { create(:wallet, kind: :cashback, currency: :BRL, user:, balance: 1) }

          it_behaves_like "not migratable"
        end

        context "and there is payout in process" do
          let!(:payout) { create(:payout, :locked, business:) }

          it_behaves_like "not migratable"
        end

        context "and there is fund in process" do
          let!(:cashback_record) { create(:cashback_record, :in_transfer_lecupon, user:) }

          it_behaves_like "not migratable"
        end
      end

      context "when activating business that has cashback already enabled" do
        let!(:business) do
          create(:business, cashback: true, cashback_requestable_amount: 20, status: Business::Status::INACTIVE)
        end
        let(:params) { {status: Business::Status::ACTIVE} }

        it "updates business and creates wallets" do
          expect { described_class.call(params:, business:) }
            .to change { business.reload.active }.to(true)

          expect(Business::CreateWallets).to have_received(:call).with(business)
          expect(Business::MigrateCashbackWalletDestination).not_to have_received(:call)
        end
      end
    end
  end

  context "with user as cashback transfer receiver" do
    let!(:business) { create(:business, :oab, cashback_manage: true) }
    let(:params) do
      {
        name: FFaker::Name.name,
        cashback: true,
        cashback_wallet_destination: Wallet::Kind::CASHBACK,
        cashback_requestable_amount: 20,
        user_request_withdrawal: true
      }
    end
    let(:cashback_transfer_frequency_params) do
      {
        recurring_type: Enums::CashbackTransferFrequency::WEEKLY,
        interval: 1,
        day_of_week: Date::DAYNAMES.index("Friday")
      }
    end

    it "updates the business" do
      described_class.call(params:, business:, cashback_transfer_frequency_params:)

      business.reload
      expect(business.cashback_wallet_destination).to eq(Wallet::Kind::CASHBACK)
      expect(business.cashback_transfer_frequency).to eq(nil)
      expect(business.user_request_withdrawal).to be true
      expect(CashbackTransferFrequency.count).to eq(0)
    end

    context "with cashback false" do
      it "updates the business setting all cashback related params as nil" do
        described_class.call(params: params.merge(cashback: false), business:)

        business.reload
        expect(business.cashback).to be false
        expect(business.cashback_transfer_frequency).to eq(nil)
        expect(business.user_request_withdrawal).to be false
        expect(business.cashback_manage).to be false
        expect(CashbackTransferFrequency.count).to eq(0)
      end
    end
  end

  context "with business as cashback transfer receiver" do
    let!(:business) { create(:business, :oab) }
    let(:params) do
      {
        name: FFaker::Name.name,
        cashback: true,
        cashback_wallet_destination: Wallet::Kind::MEMBERSHIP,
        cashback_requestable_amount: 20
      }
    end
    let(:cashback_transfer_frequency_params) do
      {
        recurring_type: Enums::CashbackTransferFrequency::WEEKLY,
        interval: 1,
        day_of_week: Date::DAYNAMES.index("Friday")
      }
    end

    it "updates the business and creates its cashback transfer frequency" do
      described_class.call(params:, business:, cashback_transfer_frequency_params:)

      business.reload
      expect(business.cashback_wallet_destination).to eq(Wallet::Kind::MEMBERSHIP)
      expect(CashbackTransferFrequency.count).to eq(1)
      expect(business.cashback_transfer_frequency).to eq(CashbackTransferFrequency.first)
      expect(business.cashback_transfer_frequency.recurring_type).to eq(Enums::CashbackTransferFrequency::WEEKLY)
      expect(business.cashback_transfer_frequency.interval).to eq(1)
      expect(business.cashback_transfer_frequency.day_of_week).to eq(5)
      expect(business.user_request_withdrawal).to be false
    end

    context "when business already has transfer frequency set" do
      before do
        business.update!(cashback_wallet_destination: Wallet::Kind::MEMBERSHIP)
        business.create_cashback_transfer_frequency!(
          recurring_type: Enums::CashbackTransferFrequency::MONTHLY,
          interval: 3,
          day_of_month: 10
        )
      end

      it "updates the frequency" do
        described_class.call(params:, business:, cashback_transfer_frequency_params:)

        expect(business.cashback_wallet_destination).to eq(Wallet::Kind::MEMBERSHIP)
        expect(CashbackTransferFrequency.count).to eq(1)
        expect(business.cashback_transfer_frequency).to eq(CashbackTransferFrequency.first)
        expect(business.cashback_transfer_frequency.recurring_type).to eq(Enums::CashbackTransferFrequency::WEEKLY)
        expect(business.cashback_transfer_frequency.interval).to eq(1)
        expect(business.cashback_transfer_frequency.day_of_week).to eq(5)
        expect(business.user_request_withdrawal).to be false
      end
    end

    context "with cashback false" do
      it "updates the business setting all cashback related params as nil" do
        described_class.call(params: params.merge(cashback: false), business:)

        business.reload
        expect(business.cashback).to be false
        expect(business.cashback_transfer_frequency).to eq(nil)
        expect(business.user_request_withdrawal).to be false
        expect(business.cashback_manage).to be false
        expect(CashbackTransferFrequency.count).to eq(0)
      end
    end
  end

  context "when business has giftcard and deactivates giftcard" do
    it "updates business and its giftcard menu items" do
      business = create(:business, giftcard: true)
      menu = create(:menu, business:, active: true, feature_flag_rule: Navigation::FeatureFlagRule::GIFT_CARD)
      params = {giftcard: false}

      expect { described_class.call(business:, params:) }
        .to change { business.reload.giftcard }.from(true).to(false)
        .and change { menu.reload.active }.from(true).to(false)
    end
  end

  context "when business does not have giftcard" do
    it "updates business and its giftcard menu items" do
      business = create(:business, giftcard: false)
      menu = create(:menu, business:, active: false, feature_flag_rule: Navigation::FeatureFlagRule::GIFT_CARD)
      params = {giftcard: true}

      expect { described_class.call(business:, params:) }
        .to change { business.reload.giftcard }.from(false).to(true)
        .and change { menu.reload.active }.from(false).to(true)
    end
  end

  context "when business has prize draw and deactivates prize draw" do
    it "updates business and its prize draw menu items" do
      business = create(:business, prize_draw: true)
      menu = create(:menu, business:, active: true, feature_flag_rule: Navigation::FeatureFlagRule::PRIZE_DRAW)
      params = {prize_draw: false}

      expect { described_class.call(business:, params:) }
        .to change { business.reload.prize_draw }.from(true).to(false)
        .and change { menu.reload.active }.from(true).to(false)
    end
  end

  context "when business does not have prize draw" do
    it "updates business and its prize draw menu items" do
      business = create(:business, prize_draw: false)
      menu = create(:menu, business:, active: false, feature_flag_rule: Navigation::FeatureFlagRule::PRIZE_DRAW)
      params = {prize_draw: true}

      expect { described_class.call(business:, params:) }
        .to change { business.reload.prize_draw }.from(false).to(true)
        .and change { menu.reload.active }.from(false).to(true)
    end
  end

  describe "#update_users_flags" do
    context "when updated business is main business" do
      let!(:business) { create(:business, giftcard: false, telemedicine: false) }
      let!(:users) { create_list(:user, 2, business:) }

      let!(:sub_business_with_flag) { create(:business, giftcard: true, telemedicine: true, main_business: business) }
      let!(:users_on_sub_business_with_flag) { create_list(:user, 2, business: sub_business_with_flag) }

      let!(:sub_business_without_flag) { create(:business, giftcard: false, telemedicine: false, main_business: business) }
      let!(:users_on_sub_business_without_flag) { create_list(:user, 2, business: sub_business_without_flag) }

      it "must recalculate giftcard and cashback flags for all related businesses" do
        expect do
          described_class.call(business:, params: {giftcard: true})
          users.map!(&:reload)
          users_on_sub_business_with_flag.map!(&:reload)
          users_on_sub_business_without_flag.map!(&:reload)
        end.to change { users.pluck(:giftcard) }.to([true, true])
          .and change { users_on_sub_business_with_flag.pluck(:giftcard) }.to([true, true])
        expect(users_on_sub_business_without_flag.pluck(:giftcard)).to eq([false, false])
      end
    end

    context "when updated business is sub business" do
      let!(:business) { create(:business, giftcard: true) }
      let!(:sub_business_one) { create(:business, giftcard: false, telemedicine: false, main_business: business) }
      let!(:users_on_sub_business_one) { create_list(:user, 2, business: sub_business_one) }

      let!(:sub_business_two) { create(:business, giftcard: false, telemedicine: false, main_business: business) }
      let!(:users_on_sub_business_two) { create_list(:user, 2, business: sub_business_two) }

      it "must recalculate giftcard, cashback flags" do
        expect do
          described_class.call(business: sub_business_one, params: {giftcard: true})
          users_on_sub_business_one.map!(&:reload)
          users_on_sub_business_two.map!(&:reload)
        end.to change { users_on_sub_business_one.pluck(:giftcard) }.from([false, false]).to([true, true])
        expect(users_on_sub_business_two.pluck(:giftcard)).to eq([false, false])
      end
    end
  end
end
