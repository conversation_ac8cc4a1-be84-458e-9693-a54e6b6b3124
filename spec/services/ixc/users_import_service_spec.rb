require "rails_helper"

RSpec.describe Ixc::UsersImportService do
  let!(:users) do
    (1..4).collect do |user|
      {
        name: FFaker::Name.name,
        email: FFaker::Internet.email,
        cpf: CPF.new(FFaker::IdentificationBR.cpf).stripped
      }
    end
  end
  let!(:group) { create(:authorized_user_group, business:) }
  let!(:ixc_token) { create(:ixc_token, business:, authorized_user_group: group) }
  let!(:business) { create(:business) }

  let(:service) { described_class.new(business:, batch_size: 4) }
  let(:template_mock) { double("template_mock", to_json: "{}") }

  before do
    allow(Ixc::ContractUsers).to receive_message_chain(:new, :list_all).and_return({foo: "bar"})
    allow(Ixc::Users).to receive_message_chain(:new, :list_all).and_return(users)
    allow(Slack::MessageTemplate::Simple).to receive(:new).and_return(template_mock)
    allow(Slack::Api).to receive(:send_message)
      .with(template_mock.to_json)
  end

  describe "#call" do
    context "when has users" do
      describe "#inactivate_users" do
        let!(:authorized_users_to_inactivate) do
          create_list(:authorized_user, 4, :with_user, active: true, business:)
          create_list(:authorized_user, 4, :with_user, authorized_user_group: group, active: true, business:)
        end

        it "must inactivate AuthorizedUser on same group based on imported users" do
          expect { service.call }
            .to change(business.authorized_users.where(authorized_user_group: group, active: false), :count).by(4)
            .and change(business.users.where(active: false), :count).by(4)
        end

        context "when authorized user is unsyced inactive" do
          before do
            AuthorizedUser.where(authorized_user_group: group, active: true)
              .first.update(active: false)
          end

          it "must ignore on inactivation" do
            expect { service.call }.to change(AuthorizedUser.where(authorized_user_group: group, active: false), :count).by(3)
          end
        end
      end

      describe "#save_users" do
        it "must create AuthorizedUser for imported users" do
          expect { service.call }.to change(AuthorizedUser, :count).by(4)
          expect(AuthorizedUser.pluck(:business_id)).to all eq business.id
        end

        context "with already created authorized users" do
          let!(:auth_user_one) do
            create(
              :authorized_user,
              business:,
              authorized_user_group: group,
              cpf: users[0][:cpf],
              name: "old name 1",
              email: "<EMAIL>"
            )
          end
          let!(:auth_user_two) do
            create(
              :authorized_user,
              business:,
              authorized_user_group: group,
              cpf: users[1][:cpf],
              name: "old name 2",
              email: "<EMAIL>"
            )
          end

          context "when existing authorized_user is on ixc group" do
            it "updates the existing authorized users" do
              expect do
                service.call

                auth_user_one.reload
                auth_user_two.reload
              end.to change(AuthorizedUser, :count).by(2)
                .and change(auth_user_one, :name).from("Old Name 1").to(Utils::NameNormalizer.call(users[0][:name]))
                .and change(auth_user_one, :email).from("<EMAIL>").to(users[0][:email])
                .and change(auth_user_two, :name).from("Old Name 2").to(Utils::NameNormalizer.call(users[1][:name]))
                .and change(auth_user_two, :email).from("<EMAIL>").to(users[1][:email])
            end
          end

          context "when existing authorized_user is not on ixc group" do
            let!(:auth_user_one) do
              create(
                :authorized_user,
                business:,
                authorized_user_group: nil,
                cpf: users[0][:cpf],
                name: "old name 2",
                email: "<EMAIL>"
              )
            end

            it "must not update authorized user" do
              expect do
                service.call

                auth_user_one.reload
              end.not_to change { auth_user_one }
            end
          end
        end
      end

      describe "#activate_users" do
        let(:ixc_users) do
          users.first(2).each do |user|
            user[:business_id] = business.id
            user[:authorized_user_group_id] = group.id
          end
        end

        let!(:authorized_users_to_reactivate) { ixc_users.map { |params| create(:authorized_user, params) } }
        let!(:authorized_users_ids_to_reactivate) { authorized_users_to_reactivate.pluck(:id) }

        before do
          authorized_users_to_reactivate.each do |authorized_user|
            User.create_from_authorization(authorized_user)
            authorized_user.update(active: false)
          end
        end

        it "must reactivate AuthorizedUser for imported users" do
          expect { service.call }
            .to change(AuthorizedUser.where(id: authorized_users_ids_to_reactivate, active: true), :count).by(2)
            .and change(User.where(authorized_user_id: authorized_users_ids_to_reactivate, active: true), :count).by(2)
        end

        it "must ignore already reactived authorized users" do
          AuthorizedUser.where(id: authorized_users_ids_to_reactivate).limit(1).update_all(active: true)

          expect { service.call }
            .to change(AuthorizedUser.where(id: authorized_users_ids_to_reactivate, active: true), :count).by(1)
            .and change(User.where(authorized_user_id: authorized_users_ids_to_reactivate, active: true), :count).by(1)
        end

        it "must ignore authorized users on another groups of same business if they're active" do
          AuthorizedUser.where(id: authorized_users_ids_to_reactivate).limit(1).update_all(active: true, authorized_user_group_id: nil)

          expect { service.call }
            .to change(AuthorizedUser.where(id: authorized_users_ids_to_reactivate, active: true), :count).by(1)
            .and change(User.where(authorized_user_id: authorized_users_ids_to_reactivate, active: true), :count).by(1)
        end

        it "must update inactive authorized users of another related business or groups" do
          related_business = create(:business, main_business: business)
          AuthorizedUser.where(id: authorized_users_ids_to_reactivate).limit(1).update_all(active: false, authorized_user_group_id: nil, business_id: related_business.id)

          expect { service.call }
            .to change(AuthorizedUser.where(id: authorized_users_ids_to_reactivate, active: true), :count).by(2)
            .and change(User.where(authorized_user_id: authorized_users_ids_to_reactivate, active: true), :count).by(2)
        end
      end
    end

    context "when has not users" do
      let(:users) { [] }

      it "notify in slack an integration error" do
        service.call
        expect(Slack::Api).to have_received(:send_message).once
      end
    end
  end
end
