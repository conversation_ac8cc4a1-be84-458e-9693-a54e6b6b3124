require "rails_helper"

RSpec.describe VouchersImportService do
  let(:file) { Rails.root.join("spec/fixtures/vouchers.csv").open }
  let(:promotion) { create(:promotion, :coupon_code) }
  let(:service) { described_class.new(file_path: file.path, resource: promotion) }

  describe "#call" do
    it "imports vouchers from the file" do
      expect {
        service.call
      }.to change(Voucher, :count).by(2)
        .and change(VoucherBucket, :count).by(1)

      expect(promotion.vouchers.count).to eq(2)
      expect(VoucherBucket.joins(:promotions).pluck("promotions.id")).to match_array([promotion.id])
      expect(Voucher.joins(:bucket).count).to eq(2)
    end

    context "when promotion already has a voucher_bucket" do
      it "expects add exis" do
        voucher_bucket = create(:voucher_bucket)
        promotion = create(:promotion, :coupon_code, voucher_bucket:)
        service = described_class.new(file_path: file.path, resource: promotion)

        expect {
          service.call
        }.to change(Voucher, :count).by(2)
          .and change(VoucherBucket, :count).by(0)

        expect(promotion.vouchers.count).to eq(2)
        expect(VoucherBucket.joins(:promotions).pluck("promotions.id")).to match_array([promotion.id])
      end
    end

    context "with no voucher" do
      context "when importing a file without headers" do
        let(:file) { Rails.root.join("spec/fixtures/vouchers_without_headers.csv").open }

        it "raises error" do
          expect {
            service.call
          }.to change(Voucher, :count).by(0)
            .and change(VoucherBucket, :count).by(0)

          expect(service.error_messages).to eq(["Linha 1: O cabeçalho do CSV deve estar no seguinte formato: codigo;inicio;fim"])
          expect(service.vouchers_with_error_csv).to eq(nil)
          expect(Voucher.joins(:bucket).count).to eq(0)
        end
      end
    end

    context "when importing already existing vouchers" do
      let(:file_with_error_after_import) { Rails.root.join("spec/fixtures/vouchers_after_import.csv").read }

      before do
        service.call
      end

      it "returns error and does not import any of them" do
        expect {
          service.call
        }.to change(Voucher, :count).by(0)
          .and change(VoucherBucket, :count).by(0)

        expect(service.error_messages.size).to eq(2)
        errors = service.error_messages.map { _1 }
        expect(errors).to match_array([
          "Linha 2: A validação falhou: Code já está em uso",
          "Linha 3: A validação falhou: Code já está em uso"
        ])
        expect(service.vouchers_with_error_csv).to eq(file_with_error_after_import)
      end
    end

    context "when importing a file with duplicated codes" do
      let(:file) { Rails.root.join("spec/fixtures/vouchers_duplicated_code.csv").open }
      let(:file_with_error_after_import) { Rails.root.join("spec/fixtures/vouchers_duplicated_code_after_import.csv").read }

      it "imports the correct vouchers and returns error for those already imported" do
        expect {
          service.call
        }.to change(Voucher, :count).by(4)
          .and change(VoucherBucket, :count).by(1)

        expect(service.error_messages.size).to eq(2)
        errors = service.error_messages.map { _1 }
        expect(errors).to match_array([
          "Linha 4: A validação falhou: Code já está em uso",
          "Linha 7: A validação falhou: Code já está em uso"
        ])
        expect(service.vouchers_with_error_csv).to eq(file_with_error_after_import)
        expect(Voucher.joins(:bucket).count).to eq(4)
      end
    end
  end
end
