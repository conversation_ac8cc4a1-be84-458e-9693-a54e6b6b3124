# frozen_string_literal: true

require "rails_helper"

RSpec.describe OrderDestroyService do
  let(:user) { create(:user) }
  let!(:coupon) { create(:cupon, :with_branch, :coupon_code) }
  let!(:bucket) { create(:voucher_bucket) }
  let!(:redeemed_voucher) { create(:voucher, bucket:) }
  let!(:order) do
    create(:order,
      voucher: redeemed_voucher,
      cupon: coupon,
      redeem_code: redeemed_voucher.code)
  end

  it "destroys the order and makes the voucher available" do
    described_class.call(order:)
    expect(Order.where(id: order.id)).not_to exist
    redeemed_voucher.reload
    expect(redeemed_voucher.user_id).to be_nil
    expect(redeemed_voucher.cupon_id).to be_nil
    expect(redeemed_voucher.cpf).to be_nil
    expect(redeemed_voucher.redeemed_at).to be_nil
    expect(redeemed_voucher.used_at).to be_nil
    expect(redeemed_voucher.queried_at).to be_nil
    expect(redeemed_voucher.expired_at).to eq(redeemed_voucher.expired_at)
  end

  it "must decrement coupon used count" do
    coupon.update!(quantity: 10, redeemed_count: 5, infinity: false)
    coupon.promotion.update!(quantity: 10, redeemed_count: 5, infinity: false)
    old_redeemed_count = coupon.redeemed_count
    promotion = order.promotion.reload
    expect do
      described_class.call(order:)
      coupon.reload
      promotion.reload
    end.to change(coupon, :redeemed_count).from(old_redeemed_count).to(old_redeemed_count - 1)
      .and change(promotion, :redeemed_count).to(old_redeemed_count - 1)
  end
end
