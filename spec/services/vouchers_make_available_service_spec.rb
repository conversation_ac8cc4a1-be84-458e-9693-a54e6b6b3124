# frozen_string_literal: true

require "rails_helper"

RSpec.describe VouchersMakeAvailableService do
  include ActiveSupport::Testing::TimeHelpers

  before do
    travel_to(Time.zone.parse("2020-01-01 10:00"))
  end

  let(:file) { Rails.root.join("spec/fixtures/coupon_codes_make_available.csv").open }
  let!(:bucket_one) { create(:voucher_bucket) }
  let!(:bucket_two) { create(:voucher_bucket) }
  let!(:promotion_one) { create(:promotion, :lbc_giftcard, voucher_bucket: bucket_one) }
  let!(:promotion_two) { create(:promotion, :lbc_giftcard, voucher_bucket: bucket_two) }
  let!(:coupon_one) { create_coupon(promotion: promotion_one) }
  let!(:coupon_two) { create_coupon(promotion: promotion_one) }
  let!(:coupon_three) { create_coupon(promotion: promotion_two) }
  let!(:coupon_four) { create_coupon(promotion: promotion_one) }
  let!(:user) { create(:user) }
  let!(:voucher_one) do
    create(:voucher, code: "ABCD123", expired_at: 2.months.ago, bucket: bucket_two)
  end
  let!(:voucher_two) do
    create(:voucher, code: "ASDA124", expired_at: 4.months.ago, bucket: bucket_one)
  end
  let!(:voucher_three) do
    create(:voucher, code: "ASDA124", expired_at: 4.months.ago, bucket: bucket_two)
  end
  let!(:voucher_four) do
    create(:voucher, code: "7UIA509", expired_at: 3.months.ago, bucket: bucket_one)
  end
  let!(:voucher_five) do
    create(:voucher, code: "U8MKL33", expired_at: 2.months.ago, bucket: bucket_one)
  end
  let!(:voucher_six) do
    create(:voucher, code: "Y44XD1M", expired_at: 4.months.ago, bucket: bucket_two)
  end
  let!(:voucher_seven) do
    create(:voucher, code: "Y44XD1M", expired_at: 4.months.ago, bucket: bucket_one)
  end
  let!(:unrelated_bucket) { create(:voucher_bucket) }
  let!(:unrelated_promotion) { create(:promotion, :coupon_code) }
  let!(:unrelated_voucher) { create(:voucher, bucket: unrelated_bucket) }
  let!(:unrelated_coupon) { create_coupon(promotion: unrelated_promotion) }
  let!(:unrelated_order) { create(:order, cupon: unrelated_coupon, user:, voucher: unrelated_voucher) }
  let!(:order_one) { create(:order, voucher: voucher_two, cupon: coupon_two, user:) }
  let!(:order_two) { create(:order, voucher: voucher_five, cupon: coupon_four, user:) }
  let!(:order_three) { create(:order, voucher: voucher_three, cupon: coupon_two, user:) }
  let!(:order_four) { create(:order, voucher: voucher_six, cupon: coupon_three, user:) }
  let!(:now) { DateTime.current }
  let(:service) { described_class.new(file_path: file.path, resource_id: promotion_one.id) }

  it "makes the coupon codes available" do
    service.call

    expect(voucher_one.reload.expired_at).to be < now
    expect(voucher_two.reload.expired_at).to be >= now
    expect(voucher_three.reload.expired_at).to be < now
    expect(voucher_four.reload.expired_at).to be >= now
    expect(voucher_five.reload.expired_at).to be >= now
    expect(voucher_six.reload.expired_at).to be < now
    expect(voucher_seven.reload.expired_at).to be >= now
    expect(Order.count).to eq(3)
    expect(Order.where(id: order_one.id)).not_to exist
    expect(Order.where(id: order_two.id)).not_to exist
    expect(Order.where(id: order_three.id)).to exist
    expect(Order.where(id: order_four.id)).to exist
    expect(Order.where(id: unrelated_order.id)).to exist
  end

  context "when importing a file without headers" do
    let(:file) { Rails.root.join("spec/fixtures/coupon_codes_make_available_without_headers.csv").open }

    it "raises error" do
      expect { service.call }.to raise_error CsvInvalidHeadersError
    end
  end

  context "when importing a file with errors" do
    let(:file) { Rails.root.join("spec/fixtures/coupon_codes_make_available_with_error.csv").open }
    let(:file_with_error_after_import) do
      Rails.root.join("spec/fixtures/coupon_codes_make_available_with_error_after_import.csv").read
    end
    let!(:order_one_2) { create(:order, number: "AAA987", cupon: coupon_one, user:, voucher: voucher_two) }

    it "makes the coupon codes without error available" do
      service.call

      expect(voucher_one.reload.expired_at).to be < now
      expect(voucher_two.reload.expired_at).to be >= now
      expect(voucher_three.reload.expired_at).to be < now
      expect(voucher_four.reload.expired_at).to be >= now
      expect(voucher_five.reload.expired_at).to be >= now
      expect(voucher_six.reload.expired_at).to be < now
      expect(voucher_seven.reload.expired_at).to be >= now
      expect(service.error_messages.size).to eq(1)
      expect(service.error_messages[0]).to include("Linha 5")
      expect(service.vouchers_with_error_csv).to eq(file_with_error_after_import)
    end
  end

  def create_coupon(promotion:)
    create(
      :cupon,
      :lbc_giftcard,
      :with_branch,
      promotion:,
      start_hour: 1.hour.ago,
      end_hour: 1.hour.from_now
    )
  end
end
