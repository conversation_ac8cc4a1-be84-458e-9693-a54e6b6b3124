require "rails_helper"

RSpec.describe Hinova::UsersImportService do
  let!(:users) do
    (1..4).collect do
      {
        name: FFaker::Name.name,
        email: FFaker::Internet.email,
        cpf: FFaker::IdentificationBR.cpf
      }
    end
  end

  let!(:group) { create(:authorized_user_group, business:) }
  let!(:hinova_token) { create(:hinova_token, business:, authorized_user_group: group) }
  let!(:business) { create(:business) }

  let(:service) do
    described_class.new(business:, hinova_token: business.hinova_tokens.first, batch_size: 4)
  end
  let(:template_mock) { double("template_mock", to_json: "{}") }

  before do
    allow(Hinova::Users).to receive_message_chain(:new, :list_all).and_return(users)
    allow(Slack::MessageTemplate::Simple).to receive(:new).and_return(template_mock)
    allow(Slack::Api).to receive(:send_message)
      .with(template_mock.to_json)
  end

  describe "#call" do
    context "when has users" do
      describe "#inactivate_users" do
        let!(:authorized_users_to_inactivate) do
          create_list(:authorized_user, 4, :with_user, synced: true, active: true, business:)
          create_list(:authorized_user, 4, :with_user, authorized_user_group: group, synced: true, active: true, business:)
        end

        it "must inactivate AuthorizedUser on same group based on imported users" do
          expect { service.call }
            .to change(business.authorized_users.where(authorized_user_group: group, synced: false, active: false), :count).by(4)
            .and change(business.users.where(active: false), :count).by(4)
        end

        context "when authorized user is inactive" do
          before do
            AuthorizedUser.where(authorized_user_group: group, synced: true, active: true)
              .first.update(synced: false, active: false)
          end

          it "must ignore on inactivation" do
            expect { service.call }.to change(AuthorizedUser.where(authorized_user_group: group, synced: false, active: false), :count).by(3)
          end
        end
      end

      describe "#activate_users" do
        context "when cpf already exist on project" do
          context "when existing authorized user is active on another group managed by a hinova token" do
            let(:sub_business) { create(:business, main_business: business) }
            let(:sub_authorized_user_group) { create(:authorized_user_group, business: sub_business) }
            let!(:another_hinova_token) { create(:hinova_token, business: sub_business, authorized_user_group: sub_authorized_user_group) }
            let!(:authorized_user) { create(:authorized_user, cpf: users[0][:cpf], business: sub_business, authorized_user_group: sub_authorized_user_group) }

            it "must not update this authorized user" do
              expect { service.call }.not_to change { authorized_user }
            end
          end

          context "when authorized user is not active on another group managed by a hinova token" do
            let(:sub_business) { create(:business, main_business: business) }
            let(:another_authorized_user_group) { create(:authorized_user_group, business: sub_business) }
            let!(:auth_user_on_another_sub_business) { create(:authorized_user, cpf: users[0][:cpf], business: sub_business) }
            let!(:auth_user_on_another_group) { create(:authorized_user, cpf: users[1][:cpf], business: sub_business, authorized_user_group: another_authorized_user_group, active: false) }
            let!(:auth_user_on_without_group) { create(:authorized_user, cpf: users[2][:cpf], business: sub_business, active: false) }
            let!(:inactive_auth_user) { create(:authorized_user, cpf: users[3][:cpf], business: sub_business, active: false) }

            it "must update authorized user and user" do
              service.call

              auth_user_on_another_sub_business.reload
              expect(auth_user_on_another_sub_business.business).to eq(business)
              expect(auth_user_on_another_sub_business.name).to eq(Utils::NameNormalizer.call(users[0][:name]))
              expect(auth_user_on_another_sub_business.active).to eq(true)

              auth_user_on_another_group.reload
              expect(auth_user_on_another_group.authorized_user_group).to eq(group)
              expect(auth_user_on_another_group.name).to eq(Utils::NameNormalizer.call(users[1][:name]))
              expect(auth_user_on_another_group.active).to eq(true)

              auth_user_on_without_group.reload
              expect(auth_user_on_without_group.authorized_user_group).to eq(group)
              expect(auth_user_on_without_group.name).to eq(Utils::NameNormalizer.call(users[2][:name]))
              expect(auth_user_on_without_group.active).to eq(true)

              inactive_auth_user.reload
              expect(inactive_auth_user.active).to eq(true)
              expect(inactive_auth_user.name).to eq(Utils::NameNormalizer.call(users[3][:name]))
            end
          end
        end

        context "when cpf does not exist on project" do
        end
      end
    end

    context "when has not users" do
      let(:users) { [] }

      it "notify in slack an integration error" do
        service.call
        expect(Slack::Api).to have_received(:send_message).once
      end
    end
  end
end
