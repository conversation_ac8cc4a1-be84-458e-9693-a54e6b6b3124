require "rails_helper"

RSpec.describe AuthorizedUser::ExportCsvService do
  let!(:business) { create(:business) }

  let!(:authorized_user_one) do
    create(:authorized_user, business:)
  end
  let!(:authorized_user_two) do
    create(:authorized_user, business:)
  end

  let!(:authorized_user_with_deleted_user) do
    create(:authorized_user, business:)
  end

  let!(:deleted_user) do
    create(:user, business:, authorized_user: authorized_user_with_deleted_user, deleted: true)
  end

  let!(:authorized_user_export_file) do
    create(:authorized_user_export_file, business:)
  end

  let!(:csv_builder_service_mock) do
    double(:csv_builder_service_mock, add_line: nil)
  end

  let!(:file_mock) { double(:file_mock) }

  before do
    allow(AuthorizedUser::CsvBuilderService).to receive(:new)
      .and_return(csv_builder_service_mock)

    allow(csv_builder_service_mock).to receive(:generated_file)
      .and_return(file_mock)
  end

  describe "#call" do
    it "generate csv and upload to bucket" do
      described_class
        .new(authorized_user_export_file:)
        .call

      expect(csv_builder_service_mock).to have_received(:add_line).with(authorized_user_one)
      expect(csv_builder_service_mock).to have_received(:add_line).with(authorized_user_two)
      expect(csv_builder_service_mock).not_to have_received(:add_line).with(authorized_user_with_deleted_user)

      expect(csv_builder_service_mock).to have_received(:generated_file)
      expect(authorized_user_export_file.processed).to eq(true)
    end
  end
end
