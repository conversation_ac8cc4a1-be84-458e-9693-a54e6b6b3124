require "rails_helper"

describe MkSolution::UserImportService do
  let!(:business) { create(:business) }
  let!(:authorized_user_group) { create(:authorized_user_group, business:) }
  let!(:mk_solution_token) { create(:mk_solution_token, business:, authorized_user_group:) }
  let(:mk_solution_plan) { create(:mk_solution_plan, mk_solution_token:) }
  let(:mk_client_klass) { double(MkSolution::Client, active_users: return_value) }
  let(:empty_mk_client_klass) { double(MkSolution::Client, active_users: []) }
  let(:new_user_return_value) { MkSolution::User.new("019.771.841-89", "new name", FFaker::Internet.email) }
  let(:return_value) {
    (0..1).inject([new_user_return_value]) do |arr|
      arr << MkSolution::User.new(
        FFaker::IdentificationBR.cpf,
        FFaker::Name.name,
        FFaker::Internet.email
      )
    end
  }
  let(:template_mock) { double("template_mock", to_json: "{}") }

  before do
    allow(MkSolution::Client).to receive(:new).and_return(mk_client_klass)
    allow(Slack::MessageTemplate::Simple).to receive(:new).and_return(template_mock)
    allow(Slack::Api).to receive(:send_message)
      .with(template_mock.to_json)
  end

  describe "#call" do
    context "when has authorized users" do
      context "when user is not in the authorized user table" do
        it "creates a new user" do
          expect do
            described_class.call(business:)
          end.to change(AuthorizedUser.where(cpf: "***********"), :count).by(1)
        end
      end

      context "when user is active in the authorized user table" do
        context "when authorized user belongs to mk group" do
          let!(:authorized_user) do
            create(:authorized_user, cpf: "***********", authorized_user_group:, business:, active: true, name: "old name")
          end

          it "does not change active attribute of the user but updates the user" do
            described_class.call(business:)

            authorized_user.reload
            expect(authorized_user.active).to eq(true)
            expect(authorized_user.authorized_user_group).to eq(authorized_user_group)
            expect(authorized_user.name).to eq("New Name")
          end
        end

        context "when authorized user does not belong to mk group" do
          let!(:authorized_user) do
            create(:authorized_user, cpf: "***********", business:, active: true, name: "old name")
          end

          it "must not update the user" do
            expect do
              described_class.call(business:)

              authorized_user.reload
            end.not_to change { authorized_user }
          end
        end

        context "when users cpf are not included on the sync list anymore" do
          context "when user still belongs to group" do
            let!(:authorized_user) do
              create(:authorized_user, authorized_user_group:, business:, active: true, name: "old name")
            end

            it "must inactivate user" do
              described_class.call(business:)

              authorized_user.reload
              expect(authorized_user.active).to eq(false)
              expect(authorized_user.authorized_user_group).to eq(authorized_user_group)
            end
          end
        end
      end

      context "when user is inactive in the authorized user table" do
        let!(:authorized_user) do
          create(:authorized_user, cpf: "***********", authorized_user_group:, business:, active: false, name: "old name")
        end

        context "when user still belongs to group" do
          it "activates the user when data is in the payload and updates the user" do
            described_class.call(business:)

            authorized_user.reload
            expect(authorized_user.active).to eq(true)
            expect(authorized_user.authorized_user_group).to eq(authorized_user_group)
            expect(authorized_user.name).to eq("New Name")
          end
        end

        context "when user does not belong to mk group anymore" do
          let!(:authorized_user) do
            create(:authorized_user, cpf: "***********", business:, active: false, name: "old name")
          end

          it "must not update user" do
            described_class.call(business:)

            authorized_user.reload
            expect(authorized_user.active).to eq(false)
            expect(authorized_user.authorized_user_group).to eq(nil)
            expect(authorized_user.name).to eq("Old Name")
          end
        end

        context "and payload is empty" do
          before { allow(MkSolution::Client).to receive(:new).and_return(empty_mk_client_klass) }

          it "does not change active attribute of the user and does not update the user" do
            described_class.call(business:)

            authorized_user.reload
            expect(authorized_user.active).to eq(false)
            expect(authorized_user.name).to eq("Old Name")
          end
        end
      end
    end

    context "when has not authorized users" do
      context "and payload is empty" do
        before { allow(MkSolution::Client).to receive(:new).and_return(empty_mk_client_klass) }

        it "disables the user when it is not in the payload" do
          described_class.call(business:)
          expect(Slack::Api).to have_received(:send_message).once
        end
      end
    end
  end
end
