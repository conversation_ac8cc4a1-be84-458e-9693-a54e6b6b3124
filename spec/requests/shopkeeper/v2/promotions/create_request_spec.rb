# frozen_string_literal: true

require "rails_helper"
require "sidekiq/testing"

RSpec.describe Shopkeeper::V2::PromotionsController, type: :request do
  include ActiveSupport::Testing::TimeHelpers

  let!(:shopkeeper) { create(:shopkeeper, :admin_lecupon) }
  let!(:organization) { create(:organization) }
  let!(:organization_two) { create(:organization) }
  let!(:org_coupon_code) do
    create(:organization_promotion_provider,
      organization:,
      promotion_provider: Promotion::Provider::COUPON_CODE)
  end
  let!(:org_two_whatsapp) do
    create(:organization_promotion_provider,
      organization: organization_two,
      promotion_provider: Promotion::Provider::WHATSAPP)
  end
  let!(:branch_one) { create(:branch, organization:) }
  let!(:branch_two) { create(:branch, organization:) }
  let!(:branch_three) { create(:branch, organization:) }
  let!(:branch_four) { create(:branch, organization:) }
  let(:category_one) { create(:category, :promotion_category) }
  let(:category_two) { create(:category, :promotion_category) }
  let(:headers) { {"X-Shopkeeper-Email": shopkeeper.email, "X-Shopkeeper-Token": shopkeeper.authentication_token} }
  let(:physical_promotion_params) do
    {
      provider: Promotion::Provider::COUPON_CODE,
      redeem_type: Enums::PromotionRedeemType::DISCOUNT,
      description: "description",
      title: "title",
      discount: 10,
      discount_type: "fixed",
      start_date: DateTime.current,
      end_date: 1.year.from_now,
      start_hour: "09:00",
      end_hour: "18:00",
      days_of_week: %w[0 1 2 3 4 5 6],
      picture: "data:image/png;base64,#{Base64.encode64(Rails.root.join("spec/fixtures/samplefile.png").read)}",
      quantity: 10,
      infinity: false,
      rules: "Não tem regras",
      cumulative: false,
      redeems_per_cpf: 999,
      contract_custom_text: "Rede de Parcerias Qualquer",
      frequency_in_days: 1,
      category_ids: [category_one.id, category_two.id],
      sync_cashback_with_organization: false
    }
  end
  let(:online_promotion_params) do
    {
      provider: Promotion::Provider::WHATSAPP,
      redeem_type: Enums::PromotionRedeemType::DISCOUNT,
      url: FFaker::Internet.http_url,
      description: "description",
      title: "title",
      discount: 10,
      discount_type: "percent",
      start_date: DateTime.current,
      end_date: 1.year.from_now,
      start_hour: "00:00:00",
      end_hour: "23:59:59",
      days_of_week: %w[0 1 2 3 4 5 6],
      picture: "data:image/png;base64,#{Base64.encode64(Rails.root.join("spec/fixtures/samplefile.png").read)}",
      quantity: 10,
      rules: "Não tem regras",
      cumulative: false,
      redeems_per_cpf: 999,
      frequency_in_days: 1,
      category_ids: [category_one.id, category_two.id]
    }
  end
  let(:response_hash) { JSON.parse(response.body) }

  describe "POST /shopkeeper/v2/promotions" do
    let!(:shopkeeper) { create(:shopkeeper, :admin_lecupon) }
    let!(:shopkeeper_promotion_provider_one) do
      create(:shopkeeper_promotion_provider,
        shopkeeper:,
        promotion_provider: Promotion::Provider::COUPON_CODE)
    end
    let!(:shopkeeper_promotion_provider_two) do
      create(:shopkeeper_promotion_provider,
        shopkeeper:,
        promotion_provider: Promotion::Provider::WHATSAPP)
    end
    let(:serializer_keys) do
      %w[id description contract_custom_text provider rules cashback_rules code
        url discount discount_type quantity start_date end_date start_hour end_hour days_of_week
        cumulative only_debit only_physical redeems_per_cpf one_for_cpf one_per_day infinity
        price average_ticket frequency_in_days integration_code integration_partner
        status redeem_type business organization picture_small_url picture_large_url title dynamic_voucher
        closed_interval cashback_value cashback_type sync_cashback_with_organization redeemed_count usage_instruction]
    end

    it "creates a physical promotion" do
      post("/shopkeeper/v2/promotions", params: physical_promotion_params, headers:)

      expect(response).to be_successful
      response_hash = JSON.parse(response.body)
      expect(response_hash.keys).to match_array(serializer_keys)
      expect(response_hash["discount"]).to eq(10)
      expect(Promotion.count).to eq(1)
      expect(Cupon.count).to eq(0)
      promotion = Promotion.first
      expect(promotion.quantity).to eq(10)
      expect(promotion.infinity).to eq(false)
      expect(promotion.only_physical).to eq(true)
      expect(promotion.sync_cashback_with_organization).to eq(false)

      expect(promotion.contract_custom_text).to eq("Rede de Parcerias Qualquer")
    end

    it "creates a physical promotion" do
      create(:shopkeeper_promotion_provider, shopkeeper:, promotion_provider: "dynamic")
      physical_promotion_params[:provider] = "dynamic"

      post("/shopkeeper/v2/promotions", params: physical_promotion_params, headers:)

      expect(response).to be_successful
      promotion = Promotion.first
      expect(promotion.dynamic_voucher).to eq(true)
    end

    it "does not create the physical promotion with a required param missing" do
      post("/shopkeeper/v2/promotions", params: physical_promotion_params.except(:title), headers:)

      expect(response).to have_http_status(:precondition_failed)
    end

    it "creates an online promotion" do
      post("/shopkeeper/v2/promotions", params: online_promotion_params, headers:)

      expect(response).to be_successful
      expect(Promotion.count).to eq(1)
      expect(Cupon.count).to eq(0)
      promotion = Promotion.first
      expect(promotion.quantity).to eq(10)
      expect(promotion.infinity).to eq(false)
      expect(promotion.only_physical).to eq(false)
      expect(promotion.contract_custom_text).to eq(nil)
      expect(promotion.source).to eq(Promotion::Source::SHOPKEEPER)
    end

    it "creates a promotion with nil integration partner" do
      online_promotion_params[:integration_partner] = nil
      post("/shopkeeper/v2/promotions", params: online_promotion_params, headers:)

      expect(response).to be_successful
    end

    it "does not create the online promotion with a required param missing" do
      post("/shopkeeper/v2/promotions", params: online_promotion_params.except(:url), headers:)

      expect(response).to have_http_status(:precondition_failed)
    end

    context "with invalid url in online" do
      it "returns error" do
        post("/shopkeeper/v2/promotions", params: online_promotion_params.merge(url: "www.google.com"), headers:)

        expect(response).to have_http_status(:unprocessable_entity)
      end
    end

    context "when cashback is filled and user is admin lecupon" do
      it "renders successful and creates promotion" do
        physical_promotion_params[:cashback_value] = 10
        physical_promotion_params[:cashback_type] = "percent"

        expect { post "/shopkeeper/v2/promotions", params: physical_promotion_params, headers: }
          .to change(Promotion, :count).by(1)

        expect(response).to be_successful
        expect(response_hash.keys).to match_array(serializer_keys)
        expect(response_hash["discount"]).to eq(10)
      end
    end

    context "when cashback is filled and user is not admin lecupon" do
      let!(:shopkeeper) { create(:shopkeeper, :admin_shopkeeper) }

      it "renders error and does not create promotion" do
        physical_promotion_params[:cashback_value] = 10
        physical_promotion_params[:cashback_type] = "percent"

        expect { post "/shopkeeper/v2/promotions", params: physical_promotion_params, headers: }
          .to change(Promotion, :count).by(0)

        expect(response).to be_unprocessable
      end
    end
  end

  describe "POST /shopkeeper/v2/promotions/:id/branches" do
    before do
      post("/shopkeeper/v2/promotions", params: physical_promotion_params, headers:)
      @promotion = Promotion.last

      stub_request(:any, /(.*)/).to_return(
        status: 200,
        body: Rails.root.join("spec/fixtures/samplefile.png").read
      )
    end

    it "creates the branch coupons" do
      Sidekiq::Testing.inline! do
        post("/shopkeeper/v2/promotions/#{@promotion.id}/branches",
          params: {branch_ids: [branch_one.id, branch_three.id]},
          headers:)

        expect(response).to be_successful
        expect(Cupon.count).to eq(2)
        expect(Promotion.count).to eq(1)
        expect(Cupon.first.description).to eq(@promotion.description)
        expect(Cupon.first.c_type).to eq(Enums::CouponType::FIXED)
        expect(Cupon.first.quantity).to eq(10)
        expect(Cupon.first.infinity).to eq(false)
        expect(Cupon.first.only_physical).to eq(true)
        expect(Cupon.distinct.count(:batch)).to eq(1)
      end
    end

    context "when the promotion is online" do
      before do
        Cupon.delete_all
        PromotionCategory.delete_all
        Promotion.delete_all

        post("/shopkeeper/v2/promotions", params: online_promotion_params, headers:)
        @promotion = Promotion.last
      end

      it "returns error" do
        post("/shopkeeper/v2/promotions/#{@promotion.id}/branches",
          params: {branch_ids: [branch_one.id, branch_three.id]},
          headers:)

        expect(response).not_to be_successful
      end
    end

    context "when the branches organization does not accept the provider" do
      before do
        OrganizationPromotionProvider.where(organization:).delete_all
      end

      it "returns error" do
        post("/shopkeeper/v2/promotions/#{@promotion.id}/branches",
          params: {branch_ids: [branch_one.id, branch_three.id]},
          headers:)

        expect(response).not_to be_successful
      end
    end

    context "when adding branches of multiple organizations" do
      let!(:organization_three) { create(:organization) }

      before do
        branch_three.update!(organization: organization_three)
      end

      it "returns error" do
        post("/shopkeeper/v2/promotions/#{@promotion.id}/branches",
          params: {branch_ids: [branch_one.id, branch_three.id]},
          headers:)

        expect(response).not_to be_successful
      end
    end

    context "when adding all branches of an organization" do
      it "creates the coupons for all branches of the organization" do
        Sidekiq::Testing.inline! do
          post("/shopkeeper/v2/promotions/#{@promotion.id}/branches",
            params: {all_branches: true, organization_id: organization.id},
            headers:)

          expect(response).to be_successful
          expect(organization.cupons.active.ids).to match_array(@promotion.reload.cupons.ids)
          expect(organization.cupons.count).to eq(organization.branches.count)
        end
      end

      context "when adding all branches twice and coupons inactive" do
        before do
          Sidekiq::Testing.inline! do
            post "/shopkeeper/v2/promotions/#{@promotion.id}/branches",
              params: {all_branches: true, organization_id: organization.id},
              headers:
          end

          @promotion.cupons.update_all(active: false)
        end

        it "returns success and reactivates coupons" do
          Sidekiq::Testing.inline! do
            post("/shopkeeper/v2/promotions/#{@promotion.id}/branches",
              params: {all_branches: true, organization_id: organization.id},
              headers:)

            expect(response).to be_successful
            expect(@promotion.cupons).to all be_active
            expect(organization.cupons.ids).to match_array(@promotion.reload.cupons.ids)
            expect(organization.cupons.count).to eq(organization.branches.count)
          end
        end
      end

      context "when adding all branches of the organization for multiple promotions" do
        before do
          post("/shopkeeper/v2/promotions", params: physical_promotion_params, headers:)
          @promotion2 = Promotion.last
        end

        it "creates the coupons for all branches of the organization for both promotions" do
          Sidekiq::Testing.inline! do
            post("/shopkeeper/v2/promotions/#{@promotion.id}/branches",
              params: {all_branches: true, organization_id: organization.id},
              headers:)

            expect(response).to be_successful
            expect(organization.cupons.where(promotion: @promotion).ids).to match_array(@promotion.reload.cupons.ids)

            post("/shopkeeper/v2/promotions/#{@promotion2.id}/branches",
              params: {all_branches: true, organization_id: organization.id},
              headers:)

            expect(response).to be_successful
            expect(organization.cupons.where(promotion: @promotion2).ids).to match_array(@promotion2.reload.cupons.ids)
          end
        end
      end
    end
  end

  describe "POST /shopkeeper/v2/promotions/:id/organization" do
    before do
      post("/shopkeeper/v2/promotions", params: online_promotion_params, headers:)
      @promotion = Promotion.last

      stub_request(:any, /(.*)/).to_return(
        status: 200,
        body: Rails.root.join("spec/fixtures/samplefile.png").read
      )
    end

    it "creates the organization coupons" do
      post("/shopkeeper/v2/promotions/#{@promotion.id}/organization",
        params: {organization_id: organization_two.id},
        headers:)

      expect(response).to be_successful
      expect(Cupon.count).to eq(1)
      expect(Promotion.count).to eq(1)
      expect(Cupon.first.description).to eq(@promotion.description)
      expect(Cupon.first.c_type).to eq(Enums::CouponType::ONLINE)
      expect(Cupon.first.quantity).to eq(10)
      expect(Cupon.first.infinity).to eq(false)
      expect(Cupon.first.only_physical).to eq(false)
      expect(Cupon.distinct.count(:batch)).to eq(1)
    end

    context "with no organization_id as param" do
      it "returns error" do
        post("/shopkeeper/v2/promotions/#{@promotion.id}/organization", headers:)

        expect(response).to be_unprocessable
      end
    end

    context "when the promotion has branches" do
      before do
        Cupon.delete_all
        PromotionCategory.delete_all
        Promotion.delete_all

        post("/shopkeeper/v2/promotions", params: physical_promotion_params, headers:)
        @promotion = Promotion.last

        Sidekiq::Testing.inline! do
          post "/shopkeeper/v2/promotions/#{@promotion.id}/branches",
            params: {branch_ids: [branch_one.id, branch_three.id]},
            headers:
        end
      end

      it "returns error" do
        post("/shopkeeper/v2/promotions/#{@promotion.id}/organization",
          params: {organization_id: organization_two.id},
          headers:)

        expect(response).not_to be_successful
      end
    end

    context "when the organization does not accept the provider" do
      before do
        OrganizationPromotionProvider.delete_all
      end

      it "returns error" do
        post("/shopkeeper/v2/promotions/#{@promotion.id}/organization",
          params: {organization_id: organization_two.id},
          headers:)

        expect(response).not_to be_successful
      end
    end

    context "when changing organization" do
      let!(:org_one_whatsapp) do
        create(:organization_promotion_provider,
          organization:,
          promotion_provider: Promotion::Provider::WHATSAPP)
      end

      it "returns error" do
        post("/shopkeeper/v2/promotions/#{@promotion.id}/organization",
          params: {organization_id: organization_two.id},
          headers:)

        expect(response).to be_successful
        @promotion.reload
        expect(@promotion.online_organization).to eq(organization_two)
        online_coupons = @promotion.cupons.active.redeemable.online
        expect(online_coupons.length).to eq(1)
        expect(online_coupons.first.organization).to eq(organization_two)
        expect(online_coupons.first.organization_id).to eq(organization_two.id)

        params = {organization_id: organization.id}
        expect do
          post("/shopkeeper/v2/promotions/#{@promotion.id}/organization", params:, headers:)
          @promotion.reload
        end.not_to change(@promotion, :attributes)

        expect(response).to be_unprocessable
        expect(response_hash["error"]).to eq("A Organização não pode ser alterada")
      end
    end
  end

  describe "PATCH /shopkeeper/v2/promotions/:id" do
    let(:params) do
      physical_promotion_params.merge(description: "new description", picture: nil, discount: 20)
    end

    context "when the promotion has coupons" do
      let!(:shopkeeper) { create(:shopkeeper, :admin_lecupon) }

      before do
        post("/shopkeeper/v2/promotions", params: physical_promotion_params, headers:)
        @promotion = Promotion.last
      end

      before do
        stub_request(:any, /(.*)/).to_return(
          status: 200,
          body: Rails.root.join("spec/fixtures/samplefile.png").read
        )

        Sidekiq::Testing.inline! do
          post "/shopkeeper/v2/promotions/#{@promotion.id}/branches",
            params: {branch_ids: [branch_one.id, branch_three.id]},
            headers:
        end
      end

      it "updates the promotion and the coupons" do
        Sidekiq::Testing.inline! do
          params =
            physical_promotion_params
              .merge(description: "new description", picture: nil, discount: 20, discount_type: "fixed")
          patch("/shopkeeper/v2/promotions/#{@promotion.id}", params:, headers:)

          expect(response).to be_successful
          @promotion.reload
          expect(@promotion.description).to eq("new description")
          expect(@promotion.discount_type).to eq("fixed")
          first_cupon = Cupon.first
          expect(first_cupon.discount_type).to eq("fixed")
          expect(@promotion.picture).not_to be_present
          expect(@promotion.discount_value).to eq(20)
        end
      end

      context "with invalid params" do
        it "returns error" do
          patch("/shopkeeper/v2/promotions/#{@promotion.id}", params: params.merge(quantity: "NaN"), headers:)

          expect(response).to have_http_status(:unprocessable_entity)
        end
      end

      context "when trying to update quantity to less than used" do
        let(:promotion) { create(:promotion, :coupon_code, redeemed_count: 10, quantity: 10) }
        let(:params) do
          physical_promotion_params.merge(quantity: 9)
        end

        it "must not update promotion" do
          Sidekiq::Testing.inline! do
            patch("/shopkeeper/v2/promotions/#{promotion.id}", params:, headers:)
            expect(response).to be_unprocessable
            expect(JSON.parse(response.body)["error"]).to be_present
            expect(JSON.parse(response.body)["error"]).to include(I18n.t("errors.messages.greater_than_or_equal_to")
                                                                      .gsub("%{count}", "10"))
              .and include("Quantity")
          end
        end
      end
    end

    context "when the promotion does not have coupons" do
      let!(:shopkeeper) { create(:shopkeeper, :admin_lecupon) }

      before do
        post("/shopkeeper/v2/promotions", params: physical_promotion_params, headers:)
        @promotion = Promotion.last
      end

      it "updates the promotion" do
        Sidekiq::Testing.inline! do
          patch("/shopkeeper/v2/promotions/#{@promotion.id}", params:, headers:)

          expect(response).to be_successful

          @promotion.reload
          expect(@promotion.description).to eq("new description")
          expect(@promotion.picture).not_to be_present
          expect(Cupon.count).to eq(0)
        end
      end
    end

    context "when the integration partner changes to nil" do
      let!(:shopkeeper) { create(:shopkeeper, :admin_lecupon) }
      let!(:promotion) { create(:promotion, :online, integration_partner: "awin", integration_code: "123") }

      before do
        online_promotion_params[:provider] = "online"
        online_promotion_params[:integration_partner] = nil
      end

      it "changes the integration partner to nil" do
        Sidekiq::Testing.inline! do
          expect do
            patch("/shopkeeper/v2/promotions/#{promotion.id}", params: online_promotion_params, headers:)
            promotion.reload
          end.to change(promotion, :integration_partner).from("awin").to(nil)
        end

        expect(response).to be_successful
      end
    end

    context "when changing promotion provider" do
      let!(:shopkeeper) { create(:shopkeeper, :admin_lecupon) }

      before do
        post("/shopkeeper/v2/promotions", params: physical_promotion_params, headers:)
        @promotion = Promotion.last
      end

      it "returns error" do
        patch("/shopkeeper/v2/promotions/#{@promotion.id}",
          params: physical_promotion_params.merge(provider: Promotion::Provider::CPF),
          headers:)

        expect(response).to be_unprocessable
      end
    end

    context "with a required param missing" do
      let!(:shopkeeper) { create(:shopkeeper, :admin_lecupon) }

      before do
        post("/shopkeeper/v2/promotions", params: physical_promotion_params, headers:)
        @promotion = Promotion.last
      end

      it "does not update the promotion" do
        patch("/shopkeeper/v2/promotions/#{@promotion.id}",
          params: physical_promotion_params.except(:discount),
          headers:)

        expect(response).to have_http_status(:precondition_failed)
        expect(response_hash["error"]).to eq("A requisição falhou devido a ausência de parâmetro: discount")
      end
    end

    context "when cashback is filled and user is admin lecupon" do
      let!(:shopkeeper) { create(:shopkeeper, :admin_lecupon) }

      before do
        physical_promotion_params.merge!(cashback_type: "percent", cashback_value: 0)
        post("/shopkeeper/v2/promotions", params: physical_promotion_params, headers:)
        @promotion = Promotion.last
      end

      it "renders successful and updates promotion" do
        params[:cashback_value] = 10
        params[:cashback_type] = "fixed"

        Sidekiq::Testing.inline! do
          expect do
            patch("/shopkeeper/v2/promotions/#{@promotion.id}", params:, headers:)
            @promotion.reload
          end.to change(@promotion, :cashback_value).from(0.0).to(10.0)
            .and change(@promotion, :cashback_type).from("percent").to("fixed")
        end

        expect(response).to be_successful
      end
    end

    context "when cashback is filled and user is not admin lecupon" do
      let!(:shopkeeper) { create(:shopkeeper, :admin_shopkeeper) }
      let!(:shopkeeper_promotion_provider_one) do
        create(:shopkeeper_promotion_provider,
          shopkeeper:,
          promotion_provider: Promotion::Provider::COUPON_CODE)
      end
      let!(:shopkeeper_promotion_provider_two) do
        create(:shopkeeper_promotion_provider,
          shopkeeper:,
          promotion_provider: Promotion::Provider::WHATSAPP)
      end

      before do
        shopkeeper.branches = Branch.all

        post("/shopkeeper/v2/promotions", params: physical_promotion_params, headers:)
        @promotion = Promotion.last
        @promotion.update!(cashback_type: "percent", cashback_value: 20)
      end

      it "renders error and does not update promotion" do
        params[:cashback_value] = 10
        params[:cashback_type] = "percent"

        Sidekiq::Testing.inline! do
          expect do
            patch("/shopkeeper/v2/promotions/#{@promotion.id}", params:, headers:)
            @promotion.reload
          end.to not_change(@promotion, :cashback_type)
            .and not_change(@promotion, :cashback_value)
        end

        expect(response).to be_unprocessable
      end
    end
  end

  describe "GET /shopkeeper/v2/promotions/:id/branches" do
    let(:serializer_keys) do
      %w[id name taxpayer_number description telephone zipcode
        full_address address number complement neighborhood city_id
        qrcode opening_time closing_time lecupon_pay
        contact_name contact_telephone contact_email active organization qrcode_string
        lat lng]
    end

    before do
      post("/shopkeeper/v2/promotions", params: physical_promotion_params, headers:)
      @promotion = Promotion.last

      stub_request(:any, /(.*)/).to_return(
        status: 200,
        body: Rails.root.join("spec/fixtures/samplefile.png").read
      )

      Sidekiq::Testing.inline! do
        post "/shopkeeper/v2/promotions/#{@promotion.id}/branches",
          params: {branch_ids: [branch_one.id, branch_three.id]},
          headers:
      end
    end

    it "returns the branches of the promotion" do
      get("/shopkeeper/v2/promotions/#{@promotion.id}/branches", headers:)

      expect(response).to be_successful
      expect(response_hash.pluck("id")).to match_array([branch_one.id, branch_three.id])
      expect(response_hash[0].keys).to match_array(serializer_keys)
    end
  end

  describe "DELETE /shopkeeper/v2/promotions/:id/branches" do
    before do
      post("/shopkeeper/v2/promotions", params: physical_promotion_params, headers:)
      @promotion = Promotion.last

      stub_request(:any, /(.*)/).to_return(
        status: 200,
        body: Rails.root.join("spec/fixtures/samplefile.png").read
      )

      Sidekiq::Testing.inline! do
        post "/shopkeeper/v2/promotions/#{@promotion.id}/branches",
          params: {branch_ids: [branch_one.id, branch_three.id]},
          headers:
      end
    end

    it "deletes the coupons" do
      delete("/shopkeeper/v2/promotions/#{@promotion.id}/branches",
        params: {branch_ids: [branch_one.id, branch_three.id]},
        headers:)

      expect(response).to be_successful
      expect(Cupon.active.count).to eq(0)
    end
  end

  describe "DELETE /shopkeeper/v2/promotions/:id/organization" do
    before do
      post("/shopkeeper/v2/promotions", params: online_promotion_params, headers:)
      @promotion = Promotion.last

      stub_request(:any, /(.*)/).to_return(
        status: 200,
        body: Rails.root.join("spec/fixtures/samplefile.png").read
      )

      post "/shopkeeper/v2/promotions/#{@promotion.id}/organization",
        params: {organization_id: organization_two.id},
        headers:
    end

    it "deletes the coupons" do
      delete("/shopkeeper/v2/promotions/#{@promotion.id}/organization",
        params: {organization_id: organization_two.id},
        headers:)

      @promotion.reload
      expect(response).to be_successful
      expect(Cupon.active.count).to eq(0)
      expect(@promotion.online_organization_id).to eq(nil)
    end
  end
end
