# frozen_string_literal: true

require "rails_helper"

RSpec.describe Shopkeeper::V2::UsersController, type: :request do
  let!(:shopkeeper) { create(:shopkeeper, :admin_lecupon) }
  let!(:organization) { create(:organization) }
  let!(:branch_one) { create(:branch, organization:) }
  let!(:branch_two) { create(:branch, organization:) }
  let!(:branch_three) { create(:branch, organization:) }
  let!(:branch_four) { create(:branch, organization:) }
  let!(:branch_five) { create(:branch, organization:) }
  let(:headers) { {"X-Shopkeeper-Email": shopkeeper.email, "X-Shopkeeper-Token": shopkeeper.authentication_token} }
  let(:index_serializer_keys) { %w[id name email role] }
  let(:serializer_keys) { %w[id name email role promotion_providers businesses] }
  let(:branch_serializer_keys) do
    %w[id name active full_address taxpayer_number telephone organization]
  end

  describe "GET /shopkeeper/v2/users" do
    let!(:organization_one) { create(:organization) }
    let!(:organization_two) { create(:organization) }
    let!(:branch_one) { create(:branch, organization: organization_one) }
    let!(:branch_two) { create(:branch, organization: organization_two) }
    let!(:shopkeeper_admin_lecupon) do
      create(:shopkeeper, :admin_lecupon, name: "<PERSON> Doe", email: "<EMAIL>")
    end
    let!(:shopkeeper_admin_shopkeeper) do
      create(:shopkeeper, :admin_shopkeeper, name: "Jane Doe", email: "<EMAIL>")
    end
    let!(:shopkeeper_regular) do
      create(:shopkeeper, :shopkeeper, name: "Jack", email: "<EMAIL>")
    end

    it "returns the users" do
      get(shopkeeper_v2_users_url, headers:)

      expect(response).to be_successful
      response_hash = JSON.parse(response.body)
      expect(response_hash[0].keys).to match_array(index_serializer_keys)
      expect(response_hash.count).to eq(4)
    end

    it "returns the users filtered by name" do
      get(shopkeeper_v2_users_url(name: " doe"), headers:)

      expect(response).to be_successful
      response_hash = JSON.parse(response.body)
      expect(response_hash.pluck("id")).to match_array([shopkeeper_admin_lecupon.id, shopkeeper_admin_shopkeeper.id])
      expect(response_hash[0].keys).to match_array(index_serializer_keys)
      expect(response_hash.count).to eq(2)
    end

    it "returns the users filtered by email" do
      get(shopkeeper_v2_users_url(email: "@email"), headers:)

      expect(response).to be_successful
      response_hash = JSON.parse(response.body)
      expect(response_hash.pluck("id")).to match_array([shopkeeper_admin_lecupon.id, shopkeeper_regular.id])
      expect(response_hash[0].keys).to match_array(index_serializer_keys)
      expect(response_hash.count).to eq(2)
    end

    describe "shopkeeper with many relationships" do
      let!(:branch_three) { create(:branch, organization: organization_one) }
      before do
        shopkeeper_admin_shopkeeper.branches = [branch_one, branch_three]
        shopkeeper_regular.branches = [branch_two]
      end

      it "returns the users filtered by organization id" do
        get("/shopkeeper/v2/users?organization_id=#{organization_one.id}", headers:)

        expect(response).to be_successful
        response_hash = JSON.parse(response.body)
        expect(response_hash.pluck("id")).to match_array([shopkeeper_admin_shopkeeper.id])
        expect(response_hash[0].keys).to match_array(index_serializer_keys)
        expect(response_hash.count).to eq(1)
      end
    end
  end

  describe "GET /shopkeeper/v2/users/branches" do
    let!(:shopkeeper_admin_shopkeeper) { create(:shopkeeper, :admin_lecupon) }

    it "returns the user" do
      get(shopkeeper_v2_user_url(shopkeeper_admin_shopkeeper.id), headers:)

      expect(response).to be_successful
      expect(JSON.parse(response.body).keys).to match_array(serializer_keys)
    end

    let!(:shopkeeper_unrelated_to_the_current_shopkeeper) do
      create(:shopkeeper, :admin_shopkeeper)
    end
    let!(:branch_unrelated_to_the_current_shopkeeper) do
      create(:branch)
    end
    let!(:shopkeeper_two) { create(:shopkeeper, :admin_shopkeeper) }

    before do
      branch_unrelated_to_the_current_shopkeeper.shopkeepers << shopkeeper_unrelated_to_the_current_shopkeeper
      branch_one.shopkeepers << shopkeeper_two
      branch_two.shopkeepers << shopkeeper_two
      branch_unrelated_to_the_current_shopkeeper.shopkeepers << shopkeeper_two
    end

    context "when the current shopkeeper is not admin lecupon" do
      let!(:business) { create(:business) }
      let!(:shopkeeper) { create(:shopkeeper, :admin_shopkeeper, businesses: [business]) }

      before do
        shopkeeper.branches = [branch_one, branch_two, branch_three, branch_four, branch_five]
        shopkeeper_two.businesses = [business]
      end

      it "returns the branches of the user, to which the current shopkeeper has access" do
        get("/shopkeeper/v2/users/#{shopkeeper_two.id}/branches", headers:)

        expect(response).to be_successful
        response_hash = JSON.parse(response.body)
        expect(response_hash.pluck("id")).to match_array([branch_one.id, branch_two.id])
      end
    end

    context "when the current shopkeeper is admin lecupon" do
      let!(:shopkeeper) { create(:shopkeeper, :admin_lecupon) }

      it "returns all the branches of the user" do
        get("/shopkeeper/v2/users/#{shopkeeper_two.id}/branches", headers:)

        expect(response).to be_successful
        response_hash = JSON.parse(response.body)
        expect(response_hash.pluck("id")).to match_array([branch_one.id, branch_two.id, branch_unrelated_to_the_current_shopkeeper.id])
      end
    end
  end

  describe "GET /shopkeeper/v2/users/me" do
    it "returns the current user" do
      get(me_shopkeeper_v2_users_url, headers:)

      expect(response).to be_successful
      response_hash = JSON.parse(response.body)
      expect(response_hash.keys).to match_array(serializer_keys)
      expect(response_hash["id"]).to eq(shopkeeper.id)
    end
  end

  describe "POST /shopkeeper/v2/users" do
    context "when creating a regular shopkeeper" do
      let!(:business) { create(:business) }
      let(:params) do
        build(:shopkeeper, :shopkeeper).attributes.merge!(
          password: "123456",
          branches: [
            {organization_id: organization.id, branch_ids: [branch_one.id, branch_two.id]}
          ],
          business_ids: [business.id]
        )
      end

      it "creates the user" do
        post(shopkeeper_v2_users_url, params:, headers:)

        expect(response).to be_successful
        response_hash = JSON.parse(response.body)
        expect(response_hash.keys).to match_array(serializer_keys)
        shopkeeper = Shopkeeper.find(response_hash["id"])
        expect(shopkeeper.branches.size).to eq(2)
        expect(shopkeeper.businesses.ids).to eq([business.id])
      end

      context "with promotion providers" do
        before do
          params.merge!(promotion_providers: [
            Promotion::Provider::COUPON_CODE,
            Promotion::Provider::REGIONALIZED
          ])
        end

        it "creates the user but does not set the promotion providers for that user" do
          post(shopkeeper_v2_users_url, params:, headers:)

          expect(response).to be_successful
          response_hash = JSON.parse(response.body)
          expect(response_hash.keys).to match_array(serializer_keys)
          expect(ShopkeeperPromotionProvider.where(shopkeeper: Shopkeeper.last).count).to eq(0)
        end
      end
      context "with used email" do
        let!(:shopkeeper) { create(:shopkeeper, :admin_lecupon) }

        it "creates the user but does not set the promotion providers for that user" do
          post(shopkeeper_v2_users_url, params: params.merge!(email: shopkeeper.email), headers:)

          response_hash = JSON.parse(response.body)
          expect(response_hash["error"]).to eq("Esse endereço de email já está em uso")
        end
      end
    end

    context "when creating a manager shopkeeper" do
      let(:organization) { create(:organization) }
      let(:params) do
        build(:shopkeeper, :admin_shopkeeper).attributes.merge!(
          password: "123456",
          organization_id: organization.id,
          promotion_providers: [
            Promotion::Provider::COUPON_CODE,
            Promotion::Provider::REGIONALIZED
          ]
        )
      end

      it "creates the user" do
        post(shopkeeper_v2_users_url, params:, headers:)

        expect(response).to be_successful
        response_hash = JSON.parse(response.body)
        expect(response_hash.keys).to match_array(serializer_keys)
        expect(Shopkeeper.last.managed_promotion_providers.count).to eq(2)
      end
    end

    context "when creating a manager shopkeeper for all branches" do
      let(:params) do
        build(:shopkeeper, :admin_shopkeeper).attributes.merge!(
          password: "123456",
          all_branches: true,
          organization_id: organization.id
        )
      end

      it "creates the user for all branches" do
        post(shopkeeper_v2_users_url, params:, headers:)

        expect(response).to be_successful
        response_hash = JSON.parse(response.body)
        expect(response_hash.keys).to match_array(serializer_keys)
        expect(Shopkeeper.find(response_hash["id"]).branches.size).to eq(5)
      end
    end

    context "when passing promotion providers for user that is not admin shopkeeper" do
      let(:params) do
        build(:shopkeeper, :shopkeeper).attributes.merge!(
          password: "123456",
          promotion_providers: [
            Promotion::Provider::COUPON_CODE,
            Promotion::Provider::REGIONALIZED
          ]
        )
      end

      it "creates the user but does not set the promotion providers for that user" do
        post(shopkeeper_v2_users_url, params:, headers:)

        expect(response).to be_successful
        response_hash = JSON.parse(response.body)
        expect(response_hash.keys).to match_array(serializer_keys)
        expect(ShopkeeperPromotionProvider.where(shopkeeper: Shopkeeper.last).count).to eq(0)
      end
    end

    describe "branches of multiple organizations" do
      let!(:organization_two) { create(:organization) }
      let!(:branch_six) { create(:branch, organization: organization_two) }
      let!(:branch_seven) { create(:branch, organization: organization_two) }
      let(:params) do
        build(:shopkeeper, :admin_shopkeeper).attributes.merge!(
          password: "123456",
          branches: [
            {organization_id: organization.id, branch_ids: [branch_one.id, branch_two.id]},
            {organization_id: organization_two.id, all_branches: true}
          ]
        )
      end

      it "creates the user for the all branches passed" do
        post(shopkeeper_v2_users_url, params:, headers:)

        expect(response).to be_successful
        response_hash = JSON.parse(response.body)
        expect(response_hash.keys).to match_array(serializer_keys)
        expect(Shopkeeper.find(response_hash["id"]).branches.ids).to match_array([
          branch_one.id, branch_two.id, branch_six.id, branch_seven.id
        ])
      end
    end
  end

  describe "PATCH /shopkeeper/v2/users/:id" do
    context "when updating the shopkeeper and his branches access" do
      let!(:business_one) { create(:business) }
      let!(:business_two) { create(:business) }
      let!(:shopkeeper_admin_shopkeeper) { create(:shopkeeper, :admin_shopkeeper, businesses: [business_one]) }
      let(:params) do
        {
          name: "updated name",
          branches: [{organization_id: organization.id, branch_ids: [branch_two.id]}],
          promotion_providers: [
            Promotion::Provider::COUPON_CODE,
            Promotion::Provider::REGIONALIZED
          ],
          business_ids: [business_two.id]
        }
      end

      before do
        shopkeeper_admin_shopkeeper.update(branches: [branch_one])
      end

      it "updates the user" do
        patch(shopkeeper_v2_user_url(shopkeeper_admin_shopkeeper.id), params:, headers:)

        expect(response).to be_successful
        expect(JSON.parse(response.body).keys).to match_array(serializer_keys)
        expect(shopkeeper_admin_shopkeeper.reload.branches.ids).to eq([branch_two.id])
        expect(shopkeeper_admin_shopkeeper.reload.managed_promotion_providers.count).to eq(2)
        expect(shopkeeper_admin_shopkeeper.businesses.ids).to eq([business_two.id])
      end
    end

    context "when updating the shopkeeper to manage all branches" do
      let!(:shopkeeper_admin_shopkeeper) { create(:shopkeeper, :admin_shopkeeper) }
      let(:params) { {name: "updated name", all_branches: true, organization_id: organization.id} }

      before do
        shopkeeper_admin_shopkeeper.update(branches: [branch_one])
      end

      it "updates the user" do
        patch(shopkeeper_v2_user_url(shopkeeper_admin_shopkeeper.id), params:, headers:)

        expect(response).to be_successful
        expect(JSON.parse(response.body).keys).to match_array(serializer_keys)
        expect(shopkeeper_admin_shopkeeper.reload.branches.size).to eq(5)
      end
    end

    context "when passing promotion providers for user that is not admin shopkeeper" do
      let!(:regular_shopkeeper) { create(:shopkeeper, :shopkeeper) }
      let(:params) do
        {
          name: "updated name",
          branches: [{organization_id: organization.id, branch_ids: [branch_two.id]}],
          promotion_providers: [
            Promotion::Provider::COUPON_CODE,
            Promotion::Provider::REGIONALIZED
          ]
        }
      end

      it "updates the user but does not set the promotion providers for that user" do
        patch(shopkeeper_v2_user_url(regular_shopkeeper.id), params:, headers:)

        expect(response).to be_successful
        response_hash = JSON.parse(response.body)
        expect(response_hash.keys).to match_array(serializer_keys)
        expect(ShopkeeperPromotionProvider.where(shopkeeper: Shopkeeper.last).count).to eq(0)
      end
    end
  end

  describe "DELETE /shopkeeper/v2/users/:id" do
    let!(:shopkeeper_admin_shopkeeper) { create(:shopkeeper, :admin_shopkeeper) }

    before do
      shopkeeper_admin_shopkeeper.branches = shopkeeper.branches
    end

    it "deletes the user" do
      delete(shopkeeper_v2_user_url(shopkeeper_admin_shopkeeper.id), headers:)

      expect(response).to be_successful
    end
  end

  context "when the current shopkeeper cannot manage the shopkeeper" do
    let!(:shopkeeper) { create(:shopkeeper, :admin_shopkeeper) }
    let!(:headers) { {"X-Shopkeeper-Email": shopkeeper.email, "X-Shopkeeper-Token": shopkeeper.authentication_token} }
    let(:params) { build(:shopkeeper, :admin_lecupon).attributes }

    it "does not authorize" do
      post(shopkeeper_v2_users_url, params:, headers:)

      expect(response).to be_forbidden
    end
  end
end
