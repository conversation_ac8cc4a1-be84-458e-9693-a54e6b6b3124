# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::V2::Organizations::FavoritesController, type: :request do
  let(:business) { create(:business, :with_cashback) }
  let(:user) { create(:user, business:) }
  let!(:web_headers) do
    Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret, "App-Version" => "4.0.0", "aud" => "web"}, user)
  end
  let!(:headers) do
    Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret}, user)
  end
  let(:json_parse_response_body) { JSON.parse(response.body) }

  describe "GET /api/v2/organizations/favorites" do
    let!(:organization) { create(:organization, :with_cashback, :percent_highest_discount, active: true, highest_discount_value: 12) }
    let!(:favorite_organization) { create(:favorite_organization, user:, organization:).organization }

    let(:organization_serializer_keys) do
      %w[
        id
        description
        distance
        favorited
        highest_discount
        highest_fund
        logo_image_large
        logo_image_small
        name
        top_background_image_large
        top_background_image_small
        branch_types
      ]
    end
    let(:organization_3_4_5_serializer_keys) do
      %w[
        id
        description
        distance
        favorited
        highest_discount
        highest_cashback
        highest_online_discount
        highest_physical_discount
        logo_image_large
        logo_image_small
        name
        top_background_image_large
        top_background_image_small
        branch_types
      ]
    end

    before { business.project_config.update!(show_only_organizations_with_coupons: false) }

    it "must render user favorited organizations" do
      get "/api/v2/organizations/favorites", headers: web_headers

      expect(response).to be_successful
      expect(json_parse_response_body.map(&:keys)).to all(match_array(organization_serializer_keys))
      expect(json_parse_response_body.pluck("highest_discount")).to eq([
        "type" => "percent",
        "value" => 12.0
      ])
      expect(json_parse_response_body.pluck("highest_fund")).to eq([{
        "type" => "percent",
        "value" => 9.0
      }])
      expect(json_parse_response_body.pluck("id")).to eq([favorite_organization.id])
    end

    it "must render user favorited organizations when app version is equal to compatible serializer version" do
      headers["App-Version"] = "3.4.5"
      get("/api/v2/organizations/favorites", headers:)

      expect(response).to be_successful
      expect(json_parse_response_body.map(&:keys)).to all(match_array(organization_3_4_5_serializer_keys))
      expect(json_parse_response_body.pluck("highest_discount")).to match_array([12])
      expect(json_parse_response_body.pluck("highest_cashback")).to eq([{
        "type" => "percent",
        "value" => 9.0
      }])
      expect(json_parse_response_body.pluck("id")).to eq([favorite_organization.id])
    end

    context "when currency is points" do
      let(:business) { create(:business, :with_cashback, currency: "points", fair_value: 0.0125) }

      it "renders ok" do
        get "/api/v2/organizations/favorites", headers: web_headers

        expect(response).to be_successful
        expect(json_parse_response_body.map(&:keys)).to all(match_array(organization_serializer_keys))
        expect(json_parse_response_body.pluck("highest_fund")).to eq([{
          "type" => "parity",
          "value" => 8.0
        }])
        expect(json_parse_response_body.pluck("id")).to eq([favorite_organization.id])
      end
    end

    context "when project config set to show organizations without coupons" do
      before { business.project_config.update!(show_only_organizations_with_coupons: true) }

      it "returns organizations that match term" do
        get "/api/v2/organizations/favorites", headers: web_headers

        expect(response).to be_successful
        expect(json_parse_response_body).to be_empty
      end
    end
  end

  describe "POST /api/v2/organizations/:id/favorites" do
    let(:organization) do
      create(
        :organization,
        :percent_highest_discount,
        active: true,
        highest_discount_value: 13,
        highest_cashback_value: 10,
        highest_cashback_type: :percent
      )
    end

    it "must set organization as favorite for user" do
      post "/api/v2/organizations/#{organization.id}/favorite", headers: web_headers

      expect(response).to be_created
      expect(json_parse_response_body["id"]).to eq(organization.id)
      expect(json_parse_response_body["highest_discount"]).to eq(
        "type" => "percent",
        "value" => 13.0
      )
      expect(json_parse_response_body["highest_fund"]).to eq(
        "type" => "percent",
        "value" => 9.0
      )
      expect(user.organizations).to include(organization)
    end

    context "when user already favorited organization" do
      let!(:favorite_organization) { create(:favorite_organization, organization:, user:) }

      it "renders created" do
        post("/api/v2/organizations/#{organization.id}/favorite", headers: web_headers)

        expect(response).to be_created
        expect(json_parse_response_body["id"]).to eq(organization.id)
        expect(json_parse_response_body["highest_discount"]).to eq(
          "type" => "percent",
          "value" => 13.0
        )
        expect(user.organizations).to include(organization)
      end
    end

    context "when currency is points" do
      let(:business) { create(:business, :with_cashback, currency: "points", fair_value: 0.0125) }

      it "renders created" do
        post "/api/v2/organizations/#{organization.id}/favorite", headers: web_headers

        expect(response).to be_created
        expect(json_parse_response_body["id"]).to eq(organization.id)
        expect(json_parse_response_body["highest_discount"]).to eq(
          "type" => "percent",
          "value" => 13.0
        )
        expect(json_parse_response_body["highest_fund"]).to eq(
          "type" => "parity",
          "value" => 8.0
        )
        expect(user.organizations).to include(organization)
      end
    end
  end

  describe "DELETE /api/v2/organizations/:id/favorites" do
    let(:favorite_organization) { create(:favorite_organization, user:).organization }

    it "must unset organization as favorite for user" do
      delete "/api/v2/organizations/#{favorite_organization.id}/favorite", headers: web_headers
      expect(response).to be_no_content
      expect(user.organizations).not_to include(favorite_organization)
    end
  end
end
