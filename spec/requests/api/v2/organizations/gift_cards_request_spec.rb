# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::V2::Organizations::GiftCardsController, type: :request do
  let(:currency) { "points" }
  let(:business) { create(:business, :with_cashback, giftcard: true, currency:, fair_value: 0.03) }
  let(:user) { create(:user, business:) }

  let!(:organization) { create(:organization, giftcard_redeemable: true) }
  let!(:organization_inactive_with_giftcard) { create(:organization, giftcard_redeemable: true, active: false) }
  let!(:organization_blocklisted_with_giftcard) { create(:organization, giftcard_redeemable: true) }
  let!(:organization_blocklist) do
    create(:organization_blocklist, organization: organization_blocklisted_with_giftcard, business:)
  end
  let!(:organization_without_giftcard) { create(:organization, giftcard_redeemable: false) }
  let!(:branch) { create(:branch, organization:) }
  let!(:bucket) { create(:voucher_bucket) }
  let!(:giftcard) { create(:giftcard, :with_vouchers, price: 29.99, quantity: 1, branches: [branch], voucher_bucket: bucket) }
  let!(:unavailable_giftcard) { create(:giftcard, branches: [branch], available: false) }

  let!(:fund_redemption_config) { create(:fund_redemption_config, :points, :giftcard, business:, fair_value: 0.03) }

  let(:headers) { Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret}, user) }

  describe "GET /api/v2/organizations/:organization_id/gift_cards" do
    it "renders ok" do
      get("/api/v2/organizations/#{organization.id}/gift_cards", headers:)

      expect(response).to be_ok

      expect(response.parsed_body.map(&:keys)).to all match_array %w[id title price redeem_instruction usage_expiration_date]
      expect(response.parsed_body.pluck("id")).to eq [giftcard.id]
      expect(response.parsed_body.pluck("price")).to eq [1000.0]
    end

    context "when currency is BRL" do
      let(:currency) { "BRL" }

      it "renders ok" do
        get("/api/v2/organizations/#{organization.id}/gift_cards", headers:)

        expect(response).to be_ok
        expect(response.parsed_body.map(&:keys)).to all match_array %w[id title price redeem_instruction fund usage_expiration_date]
        expect(response.parsed_body.pluck("id")).to eq [giftcard.id]
      end
    end

    context "when cashback is disabled" do
      let(:business) { create(:business, cashback: false, giftcard: true, currency:, fair_value: 0.03) }

      it "renders ok" do
        get("/api/v2/organizations/#{organization.id}/gift_cards", headers:)

        expect(response).to be_ok
        expect(response.parsed_body.map(&:keys)).to all match_array %w[id title price redeem_instruction usage_expiration_date]
        expect(response.parsed_body.pluck("id")).to eq [giftcard.id]
      end
    end

    context "when giftcard is disabled" do
      let(:business) { create(:business, :with_cashback, giftcard: false, currency:, fair_value: 0.03) }

      it "renders error" do
        get("/api/v2/organizations/#{organization.id}/gift_cards", headers:)

        expect(response).to be_forbidden
      end
    end

    context "with inactive organization" do
      it "renders error" do
        get("/api/v2/organizations/#{organization_inactive_with_giftcard.id}/gift_cards", headers:)

        expect(response).to be_not_found
      end
    end

    context "with blocklisted organization" do
      it "renders error" do
        get("/api/v2/organizations/#{organization_blocklisted_with_giftcard.id}/gift_cards", headers:)

        expect(response).to be_not_found
      end
    end

    context "with no giftcards left" do
      before do
        Giftcard::Order.reserve(user:, giftcard:, voucher: giftcard.vouchers.first)
        giftcard.refresh_availability
      end

      it "renders ok" do
        get("/api/v2/organizations/#{organization.id}/gift_cards", headers:)

        expect(response).to be_ok
        expect(response.parsed_body).to be_empty
      end
    end
  end
end
