# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::V2::Users::PasswordsController, type: :request do
  let(:response_hash) { JSON.parse(response.body) }

  let(:cpf) { CPF.new(FFaker::IdentificationBR.cpf).stripped }
  let!(:user_on_another_business) { create(:user, business: another_business, password: "12345678", cpf:) }
  let!(:another_business) { create(:business) }
  let!(:user) { create(:user, business:, password: "12345678", cpf:) }

  describe "#forgot_password" do
    let(:params) do
      {
        user: {
          cpf:
        }
      }
    end

    context "when business is active" do
      let(:headers) do
        {"Api-Secret": business.api_secret}
      end

      let!(:business) { create(:business) }

      context "when user is registered" do
        it "must send forgot email to user" do
          post "/api/v2/users/password", headers:, params:, as: :json

          expect(response).to be_successful

          expect(ActionMailer::Base.deliveries.count).to eq(1)
          mail = ActionMailer::Base.deliveries.last
          expect(mail.to).to eq([user.email])
          expect(mail.from).to eq([user.main_business.mailer_config.email_sender])
          expect(mail.subject).to eq("Instruções de troca de senha")
          token = Nokogiri::HTML(mail.body.to_s).xpath("//div/p").text
          expect(token).to be_present
        end
      end

      context "when user is not registered yet" do
        let(:params) do
          {
            user: {
              cpf: "invalid_cpf"
            }
          }
        end

        it "must render error" do
          post "/api/v2/users/password",
            headers:,
            params:,
            as: :json

          expect(response).to be_unprocessable
          expect(response_hash["errors"]["cpf"]).to eq(["não encontrado"])
        end
      end

      context "when user is inactive" do
        before { user.update(active: false) }

        it "must render error" do
          post "/api/v2/users/password",
            headers:,
            params:,
            as: :json

          expect(response).to be_unprocessable
          expect(response_hash["errors"]["cpf"]).to eq(["não encontrado"])
        end
      end

      context "when user is deleted" do
        before { user.update(active: true, deleted: true) }

        it "must render error" do
          post "/api/v2/users/password",
            headers:,
            params:,
            as: :json

          expect(response).to be_unprocessable
          expect(response_hash["errors"]["cpf"]).to eq(["não encontrado"])
        end
      end

      context "when business doesnt have a valid mailer_config" do
        it "must render successfull but not send mail" do
          business.mailer_config.update!(email_sender: "<EMAIL>", dns_configured: false, domain_configured: false)
          post "/api/v2/users/password", headers:, params:, as: :json

          expect(response).to be_successful

          expect(ActionMailer::Base.deliveries.count).to eq(0)
        end
      end
    end

    context "when business is inactive" do
      let!(:business) { create(:business, status: Business::Status::SUSPENDED_BY_OVERDUE) }

      let(:headers) do
        {"Api-Secret": business.api_secret}
      end

      it "must return unauthorized" do
        post "/api/v2/users/password",
          headers:,
          params:,
          as: :json

        expect(response).to be_unauthorized
        expect(response.headers["Authorization"]).not_to be_present
      end
    end

    context "when missing business headers" do
      let!(:business) { create(:business, active: true) }

      it "must return unauthorized" do
        post "/api/v2/users/password",
          params:,
          as: :json

        expect(response).to be_unauthorized
        expect(response.headers["Authorization"]).not_to be_present
      end
    end

    context "when business disabled transactional emails" do
      let!(:business) { create(:business, active: true) }
      let(:headers) do
        {"Api-Secret": business.api_secret}
      end

      it "must render successfull but not send mail" do
        business.mailer_config.update!(send_transactional_emails: false)
        post "/api/v2/users/password", headers:, params:, as: :json

        expect(response).to be_successful

        expect(ActionMailer::Base.deliveries.count).to eq(0)
      end
    end
  end
end
