# frozen_string_literal: true

require "rails_helper"
require "sidekiq/testing"

RSpec.describe Api::V2::Users::SessionsController, type: :request do
  let(:response_hash) { JSON.parse(response.body) }

  let(:cpf) { CPF.new(FFaker::IdentificationBR.cpf).stripped }
  let!(:user_on_another_business) { create(:user, business: another_business, password: "12345678", cpf:) }
  let!(:another_business) { create(:business) }
  let!(:user) { create(:user, business:, password: "12345678", cpf:) }
  let!(:user_destroy_request) { create(:user_destroy_request, user:) }

  describe "#create" do
    let(:serialized_keys) { %w[id destroy_request] }

    let(:project_serialized_keys) do
      %w[cashback giftcard cashback_transfer_receiver cashback_requestable_amount biometry organization_manage]
    end

    let(:destroy_request_serialized_keys) do
      %w[created_at]
    end

    let(:params) do
      {
        user: {
          cpf:,
          password: "12345678"
        }
      }
    end

    context "when business is active" do
      let(:headers) do
        {"Api-Secret": business.api_secret}
      end

      let!(:business) { create(:business) }

      context "when user is registered" do
        it "must sign in and returns successful" do
          expect do
            post "/api/v2/users/sign_in", headers:, params:, as: :json
          end.to not_change(WebhookSender::EventWorker.jobs, :size)
          expect(response).to be_successful
          expect(serialized_keys).to all be_in response_hash.keys
          expect(response.headers["Authorization"]).to be_present
          expect(project_serialized_keys).to all be_in response_hash["project"].keys
          expect(destroy_request_serialized_keys).to all be_in response_hash["destroy_request"].keys
          expect(response_hash["id"]).to eq(user.id)
        end

        context "when its cpf is banned" do
          before do
            BannedCpf.create(cpf:, reason: BannedCpf::Reasons::PAYMENT_RATE_LIMIT)
          end

          it "must be forbidden" do
            post "/api/v2/users/sign_in", headers:, params:, as: :json

            expect(response).to be_forbidden
            expect(response.headers["Authorization"]).not_to be_present
            expect(response_hash["error"]).to eq(I18n.t(BannedCpf::Reasons::PAYMENT_RATE_LIMIT, scope: "banned_cpf.reasons"))
          end
        end

        context "when passing app_version header" do
          context "when version is equal or above 3.2.1" do
            it "must render with clean auth serializer" do
              post "/api/v2/users/sign_in", headers: headers.merge("App-Version" => "3.2.1"), params:, as: :json

              expect(response).to be_successful
              expect(serialized_keys).to match_array(response_hash.keys)
            end
          end

          context "when version is below 3.2.1" do
            let(:old_serialized_keys) do
              %w[id name cellphone cpf email project full_address lat lng default_auth_flow]
            end

            it "must render with default auth serializer" do
              post "/api/v2/users/sign_in", headers: headers.merge("App-Version" => "3.2.0"), params:, as: :json

              expect(response).to be_successful
              expect(old_serialized_keys).to all be_in response_hash.keys
            end
          end

          context "when business is forced to use new serializer" do
            let(:business) { create(:business, cnpj: "02421421000111") }

            it "must render with default auth serializer independent of version" do
              post "/api/v2/users/sign_in", headers: headers.merge("App-Version" => "3.1.9"), params:, as: :json

              expect(response).to be_successful
              expect(serialized_keys).to match_array(response_hash.keys)
            end
          end
        end
      end

      context "when user is not registered yet" do
        let(:params) do
          {
            user: {
              cpf: FFaker::IdentificationBR.cpf,
              password: "12345678"
            }
          }
        end

        it "must render error" do
          post "/api/v2/users/sign_in",
            headers:,
            params:,
            as: :json

          expect(response).to be_unauthorized
          expect(response_hash["error"]).to eq("Credenciais inválidas.")
          expect(response.headers["Authorization"]).not_to be_present
        end
      end

      context "when user is inactive" do
        before { user.update(active: false) }

        it "must render error" do
          post "/api/v2/users/sign_in",
            headers:,
            params:,
            as: :json

          expect(response).to be_unauthorized
          expect(response_hash["error"]).to eq(business.message_by_type(Enums::CustomTextType::CONTACT_MANAGER))
          expect(response.headers["Authorization"]).not_to be_present
        end
      end

      context "when user is deleted" do
        before { user.update(deleted: true) }

        it "must render error" do
          post "/api/v2/users/sign_in",
            headers:,
            params:,
            as: :json

          expect(response).to be_unauthorized
          expect(response_hash["error"]).to eq(I18n.t("devise.failure.invalid", authentication_keys: :Cpf))
          expect(response.headers["Authorization"]).not_to be_present
        end
      end

      context "when using smart_token param" do
        let(:headers) do
          {"Api-Secret": business.api_secret}
        end

        let!(:business) { create(:business, :oab, name: "OABMG") }

        before do
          user.create_smart_token
        end

        context "when with valid smart token" do
          let(:params) do
            {user: {smart_token: user.smart_token}}
          end

          it "must authenticate succesfully" do
            expect do
              post "/api/v2/users/sign_in", headers:, params:, as: :json
            end.to not_change(WebhookSender::EventWorker.jobs, :size)
            expect(response).to be_successful
            expect(response.headers["Authorization"]).to be_present
          end
        end

        context "when with invalid smart token" do
          let(:params) do
            {user: {smart_token: "000"}}
          end

          it "must not authenticate" do
            post "/api/v2/users/sign_in", headers:, params:, as: :json

            expect(response).to be_unauthorized
            expect(response.headers["Authorization"]).not_to be_present
            expect(response_hash["error"]).to eq(I18n.t("devise.failure.invalid_smart_token"))
          end
        end
      end

      context "when business uses default_with_external_validation UserManager" do
        let(:headers) do
          {"Api-Secret": business.api_secret}
        end

        let!(:business) { create(:business, :oab, name: "OABMG") }

        before do
          params[:user][:number] = "20020"

          business.project_config.update(user_manager: Enums::UserManager::DEFAULT_WITH_EXTERNAL_VALIDATION)

          stub_request(:post, %r{(.*)/hostservice.oabmg.org.br/(.*)}).to_return(
            status: 200,
            body: {"CodigoRetorno" => 0}.to_json
          )
        end

        it "must authenticate succesfully" do
          expect do
            post "/api/v2/users/sign_in", headers:, params:, as: :json
          end.not_to change(WebhookSender::EventWorker.jobs, :size)
          expect(response).to be_successful
          expect(response.headers["Authorization"]).to be_present
        end
      end

      context "when business uses no_signup UserManager" do
        let!(:business) { create(:business, :avanza_ti, :with_cashback) }

        let(:headers) do
          {"Api-Secret": business.api_secret}
        end

        let(:params) do
          {
            user: {
              cpf: "***********",
              password: "netway"
            }
          }
        end

        it "must authenticate succesfully creating a user", :vcr do
          expect do
            expect do
              post "/api/v2/users/sign_in", headers:, params:, as: :json
            end.to change(WebhookSender::EventWorker.jobs, :size).by(1)
            expect(response).to be_successful
            expect(response.headers["Authorization"]).to be_present
          end.to change(User, :count).by(1)
            .and change(Wallet, :count).by(1)
        end

        context "when user has default auth flow" do
          let!(:user) { create(:user, business:, cpf: params[:user][:cpf], default_auth_flow: true, password: params[:user][:password]) }
          let(:params) do
            {user: {cpf: "***********", password: "netway"}}
          end

          it "must ignore integration when user has default auth flow", :vcr do
            expect(CustomAuth::AuthV2).not_to receive(:new)
            post "/api/v2/users/sign_in", headers:, params:, as: :json

            expect(response).to be_successful
            expect(response.headers["Authorization"]).to be_present
          end
        end

        context "when any error happens on custom auth", :vcr do
          let(:params) do
            {
              user: {
                cpf: "***********",
                email: "<EMAIL>",
                password: "Gyg2*ttX"
              }
            }
          end

          before do
            business.project_config.update(user_manager: "no_signup", auth_integration_type: "gr_group")
          end

          it "must render its error message" do
            expect do
              post "/api/v2/users/sign_in", headers:, params:, as: :json
            end.to not_change(WebhookSender::EventWorker.jobs, :size)
              .and not_change(User, :count)
            expect(response).to be_unauthorized
            expect(response_hash["error"]).to eq(business.message_by_type(Enums::CustomTextType::AUTH_INTEGRATION_INACTIVE_CPF))
            expect(response.headers["Authorization"]).not_to be_present
          end

          context "when auth integration type is Hubsoft", :vcr do
            let!(:hubsoft_token) { create(:hubsoft_token, business:, bundle_code: nil, use_only_cpf: true) }
            before { business.project_config.update(user_manager: "no_signup", auth_integration_type: "hubsoft") }

            it "must render hubsoft contract errors" do
              expect do
                post "/api/v2/users/sign_in", headers:, params: {user: {cpf: "***********", password: "***********"}}, as: :json
              end.to not_change(WebhookSender::EventWorker.jobs, :size)
                .and not_change(User, :count)

              expect(response).to be_unauthorized
              expect(response_hash["error"]).to eq("Não foi encontrado um contrato vinculado a seu CPF, Plano não autorizado, Contrato suspenso")
              expect(response.headers["Authorization"]).not_to be_present
            end
          end
        end
      end

      context "when business uses single_sign_on" do
        let!(:business) { create(:business, cnpj: "62500855000139", name: "Associacao Dos Advogados De Sao Paulo") }
        let(:headers) { {"Api-Secret" => business.api_secret, "App-Version" => "3.2.1", "aud" => Enums::SessionApplicationType::WEB} }
        let(:serialized_keys) { %w[id] }
        let(:jwt) { "eyJhbGciOiJSUzI1NiIsImtpZCI6IjFFQ0FBODcwOUE2OTYzNEJCQUVCMzgxN0RGQTA0QjhGN0VGQzIwREEiLCJ0eXAiOiJKV1QiLCJ4NXQiOiJIc3FvY0pwcFkwdTY2emdYMzZCTGozNzhJTm8ifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.XWcRISsF4-kdVKzaEh9gxlAu2I66pt6EAblIO1L8E7FNB0AgHJsbDHKMVRcigavcnuLOtvTOqHJ6lIKCW4uXHSuw09ybLO-pPEeX3HLXQVaryEz_e1CNNTvTPmy10m9ybYNLMIjyzSDOk-hlf92gstNd7kJ8UP4YRjfPBdTOswk3xXMmwsVMpEtClGejCbPTyYwhTDYVQU8CaRgttw5eNxDaDkXyejij_EMYusj7pCH0BTNOf3USZQaKc4jDmnXS0CMG4TtcH-wUL3WSpitKO1TKa1gbpn-FeYwdo7KvgR1P5qJB6stnqBJlUcOFOBmKz6h2NFtj_VKjOq5_WFj6VA" }
        let(:decoded_jwt) { CustomSso::Aasp::Decode.decode(access_token: jwt) }

        before do
          business.project_config.update!(
            user_manager: Enums::UserManager::SINGLE_SIGN_ON,
            auth_integration_type: Enums::ProjectConfig::AuthIntegrationType::AASP
          )
        end

        context "when with valid jwt and existing user" do
          let(:params) do
            {user: {access_token: jwt}}
          end

          let!(:user) { create(:user, business:, external_id: "5257822") }

          it "must render succesfull" do
            expect do
              post "/api/v2/users/sign_in",
                headers:,
                params:,
                as: :json
            end.not_to change(WebhookSender::EventWorker.jobs, :size)
            expect(response).to be_successful
            expect(serialized_keys).to all be_in response_hash.keys
            expect(response.headers["Authorization"]).to be_present
          end
        end

        context "when with valid jwt and all params to register user" do
          let(:params) do
            {user: {access_token: jwt, cpf: "***********"}}
          end

          it "must create user and return succesfull" do
            expect do
              post "/api/v2/users/sign_in",
                headers:,
                params:,
                as: :json
            end.to change(WebhookSender::EventWorker.jobs, :size).by(1)

            expect(response).to be_successful
            expect(response_hash.keys).to match_array(serialized_keys)
            expect(response.headers["Authorization"]).to be_present
          end
        end

        context "when with valid jwt but without all params to register user" do
          let(:params) do
            {user: {access_token: jwt}}
          end

          it "must render unprocessable entity with user data" do
            expect do
              post "/api/v2/users/sign_in",
                headers:,
                params:,
                as: :json
            end.not_to change(WebhookSender::EventWorker.jobs, :size)

            expect(response).to be_unprocessable
            expect(response_hash.keys).to match_array(%w[cpf taxpayer_number name email cellphone])
            expect(response_hash["cpf"]).to eq(nil)
            expect(response_hash["name"]).to eq(Utils::NameNormalizer.call(decoded_jwt["NomePessoaFisica"]))
          end
        end
      end
    end

    context "when business is inactive" do
      let!(:business) { create(:business, status: Business::Status::SUSPENDED_BY_OVERDUE) }

      let(:headers) do
        {"Api-Secret": business.api_secret}
      end

      it "must return unauthorized" do
        expect do
          post "/api/v2/users/sign_in",
            headers:,
            params:,
            as: :json
        end.not_to change(WebhookSender::EventWorker.jobs, :size)

        expect(response).to be_unauthorized
        expect(response.headers["Authorization"]).not_to be_present
      end
    end

    context "when missing business headers" do
      let!(:business) { create(:business, active: true) }

      it "must return unauthorized" do
        expect do
          post "/api/v2/users/sign_in",
            params:,
            as: :json
        end.not_to change(WebhookSender::EventWorker.jobs, :size)

        expect(response).to be_unauthorized
        expect(response.headers["Authorization"]).not_to be_present
      end
    end

    context "when passing aud header to deal with multiple clients" do
      let!(:business) { create(:business) }

      context "when there is no session for this user" do
        let(:headers) do
          {"Api-Secret": business.api_secret, aud: "api"}
        end

        it "must create a new one" do
          expect do
            post "/api/v2/users/sign_in", headers:, params:, as: :json
          end.to change(AllowlistedJwt, :count).by(1)
            .and not_change(WebhookSender::EventWorker.jobs, :size)

          allowlisted_jwt = AllowlistedJwt.first
          expect(allowlisted_jwt.user).to eq(user)
          expect(allowlisted_jwt.aud).to eq("api")
          expect(allowlisted_jwt.jti).to be_present
          expect(response).to be_successful
          expect(response.headers["Authorization"]).to be_present
        end
      end

      context "when already exists a session without an aud specified" do
        let(:headers) do
          {"Api-Secret": business.api_secret, aud: "api"}
        end

        before { AllowlistedJwt.create(user:, jti: SecureRandom.hex(16), aud: nil, exp: 3.days.from_now) }

        it "must create a new session and keep the old one too" do
          expect do
            post "/api/v2/users/sign_in", headers:, params:, as: :json
          end.to change(AllowlistedJwt, :count).from(1).to(2)
            .and not_change(WebhookSender::EventWorker.jobs, :size)

          allowlisted_jwt = AllowlistedJwt.last
          expect(allowlisted_jwt.user).to eq(user)
          expect(allowlisted_jwt.aud).to eq("api")
          expect(allowlisted_jwt.jti).to be_present

          expect(AllowlistedJwt.for(user).pluck(:aud)).to eq([nil, "api"])

          expect(response).to be_successful
          expect(response.headers["Authorization"]).to be_present
        end
      end

      context "when already exists a session with an different aud specified" do
        let(:headers) do
          {"Api-Secret": business.api_secret, aud: "api"}
        end

        before { AllowlistedJwt.create(user:, jti: SecureRandom.hex(16), aud: "web", exp: 3.days.from_now) }

        it "must create a new session and keep the old one too" do
          expect do
            post "/api/v2/users/sign_in", headers:, params:, as: :json
          end.to change(AllowlistedJwt, :count).from(1).to(2)
            .and not_change(WebhookSender::EventWorker.jobs, :size)

          allowlisted_jwt = AllowlistedJwt.last
          expect(allowlisted_jwt.user).to eq(user)
          expect(allowlisted_jwt.aud).to eq("api")
          expect(allowlisted_jwt.jti).to be_present

          expect(AllowlistedJwt.for(user).pluck(:aud)).to match_array(["web", "api"])

          expect(response).to be_successful
          expect(response.headers["Authorization"]).to be_present
        end
      end

      context "when already exists a session with the same aud specified" do
        let(:headers) do
          {"Api-Secret": business.api_secret, aud: "api"}
        end

        before { AllowlistedJwt.create(user:, jti: SecureRandom.hex(16), aud: "api", exp: 3.days.from_now) }

        it "must create a new session, replacing the old one" do
          expect do
            post "/api/v2/users/sign_in", headers:, params:, as: :json
          end.to not_change(AllowlistedJwt, :count)
            .and not_change(WebhookSender::EventWorker.jobs, :size)

          allowlisted_jwt = AllowlistedJwt.first
          expect(allowlisted_jwt.user).to eq(user)
          expect(allowlisted_jwt.aud).to eq("api")
          expect(allowlisted_jwt.jti).to be_present

          expect(AllowlistedJwt.for(user).pluck(:aud)).to eq(["api"])

          expect(response).to be_successful
          expect(response.headers["Authorization"]).to be_present
        end
      end
    end

    context "when business has custom fields" do
      let(:business) { create(:business) }
      let(:headers) { {"Api-Secret": business.api_secret} }

      let(:custom_field_param) { "custom_param_example" }
      let!(:custom_field) do
        create(:custom_field,
          business:,
          name: "any_name",
          param_name: custom_field_param,
          label: "any_label",
          description: "any description",
          fill_field: "custom_field_2",
          sign_in_param: true)
      end

      let(:params) do
        {
          user: {
            cpf: user.cpf,
            password: "12345678",
            custom_param_example: "ABC123"
          }
        }
      end

      it "must upsert custom_field_values from params" do
        post "/api/v2/users/sign_in", headers:, params:, as: :json

        expect(response).to be_successful
        expect(response.headers["Authorization"]).to be_present

        custom_field_value = user.custom_field_values.first
        expect(custom_field_value).to be_present
        expect(custom_field_value.custom_field).to eq(custom_field)
        expect(custom_field_value.field_value).to eq(params.dig(:user, custom_field_param.to_sym))
        expect(user.authorized_user.reload.custom_field_2).to eq(custom_field_value.field_value)
      end
    end
  end
end
