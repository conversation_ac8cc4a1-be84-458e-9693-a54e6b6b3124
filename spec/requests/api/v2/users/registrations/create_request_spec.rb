# frozen_string_literal: true

require "rails_helper"
require "devise/jwt/test_helpers"
require "sidekiq/testing"

RSpec.describe Api::V2::Users::RegistrationsController, type: :request do
  let!(:business) { create(:business) }
  let(:response_hash) { JSON.parse(response.body) }

  describe "#create" do
    let(:serialized_keys) { %w[id] }

    let(:project_serialized_keys) do
      %w[cashback giftcard cashback_transfer_receiver cashback_requestable_amount biometry organization_manage]
    end

    let(:params) do
      {
        user: {
          name: FFaker::Name.name,
          email: FFaker::Internet.email,
          cpf: FFaker::IdentificationBR.cpf,
          password: FFaker::Internet.password,
          cellphone: FFaker::PhoneNumberBR.mobile_phone_number
        }
      }
    end

    context "when its cpf is banned" do
      let(:headers) do
        {"Api-Secret": business.api_secret}
      end

      before do
        BannedCpf.create(cpf: params[:user][:cpf], reason: BannedCpf::Reasons::PAYMENT_RATE_LIMIT)
      end

      it "must be unauthorized" do
        post "/api/v2/users",
          headers:,
          params:,
          as: :json

        expect(response).to be_forbidden

        expect(response.headers["Authorization"]).not_to be_present
        expect(response_hash["error"]).to eq(I18n.t(BannedCpf::Reasons::PAYMENT_RATE_LIMIT, scope: "banned_cpf.reasons"))
      end
    end

    context "when business is active" do
      let(:headers) do
        {"Api-Secret": business.api_secret}
      end

      let!(:business) { create(:business, :with_cashback) }

      context "when user is not registered" do
        context "when authorized user exists" do
          context "and it is active" do
            let!(:authorized_user) do
              create(:authorized_user,
                cpf: params[:user][:cpf],
                business:,
                active: true)
            end

            it "returns successful and creates the user" do
              expect do
                post "/api/v2/users", headers:, params:, as: :json
              end.to change(WebhookSender::EventWorker.jobs, :size).by(1)
              expect(WebhookSender::EventWorker.jobs.last["args"][0]).to eq(WebhookSender::EventTypes::USER_CREATED)
              expect(response).to be_successful
              expect(User.count).to eq(1)
              expect(User.order(:id).last.wallets).to exist
              expect(serialized_keys).to all be_in response_hash.keys
              expect(project_serialized_keys).to all be_in response_hash["project"].keys
              expect(response.headers["Authorization"]).to be_present
            end

            context "when passing app_version header" do
              context "when version is equal or above 3.2.1" do
                let(:clean_serialized_keys) { %w[id] }
                it "must render with clean auth serializer" do
                  post "/api/v2/users", headers: headers.merge("App-Version" => "3.2.1"), params:, as: :json

                  expect(response).to be_successful
                  expect(clean_serialized_keys).to match_array(response_hash.keys)
                end
              end

              context "when version is below 3.2.1" do
                let(:serialized_keys) do
                  %w[id name cellphone cpf email project full_address lat lng default_auth_flow]
                end

                it "must render with default auth serializer" do
                  post "/api/v2/users", headers: headers.merge("App-Version" => "3.2.0"), params:, as: :json

                  expect(response).to be_successful
                  expect(serialized_keys).to all be_in response_hash.keys
                end
              end

              context "when business is forced to use new serializer" do
                let(:business) { create(:business, cnpj: "02421421000111") }

                it "must render with default auth serializer independent of version" do
                  post "/api/v2/users", headers: headers.merge("App-Version" => "3.1.9"), params:, as: :json

                  expect(response).to be_successful
                  expect(serialized_keys).to match_array(response_hash.keys)
                end
              end
            end
          end

          context "and it is on a related business" do
            let(:sub_business) { create(:business, main_business: business) }
            let!(:authorized_user) do
              create(:authorized_user,
                cpf: params[:user][:cpf],
                business: sub_business,
                active: true)
            end

            it "returns successful and creates the user for this related business" do
              post "/api/v2/users", headers:, params:, as: :json

              expect(response).to be_successful
              expect(User.count).to eq(1)
              user = User.last
              expect(user.business).to eq(sub_business)

              expect(serialized_keys).to all be_in response_hash.keys
              expect(project_serialized_keys).to all be_in response_hash["project"].keys
              expect(response.headers["Authorization"]).to be_present
            end
          end

          context "and it is inactive" do
            let!(:authorized_user) do
              create(:authorized_user,
                cpf: params[:user][:cpf],
                business:,
                active: false)
            end

            it "must render unauthorized" do
              post "/api/v2/users",
                headers:,
                params:,
                as: :json

              expect(response).to be_unauthorized
              expect(response_hash["error"]).to eq(Enums::CustomTextType.translate(:inactive_authorized_user)[:value])
              expect(response.headers["Authorization"]).not_to be_present
            end
          end
        end

        context "when authorized user does not exist" do
          it "must render unauthorized" do
            expect do
              post "/api/v2/users",
                headers:,
                params:,
                as: :json
            end.to not_change(WebhookSender::EventWorker.jobs, :size)
            expect(response).to be_unauthorized
            expect(response_hash["error"]).to eq(Enums::CustomTextType.translate(:contact_manager)[:value])
            expect(response.headers["Authorization"]).not_to be_present
          end
        end
      end

      context "when user is already registered" do
        let!(:authorized_user) do
          create(:authorized_user,
            cpf: params[:user][:cpf],
            business:,
            active: true)
        end

        let!(:user) { create(:user, cpf: params[:user][:cpf], business:, authorized_user:) }

        it "must render error" do
          expect do
            post "/api/v2/users",
              headers:,
              params:,
              as: :json
          end.to not_change(WebhookSender::EventWorker.jobs, :size)
          expect(response).to be_unprocessable
          expect(response_hash["errors"]["cpf"]).to include("já está em uso")
          expect(response.headers["Authorization"]).not_to be_present
        end
      end
    end

    context "when business is inactive" do
      let!(:business) { create(:business, status: Business::Status::SUSPENDED_BY_OVERDUE) }

      it "must return unauthorized" do
        expect do
          post "/api/v2/users",
            headers:,
            params:,
            as: :json
        end.to not_change(WebhookSender::EventWorker.jobs, :size)
        expect(response).to be_unauthorized
        expect(response.headers["Authorization"]).not_to be_present
      end
    end

    context "when missing business headers" do
      let!(:business) { create(:business, active: true) }

      it "must return unauthorized" do
        expect do
          post "/api/v2/users",
            params:,
            as: :json
        end.to not_change(WebhookSender::EventWorker.jobs, :size)
        expect(response).to be_unauthorized
        expect(response.headers["Authorization"]).not_to be_present
      end
    end

    context "when business uses default UserManager" do
      let(:headers) do
        {"Api-Secret": business.api_secret}
      end

      let!(:business) { create(:business) }

      let!(:authorized_user) do
        create(:authorized_user,
          cpf: params[:user][:cpf],
          business:,
          active: true)
      end

      it "must create a user from an authorization" do
        expect do
          post "/api/v2/users", headers:, params:, as: :json
        end.to change(WebhookSender::EventWorker.jobs, :size).by(1)
        expect(response).to be_successful
      end
    end

    context "when business uses open_signup UserManager" do
      let(:headers) do
        {"Api-Secret": business.api_secret}
      end

      let!(:business) { create(:business) }

      before { business.project_config.update(user_manager: Enums::UserManager::OPEN_SIGNUP, open_signup_business: business) }

      it "must create a user without previous authorization" do
        expect do
          post "/api/v2/users", headers:, params:, as: :json
        end.to change(WebhookSender::EventWorker.jobs, :size).by(1)
        expect(response).to be_successful
      end
    end

    context "when business uses default_with_external_validation UserManager", :vcr do
      let(:headers) do
        {"Api-Secret": oab.api_secret}
      end

      let(:oab) { create(:business, :oab_mt) }
      let(:oab_subbusiness) { create(:business, main_business: oab) }
      let(:auth_integration) { oab.auth_integration }

      before do
        params[:user][:number] = "287760"
        params[:user][:cpf] = "***********"

        auth_integration.update!(business: oab_subbusiness)
      end

      it "must create a user when custom auth succeed" do
        expect(CustomAuth::AuthV2).to receive(:new).and_call_original
        expect do
          post "/api/v2/users", headers:, params:, as: :json
        end.to change(WebhookSender::EventWorker.jobs, :size).by(1)
        registered_user = User.first
        expect(registered_user.business).to eq(oab_subbusiness)
        expect(response).to be_successful
      end
    end

    context "when business uses external UserManager" do
      let!(:business) { create(:business, project_config:) }
      let(:project_config) { create(:project_config, :with_external_user_manager) }

      let!(:authorized_user) do
        create(:authorized_user,
          cpf: params[:user][:cpf],
          business:,
          active: true)
      end

      let(:headers) do
        {"Api-Secret": business.api_secret}
      end

      it "must use default registration and create a user from an authorization" do
        expect do
          post "/api/v2/users", headers:, params:, as: :json
        end.to change(WebhookSender::EventWorker.jobs, :size).by(1)
        expect(response).to be_successful
      end
    end

    context "when business uses no_signup UserManager" do
      let!(:business) { create(:business) }

      let(:headers) do
        {"Api-Secret": business.api_secret}
      end

      before do
        business.project_config.update!(
          auth_integration_type: Enums::ProjectConfig::AuthIntegrationType::HUBSOFT,
          user_manager: Enums::UserManager::NO_SIGNUP
        )
      end

      it "must render denied registration" do
        expect do
          post "/api/v2/users", headers:, params:, as: :json
        end.to not_change(WebhookSender::EventWorker.jobs, :size)
        expect(response).to be_unprocessable
        expect(response_hash["error"]).to eq(I18n.t("user.sign_up.registration_denied"))
      end
    end

    context "when business has custom fields" do
      let(:business) { create(:business) }
      let(:headers) { {"Api-Secret": business.api_secret} }

      let(:custom_field_param) { "custom_param_example" }
      let!(:custom_field) do
        create(:custom_field,
          business:,
          name: "any_name",
          param_name: custom_field_param,
          label: "any_label",
          description: "any description",
          fill_field: "custom_field_1",
          sign_in_param: true)
      end

      let(:params) do
        {
          user: {
            name: FFaker::Name.name,
            email: FFaker::Internet.email,
            cpf: FFaker::IdentificationBR.cpf,
            password: FFaker::Internet.password,
            cellphone: FFaker::PhoneNumberBR.mobile_phone_number,
            custom_param_example: "ABC123"
          }
        }
      end
      let!(:authorized_user) { create(:authorized_user, business:, cpf: params.dig(:user, :cpf)) }

      it "must upsert custom_field_values from params" do
        post "/api/v2/users", headers:, params:, as: :json

        expect(response).to be_successful
        expect(response.headers["Authorization"]).to be_present
        user = User.last
        custom_field_value = user.custom_field_values.last
        expect(custom_field_value).to be_present
        expect(custom_field_value.custom_field).to eq(custom_field)
        expect(custom_field_value.field_value).to eq(params.dig(:user, custom_field_param.to_sym))
        expect(user.authorized_user.custom_field_1).to eq(custom_field_value.field_value)
      end
    end

    context "when business does not require email or cellphone" do
      let(:business) { create(:business) }
      let(:headers) { {"Api-Secret": business.api_secret} }
      let!(:authorized_user) { create(:authorized_user, business:, cpf: params.dig(:user, :cpf)) }
      let(:params) do
        {
          user: {
            name: FFaker::Name.name,
            cpf: FFaker::IdentificationBR.cpf,
            password: FFaker::Internet.password
          }
        }
      end

      before do
        business.project_config.update(need_email_on_registration: false, need_cellphone_on_registration: false)
      end

      it "must create user with dummy email/cellphone" do
        post "/api/v2/users", headers:, params:, as: :json

        expect(response).to be_successful
        expect(response.headers["Authorization"]).to be_present
        user = User.last
        expect(user.email).to include("#{user.cpf}@naoenviar.com.br")
        expect(user.cellphone).to eq("999999999")
        expect(authorized_user.reload.email).to include("#{user.cpf}@naoenviar.com.br")
        expect(authorized_user.reload.phone).to eq("999999999")
      end
    end
  end
end
