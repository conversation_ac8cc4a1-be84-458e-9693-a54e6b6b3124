# frozen_string_literal: true

require "rails_helper"
require "sidekiq/testing"

RSpec.describe Api::V2::Users::Telemedicine::AccessesController, type: :request do
  let!(:business) { create(:business, telemedicine: true) }
  let(:user) { create(:user, business:) }
  let(:authorized_user) { user.authorized_user }
  let!(:headers) do
    Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret}, user)
  end
  let(:response_hash) { JSON.parse(response.body) }

  before do
    business.telemedicine_config.update!(url: "https://anyurl.com")
  end

  describe "#create" do
    context "when user has active telemedicine", :vcr do
      let!(:telemedicine_beneficiary) { create(:telemedicine_beneficiary, :active, authorized_user:, external_id: "6413606") }

      it "must generate telemedicine smart link and redirect to it" do
        Sidekiq::Testing.inline! do
          expect do
            expect(Conexa::Client).to receive(:generate_smart_link).with(telemedicine_beneficiary.external_id, business.telemedicine_config.plan).and_call_original
            post("/api/v2/users/telemedicine/access", headers:)

            expect(response).to have_http_status(:ok)
            expect(response.redirect_url).to include("https://qa-embed-paciente.conexasaude.com.br/redirecionar")
            expect(authorized_user.telemedicine_activities.where(entry_type: :accessed).count).to eq(1)
            authorized_user.reload
            telemedicine_beneficiary.reload
          end.to change(telemedicine_beneficiary, :last_access_at)
        end
      end
    end

    context "when user is not registered on conexa yet" do
      let!(:telemedicine_beneficiary) { create(:telemedicine_beneficiary, authorized_user:, external_id: nil) }

      it "must be unprocessable" do
        post("/api/v2/users/telemedicine/access", headers:)

        expect(response).to be_unprocessable
        expect(response_hash["errors"]).to eq("Atualizando seus dados na telemedicina, volte em breve!")
      end
    end

    context "when user does not have active telemedicine" do
      let!(:telemedicine_beneficiary) { create(:telemedicine_beneficiary, :inactive, cpf: user.cpf) }

      it "must be forbidden" do
        post("/api/v2/users/telemedicine/access", headers:)

        expect(response).to be_forbidden
      end
    end

    context "when business does not have active telemedicine" do
      let!(:business) { create(:business, telemedicine: false) }
      let!(:telemedicine_beneficiary) { create(:telemedicine_beneficiary, :active, cpf: user.cpf) }

      it "must be forbidden" do
        post("/api/v2/users/telemedicine/access", headers:)

        expect(response).to be_forbidden
      end
    end
  end
end
