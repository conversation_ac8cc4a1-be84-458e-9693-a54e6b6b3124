# frozen_string_literal: true

require "rails_helper"
require "sidekiq/testing"

RSpec.describe Api::V2::Users::Telemedicine::SubscriptionsController, :vcr, type: :request do
  let!(:business) { create(:business, telemedicine: true) }
  let(:user) { create(:user, business:) }
  let(:authorized_user) { user.authorized_user }
  let!(:headers) do
    Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret}, user)
  end
  let(:response_hash) { JSON.parse(response.body) }
  let(:serialized_keys) { %w[elegible subscribed active] }

  before do
    business.telemedicine_config.update!(url: "https://anyurl.com", contracted_beneficiaries: 1500)
  end

  describe "#create" do
    context "when user is elegible to telemedicine but beneficiary was not created" do
      let(:expected_mocked_external_id) { "123" }

      before do
        authorized_user.update!(telemedicine: true)
        authorized_user.telemedicine_beneficiary.delete
        authorized_user.telemedicine_activities.each(&:delete)
      end

      it "must update telemedicine subscribe and activation date" do
        Sidekiq::Testing.inline! do
          expect(Conexa::Client).to receive(:create_or_update_user).and_call_original
          post "/api/v2/users/telemedicine/subscription", headers: headers, params: {birthdate: 1.day.ago, sex: "MALE"}
          expect(response).to be_successful
          expect(response_hash.keys).to match_array(serialized_keys)
          expect(response_hash["elegible"]).to eq(true)
          expect(response_hash["subscribed"]).to eq(true)
          expect(response_hash["active"]).to eq(true)
          expect(TelemedicineMailer.deliveries.count).to eq(1)
          expect(authorized_user.telemedicine_activities.where(entry_type: :subscribed).count).to eq(1)

          expect(TelemedicineBeneficiary.count).to eq(1)
          telemedicine_beneficiary = TelemedicineBeneficiary.find_by(cpf: authorized_user.cpf)

          expect(telemedicine_beneficiary.enabled).to eq(true)
          expect(telemedicine_beneficiary.external_id).to eq(expected_mocked_external_id)
          authorized_user.reload
        end
      end
    end

    context "when user is elegible but already subscribed" do
      before do
        business.telemedicine_config.update_columns(plan: Telemedicine::Plans::INTEGRAL)

        authorized_user.update!(telemedicine: true)
      end

      it "must update telemedicine beneficiary" do
        expect do
          expect(Conexa::Client).not_to receive(:create_or_update_user)
          post "/api/v2/users/telemedicine/subscription", headers: headers, params: {birthdate: 2.days.ago, sex: "MALE"}

          expect(response).to be_successful
          expect(response_hash["errors"]).to eq("Você já se cadastrou para usar Telemedicina.")
          expect(TelemedicineMailer.deliveries.count).to eq(0)
          expect(authorized_user.telemedicine_beneficiary.enabled).to eq(true)
        end.to not_change(TelemedicineBeneficiary, :count)
          .and not_change(authorized_user.telemedicine_beneficiary.reload, :enabled)
          .and not_change(authorized_user.telemedicine_activities.where(entry_type: :subscribed), :count)
      end
    end

    context "when subscribe transaction fails" do
      before do
        authorized_user.update_columns(telemedicine: true)
        business.telemedicine_config.update_columns(plan: Telemedicine::Plans::INTEGRAL)

        allow(Conexa::Client).to receive(:create_or_update_user).and_raise(Conexa::IntegrationError.new(I18n.t("conexa.errors.create_error"), nil, {}))
      end

      it "must render error" do
        post "/api/v2/users/telemedicine/subscription", headers: headers, params: {birthdate: 1.day.ago, sex: "MALE"}
        expect(response).to be_unprocessable

        expect(TelemedicineMailer.deliveries.count).to eq(0)
        expect(authorized_user.telemedicine_activities.where(entry_type: :subscribed).count).to eq(0)

        expect(TelemedicineBeneficiary.count).to eq(0)
      end
    end

    context "when user is not elegible to telemedicine" do
      it "must be forbidden" do
        authorized_user.reload.update!(telemedicine: false)
        post("/api/v2/users/telemedicine/subscription", headers:)

        expect(response).to be_forbidden
      end
    end

    context "when business has reached contracted beneficiaries limit" do
      before do
        authorized_user.update!(telemedicine: true)
        another_authorized_user = create(:authorized_user, business:)
        create(:telemedicine_beneficiary, authorized_user: another_authorized_user)
        business.telemedicine_config.update_columns(contracted_beneficiaries: 1)
      end

      it "must be forbidden" do
        post("/api/v2/users/telemedicine/subscription", headers:)

        expect(response).to be_forbidden
      end
    end

    context "when business does not have active telemedicine" do
      let!(:business) { create(:business, telemedicine: false) }
      let!(:telemedicine_beneficiary) { create(:telemedicine_beneficiary, :active, cpf: authorized_user.cpf) }
      before do
        authorized_user.update!(telemedicine: true)
      end

      it "must be forbidden" do
        post("/api/v2/users/telemedicine/subscription", headers:)

        expect(response).to be_forbidden
      end
    end
  end
end
