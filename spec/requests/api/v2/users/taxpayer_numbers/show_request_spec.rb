# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::V2::Users::TaxpayerNumberValidationsController, type: :request do
  let!(:business) { create(:business) }
  let(:cpf) { FFaker::IdentificationBR.cpf }

  let(:auth_headers) { {"Api-Secret" => business.api_secret} }
  let(:response_hash) { JSON.parse(response.body) }

  describe "#show" do
    context "when cpf is valid" do
      it "render succesfull" do
        get "/api/v2/users/taxpayer_number_validation", headers: auth_headers, params: {taxpayer_number: cpf}

        expect(response).to have_http_status(:ok)
        expect(response_hash["message"]).to eq("Documento válido")
      end
    end

    context "when user cpf is in use" do
      let!(:user) { create(:user, cpf:, business:) }

      it "render unprocessable entity" do
        get "/api/v2/users/taxpayer_number_validation", headers: auth_headers, params: {taxpayer_number: cpf}

        expect(response).to have_http_status(:unprocessable_entity)
        expect(response_hash["error"]).to eq("Documento já registrado")
      end

      context "when cpf is in use on another business" do
        let!(:user) { create(:user, cpf:) }

        it "render succesfull" do
          get "/api/v2/users/taxpayer_number_validation", headers: auth_headers, params: {taxpayer_number: cpf}

          expect(response).to have_http_status(:ok)
          expect(response_hash["message"]).to eq("Documento válido")
        end

        context "when is related business" do
          let(:related_business) { create(:business, main_business: business) }
          let(:user) { create(:user, cpf:, business: related_business) }

          it "must render unprocessable entity" do
            get "/api/v2/users/taxpayer_number_validation", headers: auth_headers, params: {taxpayer_number: cpf}

            expect(response).to have_http_status(:unprocessable_entity)
            expect(response_hash["error"]).to eq("Documento já registrado")
          end
        end
      end
    end
  end
end
