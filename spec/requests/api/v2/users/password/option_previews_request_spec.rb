# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::V2::Users::Password::OptionPreviewsController, type: :request do
  let!(:business) { create(:business) }

  let(:auth_headers) { {"Api-Secret" => business.api_secret} }
  let(:response_hash) { JSON.parse(response.body) }

  describe "#show" do
    context "when exist a user on business with searched cpf" do
      let(:user) { create(:user, business:, email: "<EMAIL>") }
      it "render hidden email value" do
        get "/api/v2/users/password/option_previews", headers: auth_headers, params: {taxpayer_number: user.cpf}

        expect(response).to have_http_status(:ok)
        expect(response_hash["email_preview"]).to eq("t******<EMAIL>")
      end

      context "when cpf is on a related sub business" do
        let(:related_business) { create(:business, main_business: business) }
        let(:user) { create(:user, business: related_business, email: "<EMAIL>") }

        it "render succesfull" do
          get "/api/v2/users/password/option_previews", headers: auth_headers, params: {taxpayer_number: user.cpf}

          expect(response).to have_http_status(:ok)
          expect(response_hash["email_preview"]).to eq("t******<EMAIL>")
        end
      end
    end

    context "when does not exist a user on business with searched cpf" do
      let!(:user) { create(:user) }

      it "render unprocessable entity" do
        get "/api/v2/users/password/option_previews", headers: auth_headers, params: {taxpayer_number: user.cpf}

        expect(response).to have_http_status(:unprocessable_entity)
        expect(response_hash["error"]).to eq("CPF não encontrado")
      end
    end
  end
end
