# frozen_string_literal: true

require "rails_helper"
require "sidekiq/testing"

RSpec.describe Api::V2::RedeemController, :vcr, type: :request do
  include ActiveSupport::Testing::TimeHelpers

  let(:business) { create(:business, lbc_giftcard: false) }
  let(:user) { create(:user, business:) }
  let!(:headers) do
    Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret}, user)
  end

  describe "#create" do
    context "given a active organization and branch" do
      let(:active_organization) { create(:organization, active: true, promotion_redeemable: true) }
      let(:active_branch_online) do
        create(
          :branch,
          :online,
          organization: active_organization,
          cnpj: "32.839.587/0001-13",
          active: true
        )
      end

      let(:redeem_serializer_keys) do
        %w[
          id
          link
          redeem_code
          usage_instruction
          number
        ]
      end

      context "given a coupon CLASSIC and promotion provider: qrcode" do
        let(:create_coupon_classic) do
          promotion = create(:promotion, :available, provider: "qrcode", cashback_type: :fixed, cashback_value: 2)
          create(:cupon, :classic, promotion:)
        end

        it "renders created and does not return usage instruction when not sending app version" do
          coupon = create_coupon_classic
          params = {confirmation_key: coupon.branch.cnpj}

          expect do
            post "/api/v2/coupons/#{coupon.id}/redeem", headers:, params:
          end.to change(Order, :count).by(1)
            .and change(WebhookSender::EventWorker.jobs, :size).by(1)

          expect(response).to have_http_status(:created)
          order = Order.order(:created_at).last
          expect(order.cashback_type).to eq("fixed")
          expect(order.cashback_value).to eq(2)
          expect(response.parsed_body["link"]).to be_nil
          expect(response.parsed_body["redeem_code"]).not_to be_nil
          expect(response.parsed_body["usage_instruction"]).to be_nil
          expect(response.parsed_body.keys).to match_array(redeem_serializer_keys)
        end

        it "renders created and does not return usage instruction when app version is equal to compatible serializer version" do
          coupon = create_coupon_classic
          params = {confirmation_key: coupon.branch.cnpj}
          headers["App-Version"] = "3.2.1"

          expect do
            post "/api/v2/coupons/#{coupon.id}/redeem", headers:, params:
          end.to change(Order, :count).by(1)
            .and change(WebhookSender::EventWorker.jobs, :size).by(1)

          expect(response).to have_http_status(:created)
          order = Order.order(:created_at).last
          expect(order.cashback_type).to eq("fixed")
          expect(order.cashback_value).to eq(2)
          expect(response.parsed_body["link"]).to be_nil
          expect(response.parsed_body["redeem_code"]).not_to be_nil
          expect(response.parsed_body["usage_instruction"]).to be_nil
          expect(response.parsed_body.keys).to match_array(redeem_serializer_keys)
        end

        it "renders created and does not return usage instruction when app version is less than compatible serializer version" do
          coupon = create_coupon_classic
          params = {confirmation_key: coupon.branch.cnpj}
          headers["App-Version"] = "3.2.0"

          expect do
            post "/api/v2/coupons/#{coupon.id}/redeem", headers:, params:
          end.to change(Order, :count).by(1)
            .and change(WebhookSender::EventWorker.jobs, :size).by(1)

          expect(response).to have_http_status(:created)
          order = Order.order(:created_at).last
          expect(order.cashback_type).to eq("fixed")
          expect(order.cashback_value).to eq(2)
          expect(response.parsed_body["link"]).to be_nil
          expect(response.parsed_body["redeem_code"]).not_to be_nil
          expect(response.parsed_body["usage_instruction"]).to be_nil
          expect(response.parsed_body.keys).to match_array(redeem_serializer_keys)
        end

        it "renders created and returns usage instruction when sending app version greater than compatible" do
          coupon = create_coupon_classic
          params = {confirmation_key: coupon.branch.cnpj}
          headers["App-Version"] = "3.2.2"

          expect do
            post "/api/v2/coupons/#{coupon.id}/redeem", headers:, params:
          end.to change(Order, :count).by(1)
            .and change(WebhookSender::EventWorker.jobs, :size).by(1)

          expect(response).to have_http_status(:created)
          order = Order.order(:created_at).last
          expect(order.cashback_type).to eq("fixed")
          expect(order.cashback_value).to eq(2)
          expect(response.parsed_body["link"]).to be_nil
          expect(response.parsed_body["redeem_code"]).not_to be_nil
          expect(response.parsed_body["usage_instruction"]).to eq("Você resgatou seu cupom com sucesso!")
          expect(response.parsed_body.keys).to match_array(redeem_serializer_keys + ["navigation_option", "status"])
        end

        it "an unsuccessful redemption is expected" do
          coupon = create_coupon_classic

          expect do
            post "/api/v2/coupons/#{coupon.id}/redeem", headers:, params: {confirmation_key: "11111110001111"}
          end.to change(Order, :count).by(0)
            .and change(WebhookSender::EventWorker.jobs, :size).by(0)

          expect(response).to have_http_status(:unprocessable_entity)
          expect(response.parsed_body["error"]).to eq("Cupom inválido para esse CNPJ")
        end
      end

      context "given a coupon CPF and promotion provider: cpf" do
        it "is expected a successful redemptionwith versioned serializer" do
          coupon = create(:cupon, :cpf, branch: active_branch_online)

          expect do
            post "/api/v2/coupons/#{coupon.id}/redeem", headers:
          end.to change(Order, :count).by(1)
            .and change(WebhookSender::EventWorker.jobs, :size).by(1)

          expect(response).to have_http_status(:created)
          expect(response.parsed_body["link"]).to be_nil
          expect(response.parsed_body["redeem_code"]).to eq(CPF.new(user.cpf).formatted)
          expect(response.parsed_body["usage_instruction"]).to be_nil
          expect(response.parsed_body.keys).to match_array(redeem_serializer_keys)
        end

        it "is expected a successful redemption with default serializer" do
          headers["App-Version"] = "3.2.2"
          coupon = create(:cupon, :cpf, branch: active_branch_online)

          expect do
            post "/api/v2/coupons/#{coupon.id}/redeem", headers: headers
          end.to change(Order, :count).by(1)
            .and change(WebhookSender::EventWorker.jobs, :size).by(1)

          expect(response).to have_http_status(:created)
          expect(response.parsed_body["link"]).to be_nil
          expect(response.parsed_body["redeem_code"]).to eq(CPF.new(user.cpf).formatted)
          expect(response.parsed_body["usage_instruction"]).to eq("Para validar a promoção, solicitamos que você apresente o seu CPF durante a compra. Fique atento às regras da promoção e boas compras!")
          expect(response.parsed_body.keys).to match_array(redeem_serializer_keys + ["navigation_option", "status"])
        end
      end

      context "given a coupon DYNAMIC and promotion provider: dynamic" do
        it "is expected a successful redemption" do
          bucket = create(:voucher_bucket)
          promotion = create(:promotion, :available, :dynamic, dynamic_voucher: true, voucher_bucket: bucket)
          coupon = create(:cupon, promotion:, branch: active_branch_online)

          expect do
            post "/api/v2/coupons/#{coupon.id}/redeem", headers:
          end.to change(Order, :count).by(1)
            .and change(WebhookSender::EventWorker.jobs, :size).by(1)

          expect(response).to have_http_status(:created)
          expect(response.parsed_body["link"]).to be_nil
          expect(response.parsed_body["redeem_code"]).not_to be_nil
        end
      end

      context "given a coupon DYNAMIC_ONLINE and promotion provider: dynamic_coupon_code" do
        it "is expected a successful redemption" do
          bucket = create(:voucher_bucket)
          promotion = create(:promotion, :available, :dynamic_coupon_code, dynamic_voucher: true, voucher_bucket: bucket)
          coupon = create(:cupon, :dynamic_online, promotion:, branch: active_branch_online)

          expect do
            post "/api/v2/coupons/#{coupon.id}/redeem", headers:
          end.to change(Order, :count).by(1)
            .and change(WebhookSender::EventWorker.jobs, :size).by(1)
          expect(response).to have_http_status(:created)
          expect(response.parsed_body["link"]).not_to be_nil
          expect(response.parsed_body["redeem_code"]).not_to be_nil
        end

        context "when dynamic_voucher is false" do
          it "is expected a successful redemption" do
            headers["App-Version"] = "3.2.2"

            bucket = create(:voucher_bucket)
            promotion = create(:promotion, :dynamic_coupon_code, :available, dynamic_voucher: false, voucher_bucket: bucket, web_postos: true)
            voucher = create(:voucher, bucket:)
            coupon = create(:cupon, :dynamic_online, promotion:, branch: active_branch_online)

            expect do
              post "/api/v2/coupons/#{coupon.id}/redeem", headers:
            end.to change(Order, :count).by(1)
              .and change { voucher.reload.order_id }.from(nil).to(be_present)
              .and change(WebhookSender::EventWorker.jobs, :size).by(1)
              .and change(Order::WebPostosWorker.jobs, :size).by(1)

            expect(response).to have_http_status(:created)
            expect(response.parsed_body["link"]).not_to be_nil
            expect(response.parsed_body["redeem_code"]).not_to be_nil
            expect(response.parsed_body["redeem_code"]).not_to be_nil
            expect(response.parsed_body["status"]).to eq("pending")
          end

          it "when web_postos flag is true create order how pending" do
            headers["App-Version"] = "3.2.2"

            Sidekiq::Testing.inline! do
              bucket = create(:voucher_bucket)
              promotion = create(:promotion, :dynamic_coupon_code, :available, dynamic_voucher: false, voucher_bucket: bucket, web_postos: true)
              voucher = create(:voucher, bucket:)
              coupon = create(:cupon, :dynamic_online, promotion:, branch: active_branch_online)

              expect do
                post "/api/v2/coupons/#{coupon.id}/redeem", headers:
              end.to change(Order, :count).by(1)
                .and change { voucher.reload.order_id }.from(nil).to(be_present)
              expect(response).to have_http_status(:created)
              expect(response.parsed_body["status"]).to eq("pending")

              travel_to 7.minutes.from_now do
                order = Order.find response.parsed_body["id"]
                expect(order).to be_status_canceled
                expect(order.voucher.canceled_at).not_to be_nil
              end
            end
          end

          it "an unsuccessful redemption is expected" do
            promotion = create(:promotion, :dynamic_coupon_code, :no_voucher, code: nil, dynamic_voucher: false)
            coupon = create(:cupon, :dynamic_online, promotion:, branch: active_branch_online)

            expect do
              post "/api/v2/coupons/#{coupon.id}/redeem", headers:
            end.to change(Order, :count).by(0)
              .and change(WebhookSender::EventWorker.jobs, :size).by(0)

            expect(response).to have_http_status(:not_found)
            expect(response.parsed_body["error"]).to eq("Registro(s) não encontrado(s).")
          end
        end
      end

      context "given a coupon FIXED and promotion provider: coupon_code" do
        it "is expected a successful redemption" do
          bucket = create(:voucher_bucket)
          promotion_coupon_code = create(:promotion, :coupon_code, :available, code: nil, voucher_bucket: bucket)
          voucher = create(:voucher, bucket:)
          coupon = create(:cupon, :fixed, promotion: promotion_coupon_code, branch: active_branch_online)

          expect do
            post "/api/v2/coupons/#{coupon.id}/redeem", headers:
          end.to change(Order, :count).by(1)
            .and change { voucher.reload.order_id }.from(nil).to(be_present)

          expect(response).to have_http_status(:created)
          expect(response.parsed_body["link"]).to be_nil
          expect(response.parsed_body["redeem_code"]).not_to be_nil
        end

        it "an unsuccessful redemption is expected" do
          promotion = create(:promotion, :coupon_code, :no_voucher, code: nil)
          coupon = create(:cupon, :fixed, promotion:, branch: active_branch_online)

          expect do
            post "/api/v2/coupons/#{coupon.id}/redeem", headers:
          end.to change(Order, :count).by(0)

          expect(response).to have_http_status(:not_found)
          expect(response.parsed_body["error"]).to eq("Registro(s) não encontrado(s).")
        end

        context "when try to redeem a coupom that has already been redeemed" do
          it "is expected a error on redemption" do
            bucket = create(:voucher_bucket)
            promotion_coupon_code = create(:promotion, :coupon_code, :available, voucher_bucket: bucket)
            voucher = create(:voucher, bucket:)
            coupon = create(:cupon, :fixed, promotion: promotion_coupon_code, branch: active_branch_online)
            create(:order, cupon: coupon, voucher:)

            expect do
              post "/api/v2/coupons/#{coupon.id}/redeem", headers:
            end.to change(Order, :count).by(0)

            expect(response).to have_http_status(:unprocessable_entity)
            expect(response.parsed_body["error"]).to eq("Voucher não disponível para resgate")
          end
        end

        context "when try to redeem a coupom that has already been redeemed in period" do
          it "is expected a successful redemption" do
            travel_to(Time.zone.parse("2023-08-30 08:35"))
            promotion = create(:promotion, :cpf, :available, frequency_in_days: 10, redeems_per_cpf: 2, closed_interval: false)
            coupon = create(:cupon, :cpf, promotion:, branch: active_branch_online, frequency_in_days: 10, redeems_per_cpf: 2)
            _order_one = create(:order, user:, cupon: coupon, created_at: "2023-08-20 08:34")
            _order_two = create(:order, user:, cupon: coupon, created_at: "2023-08-20 08:33")
            order_three = create(:order, user:, cupon: coupon, created_at: "2023-08-20 08:38")
            _order_four = create(:order, user:, cupon: coupon, created_at: "2023-08-20 08:37")

            expect do
              post "/api/v2/coupons/#{coupon.id}/redeem", headers:
            end.to change(Order, :count).by(0)

            expect(response).to have_http_status(:unprocessable_entity)
            expect(response.parsed_body["order_id"]).to eq(order_three.id)
            expect(response.parsed_body["error"]).to eq("Esta promoção só pode ser ativada novamente a cada 10 dia(s).")
          end
        end
      end

      context "given a coupon FIXED_CODE and promotion provider: fixed_code" do
        it "is expected a successful redemption" do
          coupon = create(:cupon, :fixed_code, branch: active_branch_online)

          expect do
            post "/api/v2/coupons/#{coupon.id}/redeem", headers:
          end.to change(Order, :count).by(1)

          expect(response).to have_http_status(:created)
          expect(response.parsed_body["link"]).to be_nil
          expect(response.parsed_body["redeem_code"]).not_to be_nil
        end
      end

      context "given a coupon LBC_GIFTCARD and promotion provider: lbc_giftcard" do
        let(:promotion_lbc_giftcard) { create(:promotion, :lbc_giftcard, :no_voucher, code: nil) }
        let(:create_coupon_lbc_giftcard) do
          create(:cupon, :lbc_giftcard, promotion: promotion_lbc_giftcard, branch: active_branch_online)
        end

        context "when there is no voucher available" do
          it "an unsuccessful redemption is expected" do
            business.update!(lbc_giftcard: true)
            coupon = create_coupon_lbc_giftcard

            expect do
              post "/api/v2/coupons/#{coupon.id}/redeem", headers:
            end.to change(Order, :count).by(0)

            expect(response).to have_http_status(:not_found)
            expect(response.parsed_body["error"]).to eq("Registro(s) não encontrado(s).")
          end
        end

        context "when there is a voucher available" do
          it "is expected a successful redemption" do
            business.update!(lbc_giftcard: true)
            bucket = create(:voucher_bucket)
            promotion_lbc_giftcard.update_columns(voucher_bucket_id: bucket.id)
            voucher = create(:voucher, bucket:)
            coupon = create_coupon_lbc_giftcard
            promotion_lbc_giftcard.propagate_changes

            expect do
              post "/api/v2/coupons/#{coupon.id}/redeem", headers:
            end.to change(Order, :count).by(1)
              .and change { voucher.reload.order_id }.from(nil).to(be_present)

            expect(response).to have_http_status(:created)
            expect(response.parsed_body.keys).to match_array(redeem_serializer_keys)
          end

          context "when lbc_giftcard is false" do
            it "an unsuccessful redemption is expected" do
              business.update!(lbc_giftcard: false)
              bucket = create(:voucher_bucket)
              promotion_lbc_giftcard.update_columns(voucher_bucket_id: bucket.id)
              create(:voucher, bucket:)
              coupon = create_coupon_lbc_giftcard
              promotion_lbc_giftcard.propagate_changes

              expect do
                post "/api/v2/coupons/#{coupon.id}/redeem", headers:
              end.to change(Order, :count).by(0)

              expect(response.parsed_body["error"])
                .to eq("Esta promoção não está ativa pois a opção Promoção de postos foi desabilitada pelo seu gestor")
              expect(response).to have_http_status(:unprocessable_entity)
            end
          end
        end
      end

      context "given a coupon ONLINE" do
        context "and promotion: online with code" do
          let(:promotion_online) { create(:promotion, :online, :available) }

          it "is expected a successful redemption" do
            url = FFaker::Internet.http_url
            coupon = create(:cupon, c_type: "ONLINE", url:, branch: active_branch_online, promotion: promotion_online)

            expect do
              post "/api/v2/coupons/#{coupon.id}/redeem", headers:
            end.to change(Order, :count).by(1)
              .and change(WebhookSender::EventWorker.jobs, :size).by(1)

            expect(response).to have_http_status(:created)
            expect(response.parsed_body["redeem_code"]).not_to be_nil
            expect(response.parsed_body["link"]).to eq(url)
            expect(response.parsed_body["usage_instruction"]).to be_nil
          end
        end

        context "without code" do
          let(:url) { FFaker::Internet.http_url }
          let(:promotion) { create(:promotion, :online, :available, url:, code: nil) }
          let(:coupon) { create(:cupon, :online, url:, branch: active_branch_online, promotion:, code: nil) }

          it "renders successful and redeems coupon" do
            expect do
              post("/api/v2/coupons/#{coupon.id}/redeem", headers:)
            end.to change(Order, :count).by(1)
              .and change(WebhookSender::EventWorker.jobs, :size).by(1)

            expect(response).to be_created
            expect(response.parsed_body["redeem_code"]).to be_nil
            expect(response.parsed_body["link"]).to eq(url)
            expect(response.parsed_body["usage_instruction"]).to be_nil
          end
        end

        context "and provider: regionalized" do
          let(:promotion_regionalized) { create(:promotion, :regionalized, :available) }

          it "is expected a successful redemption" do
            url = FFaker::Internet.http_url
            coupon = create(:cupon, c_type: "ONLINE", url:, branch: active_branch_online, promotion: promotion_regionalized)

            expect do
              post "/api/v2/coupons/#{coupon.id}/redeem", headers:
            end.to change(Order, :count).by(1)
              .and change(WebhookSender::EventWorker.jobs, :size).by(1)

            expect(response).to have_http_status(:created)
            expect(response.parsed_body["redeem_code"]).not_to be_nil
            expect(response.parsed_body["link"]).to eq(url)
          end
        end

        context "and provider: whatsapp" do
          let(:promotion_whatsapp) { create(:promotion, :whatsapp, :available) }

          it "is expected a successful redemption" do
            url = FFaker::Internet.http_url
            coupon = create(:cupon, c_type: "ONLINE", url:, branch: active_branch_online, promotion: promotion_whatsapp)

            expect do
              post "/api/v2/coupons/#{coupon.id}/redeem", headers:
            end.to change(Order, :count).by(1)
              .and change(WebhookSender::EventWorker.jobs, :size).by(1)

            expect(response).to have_http_status(:created)
            expect(response.parsed_body["redeem_code"]).not_to be_nil
            expect(response.parsed_body["link"]).to eq(url)
          end
        end
      end

      context "when is embedded project" do
        let(:business) { create(:business, :with_cashback, embedded: true) }
        let(:create_coupon_classic) do
          create(:cupon, :classic, branch: active_branch_online)
        end
        let(:headers) do
          Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret}, user)
        end

        it "must create order with confirmation_key as branch cnpj" do
          coupon = create_coupon_classic
          expect(Order).to receive(:new).with(hash_including(confirmation_key: coupon.branch.cnpj)).and_call_original

          expect do
            post "/api/v2/coupons/#{coupon.id}/redeem", headers:, params: {confirmation_key: "any_other_value"}
          end.to change(Order, :count).by(1)
            .and change(WebhookSender::EventWorker.jobs, :size).by(1)

          expect(response).to have_http_status(:created)
        end
      end

      context "given a inactive coupon" do
        let(:create_inactive_coupon) do
          create(:cupon, :percent_discount, branch: active_branch_online, active: false)
        end

        specify do
          coupon = create_inactive_coupon

          post("/api/v2/coupons/#{coupon.id}/redeem", headers:)

          expect(response).to have_http_status(:not_found)
        end
      end
    end

    context "given a inactive organization" do
      let(:inactive_organization) { create(:organization, active: false, promotion_redeemable: true) }
      let(:active_branch_online) { create(:branch, :online, organization: inactive_organization, active: true) }
      let(:promotion) { create(:promotion, :available, :percent_discount, redeem_type: Enums::PromotionRedeemType::DISCOUNT) }
      let(:create_coupon) { create(:cupon, :percent_discount, branch: active_branch_online, promotion:) }

      specify do
        coupon = create_coupon

        post("/api/v2/coupons/#{coupon.id}/redeem", headers:)

        expect(response).to have_http_status(:not_found)
      end
    end

    context "given a inactive branch" do
      let(:active_organization) { create(:organization, active: true) }
      let(:inactive_branch_online) { create(:branch, :online, organization: active_organization, active: false) }
      let(:promotion) { create(:promotion, :available) }
      let(:create_coupon) { create(:cupon, branch: inactive_branch_online, promotion:) }

      specify do
        coupon = create_coupon

        post("/api/v2/coupons/#{coupon.id}/redeem", headers:)

        expect(response).to have_http_status(:not_found)
      end
    end

    context "given a organization blocklisted" do
      let(:active_organization) { create(:organization, active: true) }
      let(:active_branch_online) { create(:branch, :online, organization: active_organization, active: true) }
      let(:promotion) { create(:promotion, :available) }
      let(:create_coupon) { create(:cupon, branch: active_branch_online, promotion:) }

      specify do
        coupon = create_coupon
        create(:organization_blocklist, business:, organization: active_organization)

        post("/api/v2/coupons/#{coupon.id}/redeem", headers:)

        expect(response).to have_http_status(:not_found)
      end
    end

    context "given a category blocklisted" do
      let!(:category) { create(:category) }
      let!(:active_organization) { create(:organization, active: true, categories: [category]) }
      let(:active_branch_online) { create(:branch, :online, organization: active_organization, active: true) }
      let(:promotion) { create(:promotion, :available) }
      let(:create_coupon) { create(:cupon, branch: active_branch_online, promotion:) }

      specify do
        coupon = create_coupon
        create(:category_blocklist, business:, category:)

        post("/api/v2/coupons/#{coupon.id}/redeem", headers:)

        expect(response).to have_http_status(:not_found)
      end
    end
  end
end
