# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::V2::WidgetsController, type: :request do
  let(:currency) { "BRL" }
  let(:cnpj) { FFaker::IdentificationBR.cnpj }
  let(:business) { create(:business, :with_cashback, currency:, giftcard: true, fair_value: 0.035, home_banner: true, cnpj:) }
  let(:user) { create(:user, business:) }
  let(:aud) { "web" }
  let(:headers) do
    Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret, "aud" => aud}, user)
  end
  let(:response_hash) { JSON.parse(response.body) }

  describe "GET /api/v2/widgets" do
    let!(:category) { create(:category) }

    let!(:organization_percent) { create_organization(highest_cashback_type: :percent, highest_cashback_value: 3.5) }
    let!(:highlight_percent) { create(:highlight_organization, organization: organization_percent, business:) }
    let!(:organization_fixed) { create_organization(highest_cashback_type: :fixed, highest_cashback_value: 5.5) }
    let!(:highlight_fixed) { create(:highlight_organization, organization: organization_fixed, business:) }
    let!(:organization_discount) { create_organization(highest_discount_type: :fixed, highest_discount_value: 10) }
    let!(:highlight_discount) { create(:highlight_organization, organization: organization_discount, business:) }
    let!(:organization_benefit) { create_organization }
    let!(:highlight_benefit) { create(:highlight_organization, organization: organization_benefit, business:) }
    let!(:new_organization) do
      create_organization(
        highest_cashback_type: :percent,
        highest_cashback_value: 3.5,
        highest_discount_type: :percent,
        highest_discount_value: 10,
        created_at: DateTime.current
      )
    end
    let!(:new_organization_2) do
      create_organization(
        highest_discount_type: :fixed,
        highest_discount_value: 5.5,
        created_at: DateTime.current
      )
    end

    let(:widget_serializer_keys) { %w[title subtitle template items destination_url] }
    let(:widget_item_serializer_keys) { %w[title image destination_url badges open_new_tab external navigation_option] }
    let(:widget_item_image_serializer_keys) { %w[small large] }
    let(:widget_item_open_new_tab_serializer_keys) { %w[mobile web] }
    let(:widget_badge_serializer_keys) { %w[type text] }

    describe "online" do
      let(:templates) do
        %w[
          carousel_banner
          horizontal_list_one
          horizontal_list_two
          static_banner
          horizontal_list_three
          static_banner
          favorites
          wallet_widget
        ]
      end

      context "when currency is cashback" do
        let!(:banner) { create(:banner, :organization_coupon, business:, metadata: {organization_id: 1}) }

        it "renders ok" do
          get("/api/v2/widgets", headers:)

          expect(response).to be_ok
          expect(response_hash.map(&:keys)).to all match_array widget_serializer_keys
          expect(response_hash.pluck("template")).to eq templates
          expect(response_hash.pluck("items").flatten.pluck("image"))
            .to include(
              "small" => "http://example.com/images/home_widgets/mobile/nearby_deals.svg",
              "large" => "http://example.com/images/home_widgets/web/nearby_deals.svg"
            )
            .and not_include(
              "small" => "http://example.com/images/home_widgets/mobile/nearby_deals-black.svg",
              "large" => "http://example.com/images/home_widgets/web/nearby_deals-black.svg"
            )
            .and include(
              "small" => "http://example.com/images/home_widgets/mobile/gift_cards-cashback.svg",
              "large" => "http://example.com/images/home_widgets/web/gift_cards-cashback.svg"
            )
          expect(response_hash.pluck("items").flatten.map(&:keys)).to all match_array widget_item_serializer_keys
          expect(response_hash.pluck("items").flatten.pluck("image").map(&:keys)).to all match_array widget_item_image_serializer_keys
          expect(response_hash.pluck("items").flatten.pluck("open_new_tab").map(&:keys)).to all match_array widget_item_open_new_tab_serializer_keys
          expect(response_hash.pluck("items").flatten.pluck("badges").flatten).to match_array([
            {"text" => "Cashback 3,5%", "type" => "cashback"},
            {"text" => "Cashback R$ 5,50", "type" => "cashback"},
            {"text" => "R$ 10 Off", "type" => "discount"},
            {"text" => "Benefício", "type" => "benefit"},
            {"text" => "Cashback 3,5%", "type" => "cashback"},
            {"text" => "Cashback 3,5%", "type" => "cashback"},
            {"text" => "Cashback R$ 5,50", "type" => "cashback"},
            {"text" => "R$ 10 Off", "type" => "discount"},
            {"text" => "Benefício", "type" => "benefit"},
            {"text" => "R$ 5,50 Off", "type" => "discount"}
          ])
        end

        context "when business has different banner color" do
          let(:cnpj) { ["52177416000183", "52177416000184"].sample }

          it "renders ok" do
            get("/api/v2/widgets", headers:)

            expect(response).to be_ok
            expect(response_hash.map(&:keys)).to all match_array widget_serializer_keys
            expect(response_hash.pluck("items").flatten.pluck("image"))
              .to include(
                "small" => "http://example.com/images/home_widgets/mobile/nearby_deals-black.svg",
                "large" => "http://example.com/images/home_widgets/web/nearby_deals-black.svg"
              )
              .and not_include(
                "small" => "http://example.com/images/home_widgets/mobile/nearby_deals.svg",
                "large" => "http://example.com/images/home_widgets/web/nearby_deals.svg"
              )
          end
        end
      end

      context "when currency is points" do
        let!(:banner) { create(:banner, :organization_coupon, business:, metadata: {organization_id: 1}) }
        let(:currency) { "points" }

        it "renders ok" do
          get("/api/v2/widgets", headers:)

          expect(response).to be_ok
          expect(response_hash.map(&:keys)).to all match_array widget_serializer_keys
          expect(response_hash.pluck("template")).to eq templates
          expect(response_hash.pluck("items").flatten.map(&:keys)).to all match_array widget_item_serializer_keys
          expect(response_hash.pluck("items").flatten.pluck("image").map(&:keys)).to all match_array widget_item_image_serializer_keys
          expect(response_hash.pluck("items").flatten.pluck("open_new_tab").map(&:keys)).to all match_array widget_item_open_new_tab_serializer_keys

          expect(response_hash.pluck("items").flatten.pluck("badges").flatten).to match_array([
            {"text" => "R$ 1 = 1pts", "type" => "point"},
            {"text" => "157 pontos", "type" => "point"},
            {"text" => "R$ 10 Off", "type" => "discount"},
            {"text" => "Benefício", "type" => "benefit"},
            {"text" => "R$ 1 = 1pts", "type" => "point"},
            {"text" => "R$ 1 = 1pts", "type" => "point"},
            {"text" => "157 pontos", "type" => "point"},
            {"text" => "R$ 10 Off", "type" => "discount"},
            {"text" => "Benefício", "type" => "benefit"},
            {"text" => "R$ 5,50 Off", "type" => "discount"}
          ])

          expect(response_hash.pluck("items").flatten.pluck("image")).to include(
            "small" => "http://example.com/images/home_widgets/mobile/gift_cards-points.svg",
            "large" => "http://example.com/images/home_widgets/web/gift_cards-points.svg"
          )
        end
      end

      context "when cashback is disabled" do
        let!(:banner) { create(:banner, :organization_coupon, business:, metadata: {organization_id: 1}) }
        let(:business) { create(:business, cashback: false, giftcard: true, home_banner: true) }

        it "renders ok" do
          get("/api/v2/widgets", headers:)

          expect(response).to be_ok
          expect(response_hash.map(&:keys)).to all match_array widget_serializer_keys
          expect(response_hash.pluck("template")).to eq templates - ["wallet_widget"]
          expect(response_hash.pluck("items").flatten.pluck("badges").flatten).to match_array([
            {"text" => "Benefício", "type" => "benefit"},
            {"text" => "Benefício", "type" => "benefit"},
            {"text" => "R$ 10 Off", "type" => "discount"},
            {"text" => "Benefício", "type" => "benefit"},
            {"text" => "Benefício", "type" => "benefit"},
            {"text" => "10% Off", "type" => "discount"},
            {"text" => "Benefício", "type" => "benefit"},
            {"text" => "R$ 10 Off", "type" => "discount"},
            {"text" => "Benefício", "type" => "benefit"},
            {"text" => "R$ 5,50 Off", "type" => "discount"}
          ])

          expect(response_hash.pluck("items").flatten.pluck("image")).to include(
            "small" => "http://example.com/images/home_widgets/mobile/gift_cards.svg",
            "large" => "http://example.com/images/home_widgets/web/gift_cards.svg"
          )
        end
      end

      context "when giftcard is disabled" do
        let!(:banner) { create(:banner, :organization_coupon, business:, metadata: {organization_id: 1}) }
        let(:business) { create(:business, :with_cashback, currency:, giftcard: false, home_banner: true) }

        let(:templates) do
          %w[
            carousel_banner
            horizontal_list_one
            horizontal_list_two
            static_banner
            horizontal_list_three
            favorites
            wallet_widget
          ]
        end

        it "renders ok" do
          get("/api/v2/widgets", headers:)

          expect(response).to be_ok
          expect(response_hash.map(&:keys)).to all match_array widget_serializer_keys
          expect(response_hash.pluck("template")).to eq templates
        end
      end

      context "when business has no banner" do
        it "renders ok" do
          get("/api/v2/widgets", headers:)

          expect(response).to be_ok
          expect(response_hash.map(&:keys)).to all match_array widget_serializer_keys
          expect(response_hash.pluck("template")).to eq templates - ["carousel_banner"]
        end
      end

      context "when the carousel has an external link banner" do
        let!(:banner) { create(:banner, :external_link, title: "external_banner", business:, metadata: {organization_id: 1}, open_new_tab_on_web: false, mobile_navigation_option: "web_view") }

        let(:aud) { "api" }

        before { business.update(home_banner: true) }

        it "renders the item with external true" do
          get("/api/v2/widgets", headers:)

          expect(response).to be_ok
          carousel = response_hash.find { |widget| widget["template"] == "carousel_banner" }
          banner = carousel["items"].find { |item| item["title"] == "external_banner" }
          expect(banner["external"]).to be true
          expect(banner["navigation_option"]).to eq "web_view"
        end
      end
    end

    describe "physical" do
      let!(:organization_physical) do
        create_organization(
          highest_discount_type: :percent,
          highest_discount_value: 15,
          branch_type: Enums::Branch::Type::PHYSICAL,
          lat: 1.0,
          lng: 1.0
        )
      end
      let!(:highlight_physical) { create(:highlight_organization, organization: organization_physical, business:) }

      let!(:organization_far) do
        create_organization(
          highest_discount_type: :percent,
          highest_discount_value: 20,
          branch_type: Enums::Branch::Type::PHYSICAL,
          lat: 30.0,
          lng: 30.0
        )
      end
      let!(:highlight_far) { create(:highlight_organization, organization: organization_far, business:) }

      let!(:drugstore_category) { create(:category, id: 19) }
      let!(:drugstore_subcategory) { create(:category, main_category: drugstore_category) }
      let!(:gas_station_category) { create(:category, id: 18) }

      let!(:drugstore) do
        create_organization(
          categories: [drugstore_category],
          branch_type: Enums::Branch::Type::PHYSICAL,
          lat: 1.01,
          lng: 1.01
        )
      end

      let!(:drugstore_subcategory_organization) do
        create_organization(
          categories: [drugstore_subcategory],
          branch_type: Enums::Branch::Type::PHYSICAL,
          lat: 1.01,
          lng: 1.01
        )
      end
      let!(:gas_station) do
        create_organization(
          categories: [gas_station_category],
          branch_type: Enums::Branch::Type::PHYSICAL,
          lat: 1.0,
          lng: 1.0
        )
      end

      let(:params) { {distance: {lat: 1.0, lng: 1.0}} }

      context "when telemedicine is activated" do
        let(:business) { create(:business, :with_cashback, telemedicine: true) }
        let(:authorized_user) { create(:authorized_user, telemedicine: true) }
        let(:user) { create(:user, authorized_user:, business:) }

        let(:templates) do
          %w[
            carousel_banner
            horizontal_list_one
            horizontal_list_two
            horizontal_list_three
            horizontal_list_three
            wallet_widget
            favorites
            static_banner
            horizontal_list_three
          ]
        end

        it "renders ok" do
          get("/api/v2/widgets", headers:, params:)

          expect(response).to be_ok
          expect(response_hash.map(&:keys)).to all match_array widget_serializer_keys
          expect(response_hash.pluck("template")).to eq templates
          expect(response_hash.pluck("items").flatten.map(&:keys)).to all match_array widget_item_serializer_keys
          expect(response_hash.pluck("items").flatten.pluck("image").map(&:keys)).to all match_array widget_item_image_serializer_keys
          expect(response_hash.pluck("items").flatten.pluck("open_new_tab").map(&:keys)).to all match_array widget_item_open_new_tab_serializer_keys
          expect(response_hash.pluck("items").flatten.pluck("badges").flatten).to match_array([
            {"text" => "15% Off", "type" => "discount"},
            {"text" => "0Km", "type" => "distance"},
            {"text" => "15% Off", "type" => "discount"},
            {"text" => "0Km", "type" => "distance"},
            {"text" => "Benefício", "type" => "benefit"},
            {"text" => "0Km", "type" => "distance"},
            {"text" => "Benefício", "type" => "benefit"},
            {"text" => "1,6Km", "type" => "distance"},
            {"text" => "Benefício", "type" => "benefit"},
            {"text" => "1,6Km", "type" => "distance"},
            {"text" => "Benefício", "type" => "benefit"},
            {"text" => "0Km", "type" => "distance"},
            {"text" => "1,6Km", "type" => "distance"},
            {"text" => "1,6Km", "type" => "distance"},
            {"text" => "Benefício", "type" => "benefit"},
            {"text" => "Benefício", "type" => "benefit"}
          ])
        end
      end

      context "when telemedicine is disabled" do
        let(:business) { create(:business, :with_cashback, telemedicine: false) }

        let(:templates) do
          %w[
            carousel_banner
            horizontal_list_one
            horizontal_list_two
            horizontal_list_three
            horizontal_list_three
            wallet_widget
            favorites
            horizontal_list_three
          ]
        end

        it "renders ok" do
          get("/api/v2/widgets", headers:, params:)

          expect(response).to be_ok
          expect(response_hash.map(&:keys)).to all match_array widget_serializer_keys
          expect(response_hash.pluck("template")).to eq templates
        end
      end
    end
  end

  def create_organization(branch_type: Enums::Branch::Type::ONLINE, lat: nil, lng: nil, **params)
    created_at = params[:created_at] || 1.day.ago.beginning_of_day
    organization = create(:organization, active: true, promotion_redeemable: true, created_at:, **params)
    branch = create(:branch, branch_type:, organization:, lat:, lng:, promotion_redeemable: true)

    if branch_type == Enums::Branch::Type::ONLINE
      promotion = create(:promotion, :available, :online, organization:)
      create(:cupon, :online, promotion:, branch:)
    else
      promotion = create(:promotion, :available, :cpf, organization:)
      create(:cupon, :cpf, promotion:, branch:)
    end

    organization
  end
end
