# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::V2::ProjectConfigsController, type: :request do
  describe "GET /api/v2/project_config" do
    before do
      allow(Rails.application.credentials).to receive(:android_min_version).and_return("2.1")
      allow(Rails.application.credentials).to receive(:ios_min_version).and_return("2.2")
    end

    let!(:business) { create(:business, create_project_config: false) }
    let!(:project_config) do
      create(
        :project_config,
        :with_clever_tap,
        business:,
        android_version: "2.1",
        ios_version: "2.1",
        google_identifier: "br.com.android.vantagensclube",
        apple_identifier: "br.com.ios.vantagensclube",
        google_store_url: "https://play.google.com/store/apps/details?id=br.com.android.vantagensclube",
        apple_store_url: "https://apps.apple.com/app/vantagens-clube/id12345678"
      )
    end

    let(:auth_headers) do
      {"Api-Secret": business.api_secret, "App-Version": "4.0.5"}
    end
    let(:response_hash) { JSON.parse(response.body) }

    let(:support_info_keys) do
      %w[support support_url help_center_url giftcard_faq_url]
    end

    let(:giftcard_config_keys) do
      %w[limit_by_user limit_by_user_period_interval limit_by_user_period_type]
    end

    context "when scope param is 'web'" do
      let!(:serialized_keys) do
        %w[user_manager_info clevertap customization_info support_info giftcard_config project updated_at
          facebook_sdk facebook_sdk_app_id facebook_sdk_app_name facebook_sdk_client_token gtm_tag_id
          telemedicine_url home_config user_request_withdrawal azul_withdrawal]
      end

      let(:home_config_keys) do
        %w[search view_mode]
      end

      let(:user_manager_info_keys) do
        %w[user_manager sign_up_url user_update_url password_recovery_url single_sign_on_url single_sign_on_logout_url
          need_cellphone_on_registration need_email_on_registration]
      end

      let(:customization_info_keys) do
        %w[
          horizontal_white_logo_url
          vertical_logo_url
          term_of_use_url
          privacy_term_url
          name
          primary_color
          secondary_color
          background_color
          font_color
          favicon_url
          horizontal_logo_url
        ]
      end

      let(:project_keys) do
        %w[id banner cashback giftcard cashback_requestable_amount cashback_transfer_receiver
          member_referral web_domain wallet wallet_type embedded show_my_account prize_draw]
      end

      let(:clevertap_keys) do
        %w[clever_tap_account_id clever_tap_project_token clever_tap_region clever_tap_passcode]
      end

      it "must return fields related to web" do
        get "/api/v2/project_config", headers: auth_headers, params: {scope: "web"}

        expect(response).to be_successful
        expect(response_hash.keys).to match_array(serialized_keys)
        expect(response_hash["user_manager_info"].keys).to match_array(user_manager_info_keys)
        expect(response_hash["home_config"].keys).to match_array(home_config_keys)
        expect(response_hash["customization_info"].keys).to match_array(customization_info_keys)
        expect(response_hash["support_info"].keys).to match_array(support_info_keys)
        expect(response_hash["giftcard_config"].keys).to match_array(giftcard_config_keys)
        expect(response_hash["project"].keys).to match_array(project_keys)
        expect(response_hash["clevertap"].keys).to match_array(clevertap_keys)
      end

      it "must return fields related to web if business is inactive" do
        business.update_columns(active: false)
        get "/api/v2/project_config", headers: auth_headers, params: {scope: "web"}

        expect(response).to be_successful
        expect(response_hash.keys).to match_array(serialized_keys)
      end

      context "when app version is less or equal than 4.0.4" do
        let(:auth_headers) do
          {"Api-Secret": business.api_secret, "App-Version": "4.0.4"}
        end

        let!(:serialized_keys) do
          %w[user_manager_info clevertap customization_info support_info giftcard_config project updated_at
            facebook_sdk facebook_sdk_app_id facebook_sdk_app_name facebook_sdk_client_token gtm_tag_id
            telemedicine_url home_search user_request_withdrawal]
        end

        let(:project_keys) do
          %w[id banner cashback giftcard cashback_requestable_amount cashback_transfer_receiver
            member_referral web_domain wallet_type embedded prize_draw]
        end

        it "must return fields related to web" do
          get "/api/v2/project_config", headers: auth_headers, params: {scope: "web"}

          expect(response).to be_successful
          expect(response_hash.keys).to match_array(serialized_keys)
          expect(response_hash["user_manager_info"].keys).to match_array(user_manager_info_keys)
          expect(response_hash["customization_info"].keys).to match_array(customization_info_keys)
          expect(response_hash["support_info"].keys).to match_array(support_info_keys)
          expect(response_hash["giftcard_config"].keys).to match_array(giftcard_config_keys)
          expect(response_hash["project"].keys).to match_array(project_keys)
          expect(response_hash["clevertap"].keys).to match_array(clevertap_keys)
        end
      end
    end

    context "when scope param is 'mobile'" do
      let!(:serialized_keys) do
        %w[user_manager_info clevertap customization_info support_info giftcard_config store_info ux_cam project inngage_token updated_at
          facebook_sdk facebook_sdk_app_id facebook_sdk_app_name facebook_sdk_client_token gtm_tag_id
          telemedicine_url home_config user_request_withdrawal azul_withdrawal
          freshworks_app_id freshworks_app_key freshworks_domain]
      end

      let(:project_keys) do
        %w[id banner cashback giftcard cashback_requestable_amount cashback_transfer_receiver
          member_referral web_domain wallet wallet_type embedded show_my_account prize_draw]
      end

      let(:user_manager_info_keys) do
        %w[user_manager sign_up_url user_update_url password_recovery_url single_sign_on_url single_sign_on_logout_url
          need_cellphone_on_registration need_email_on_registration]
      end

      let(:customization_info_keys) do
        %w[
          horizontal_logo_url
          horizontal_white_logo_url
          vertical_logo_url
          term_of_use_url
          privacy_term_url
          name
          primary_color
          secondary_color
          background_color
          font_color
        ]
      end

      let(:store_info_keys) do
        %w[android ios]
      end

      let(:clevertap_keys) do
        %w[clever_tap_account_id clever_tap_project_token clever_tap_passcode clever_tap_region]
      end

      before do
        project_config.update_columns(clever_tap: false)
      end

      it "must return fields related to mobile" do
        get "/api/v2/project_config", headers: auth_headers, params: {scope: "mobile"}

        expect(response).to be_successful
        expect(response_hash.keys).to match_array(serialized_keys)

        expect(response_hash["project"].keys).to match_array(project_keys)
        expect(response_hash["user_manager_info"].keys).to match_array(user_manager_info_keys)
        expect(response_hash["customization_info"].keys).to match_array(customization_info_keys)
        expect(response_hash["support_info"].keys).to match_array(support_info_keys)
        expect(response_hash["giftcard_config"].keys).to match_array(giftcard_config_keys)
        expect(response_hash["clevertap"]).to be_nil

        expect(response_hash["store_info"].keys).to match_array(store_info_keys)
        expect(response_hash["store_info"]).to eq({
          "android" => {
            "version" => "2.1",
            "min_version" => "2.1",
            "check_update" => true,
            "store_url" => "https://play.google.com/store/apps/details?id=br.com.android.vantagensclube"
          },
          "ios" => {
            "min_version" => "2.2",
            "check_update" => false,
            "version" => "2.1",
            "store_url" => "https://apps.apple.com/app/vantagens-clube/id12345678"
          }
        })
      end

      it "must return fields related to mobile if business is inactive" do
        business.update_columns(active: false)
        get "/api/v2/project_config", headers: auth_headers, params: {scope: "mobile"}

        expect(response).to be_successful
        expect(response_hash.keys).to match_array(serialized_keys)
      end

      context "when app version is less or equal than 4.0.4" do
        let(:auth_headers) do
          {"Api-Secret": business.api_secret, "App-Version": "4.0.4"}
        end

        let!(:serialized_keys) do
          %w[user_manager_info clevertap customization_info support_info giftcard_config store_info ux_cam project inngage_token updated_at
            facebook_sdk facebook_sdk_app_id facebook_sdk_app_name facebook_sdk_client_token gtm_tag_id
            telemedicine_url home_search user_request_withdrawal
            freshworks_app_id freshworks_app_key freshworks_domain]
        end

        let(:project_keys) do
          %w[id banner cashback giftcard cashback_requestable_amount cashback_transfer_receiver
            member_referral web_domain wallet_type embedded prize_draw]
        end

        before do
          project_config.update_columns(clever_tap: false)
        end

        it "must return fields related to mobile" do
          get "/api/v2/project_config", headers: auth_headers, params: {scope: "mobile"}

          expect(response).to be_successful
          expect(response_hash.keys).to match_array(serialized_keys)

          expect(response_hash["project"].keys).to match_array(project_keys)
          expect(response_hash["user_manager_info"].keys).to match_array(user_manager_info_keys)
          expect(response_hash["customization_info"].keys).to match_array(customization_info_keys)
          expect(response_hash["support_info"].keys).to match_array(support_info_keys)
          expect(response_hash["giftcard_config"].keys).to match_array(giftcard_config_keys)
          expect(response_hash["clevertap"]).to be_nil
        end
      end
    end

    context "when with any other scope param" do
      let!(:serialized_keys) do
        %w[user_manager_info customization_info]
      end

      it "must render error" do
        get "/api/v2/project_config", headers: auth_headers, params: {scope: "other"}
        expect(response).to be_unprocessable
        expect(response_hash["error"]).to eq(I18n.t("project_config.errors.invalid_scope"))
      end
    end

    context "with invalid business header" do
      let(:headers) { {"Web-Domain" => "wrong"} }

      it "must unauthorize request" do
        get("/api/v2/project_config", headers:)

        expect(response).to have_http_status(:unprocessable_entity)
      end
    end

    context "without business header" do
      it "must unauthorize request" do
        get "/api/v2/project_config"

        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end
end
