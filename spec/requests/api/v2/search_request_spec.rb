require "rails_helper"

RSpec.describe Api::V2::SearchController, type: :request do
  describe "GET /api/v2/search" do
    let!(:business) { create(:business, :with_cashback) }
    let!(:user) { create(:user, business:) }
    let!(:headers) do
      Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret}, user)
    end
    let(:web_headers) do
      Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret, "App-Version" => "4.0.0", "aud" => "web"}, user)
    end
    let(:json_parse_response_body) { JSON.parse(response.body) }

    context "when resource is organization" do
      let(:organization_serializer_keys) do
        %w[
          id
          description
          distance
          favorited
          highest_discount
          highest_fund
          logo_image_large
          logo_image_small
          name
          top_background_image_large
          top_background_image_small
          branch_types
        ]
      end
      let(:organization_3_4_5_serializer_keys) do
        %w[
          id
          description
          distance
          favorited
          highest_discount
          highest_cashback
          highest_online_discount
          highest_physical_discount
          logo_image_large
          logo_image_small
          name
          top_background_image_large
          top_background_image_small
          branch_types
        ]
      end
      let!(:near_organization) { create(:organization, :with_cashback, name: "Near Órganizãtion") }
      let!(:near_branch) { create(:branch, :physical, organization: near_organization, lat: -19.9221, lng: -43.9241) }
      let!(:online_organization) { create(:organization, :percent_highest_discount, :with_cashback, name: "Online organization", highest_discount_value: 5) }
      let!(:online_branch) { create(:branch, :online, organization: online_organization) }
      let!(:far_organization) { create(:organization, :with_cashback, name: "Far organization") }
      let!(:far_branch) { create(:branch, :physical, organization: far_organization, lat: 20.0, lng: 20.0) }
      let!(:near_branch_inactive_branch) do
        create(:branch, :physical, active: false, organization: far_organization, lat: -19.9317, lng: -43.9337)
      end
      let!(:organization_all) { create(:organization, :with_cashback, :percent_highest_discount, name: "Organization", highest_discount_value: 6) }
      let!(:near_branch_all) { create(:branch, :physical, organization: organization_all, lat: -19.9353, lng: -43.9381) }
      let!(:far_branch_all) { create(:branch, :physical, organization: organization_all, lat: 20.0, lng: 20.0) }
      let!(:online_branch_all) { create(:branch, :online, organization: organization_all) }
      let!(:near_organization_inactive) { create(:organization, :with_cashback, name: "Near organization") }

      context "when branch type is online" do
        before { business.project_config.update!(show_only_organizations_with_coupons: false) }
        let(:params) { {term: "org", resource: "organization", branch_type: "online"} }

        it "returns organizations that have online branch" do
          headers["App-Version"] = "3.4.5"
          get("/api/v2/search", headers:, params:)

          expect(response).to be_ok
          expect(json_parse_response_body.map(&:keys)).to all(match_array(organization_3_4_5_serializer_keys))
          expect(json_parse_response_body.pluck("highest_discount")).to match_array([5, 6])
          expect(json_parse_response_body.pluck("highest_cashback")).to match_array([
            {"type" => "percent", "value" => 9.0},
            {"type" => "percent", "value" => 9.0}
          ])
          expect(json_parse_response_body.pluck("id")).to match_array([
            online_organization.id, organization_all.id
          ])
        end

        it "returns organizations that have online branch when app version is equal to compatible serializer version 3.4.5" do
          get("/api/v2/search", headers: web_headers, params:)

          expect(response).to be_ok
          expect(json_parse_response_body.map(&:keys)).to all(match_array(organization_serializer_keys))
          expect(json_parse_response_body.pluck("highest_discount")).to match_array([
            {"type" => "percent", "value" => 5.0},
            {"type" => "percent", "value" => 6.0}
          ])
          expect(json_parse_response_body.pluck("highest_fund")).to match_array([
            {"type" => "percent", "value" => 9.0},
            {"type" => "percent", "value" => 9.0}
          ])
          expect(json_parse_response_body.pluck("id")).to match_array([
            online_organization.id, organization_all.id
          ])
        end

        context "when currency is points" do
          let!(:business) { create(:business, :with_cashback, currency: "points", fair_value: 0.0125) }

          it "returns organizations that have online branch when app version is equal to compatible serializer version 3.4.5" do
            get("/api/v2/search", headers: web_headers, params:)

            expect(response).to be_ok
            expect(json_parse_response_body.map(&:keys)).to all(match_array(organization_serializer_keys))
            expect(json_parse_response_body.pluck("highest_fund")).to match_array([
              {"type" => "parity", "value" => 8.0},
              {"type" => "parity", "value" => 8.0}
            ])
            expect(json_parse_response_body.pluck("id")).to match_array([
              online_organization.id, organization_all.id
            ])
          end
        end
      end

      context "when branch type is physical" do
        before { business.project_config.update!(show_only_organizations_with_coupons: false) }

        context "with coordinates" do
          let(:params) do
            {term: "org", resource: "organization", branch_type: "physical", latitude: -19.9316, longitude: -43.9336}
          end

          it "returns organizations that have physical nearby branches" do
            nearest_org = create(:organization, :with_cashback, name: "Near Órganizãtion")
            nearest_branch = create(:branch, :physical, organization: nearest_org, lat: -19.9316, lng: -43.9336)

            _branch_one = create(:branch, :physical, organization: near_organization, lat: -19.9222, lng: -43.9242)
            branch_two = create(:branch, :physical, organization: near_organization, lat: -19.9319, lng: -43.9339)
            _branch_three = create(:branch, :physical, organization: near_organization, lat: -19.9320, lng: -43.9340)

            get("/api/v2/search", headers: web_headers, params:)

            expect(response).to be_ok
            expect(json_parse_response_body.map(&:keys)).to all(match_array(organization_serializer_keys))
            expect(json_parse_response_body.pluck("highest_discount")).to eq([
              nil,
              nil,
              {"type" => "percent", "value" => 6.0}
            ])
            expect(json_parse_response_body.pluck("id", "distance")).to eq([
              [nearest_org.id, nearest_branch.distance_to([params[:latitude], params[:longitude]]).round(2)],
              [near_organization.id, branch_two.distance_to([params[:latitude], params[:longitude]]).round(2)],
              [organization_all.id, near_branch_all.distance_to([params[:latitude], params[:longitude]]).round(2)]
            ])
          end
        end

        context "without coordinates" do
          let(:params) { {term: "org", resource: "organization", branch_type: "physical"} }

          it "renders error" do
            get("/api/v2/search", headers: web_headers, params:)
            expect(response).to be_unprocessable
            expect(json_parse_response_body["error"]).to eq("Latitude e longitude são obrigatórios")
          end
        end
      end

      context "when no organization matches term" do
        before { business.project_config.update!(show_only_organizations_with_coupons: false) }

        let(:params) { {term: "abc", resource: "organization", branch_type: "online"} }

        it "returns empty" do
          get("/api/v2/search", headers: web_headers, params:)
          expect(response).to be_ok
          expect(json_parse_response_body).to be_empty
        end
      end

      context "when project config set to show only organizations with coupons" do
        let(:params) { {term: "org", resource: "organization", branch_type: "online"} }

        before { business.project_config.update!(show_only_organizations_with_coupons: true) }

        it "returns empty" do
          get("/api/v2/search", headers: web_headers, params:)

          expect(response).to be_ok
          expect(json_parse_response_body).to be_empty
        end
      end

      context "when project config set to show organizations without coupons" do
        let(:params) { {term: "org", resource: "organization", branch_type: "online"} }

        before { business.project_config.update!(show_only_organizations_with_coupons: false) }

        it "returns organizations that match term" do
          get("/api/v2/search", headers: web_headers, params:)

          expect(response).to be_ok
          expect(json_parse_response_body.pluck("id")).to match_array([
            online_organization.id, organization_all.id
          ])
        end
      end

      context "with invalid term" do
        let(:params) { {term: "", resource: "organization", branch_type: "online"} }

        it "renders error" do
          get("/api/v2/search", headers: web_headers, params:)
          expect(response).to be_unprocessable
          expect(json_parse_response_body["error"]).to eq("Termo de busca inválido")
        end
      end
    end

    context "when resource is invalid" do
      let(:params) { {term: "org", resource: nil, branch_type: "online"} }

      it "renders error" do
        get("/api/v2/search", headers: web_headers, params:)
        expect(response).to be_unprocessable
        expect(json_parse_response_body["error"]).to eq("Recurso inválido")
      end
    end
  end
end
