# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::V2::Wallets::OrderHistory::EventsController, type: :request do
  describe "GET /api/v2/wallets/order_history/:order_history_id" do
    let(:user) { create(:user, business:) }
    let(:headers) { Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => user.business.api_secret}, user) }
    let(:response_body) { JSON.parse(response.body) }

    context "with funds of points" do
      let(:business) { create(:business, currency: "points", fair_value: 0.01) }
      let!(:fund) { create(:fund, :points, :pending, user:, amount: FFaker::Number.number) }

      before { fund.approved! }

      it "renders ok" do
        get("/api/v2/wallets/order_history/#{fund.id}/events", headers:)

        expect(response).to be_ok
        expect(response_body).to eq([
          {"status" => "pending", "description" => "A pontuação será aprovada em até 60 dias."},
          {"status" => "approved", "description" => "A pontuação ficará disponível na sua carteira em até 60 dias."}
        ])
      end
    end

    context "with funds of cashback" do
      let(:business) { create(:business, currency: "BRL") }
      let!(:fund) { create(:cashback_record, :pending, user:, order_amount: FFaker::Number.number) }

      before do
        fund.approved!
        fund.available!
        create(:cashback_record_history, cashback_record: fund, transaction_status: :transferred)
      end

      it "renders ok" do
        get("/api/v2/wallets/order_history/#{fund.id}/events", headers:)

        expect(response).to be_ok
        expect(response_body).to eq([
          {"status" => "pending", "description" => Date.current.strftime("%d/%m/%Y")},
          {"status" => "approved", "description" => Date.current.strftime("%d/%m/%Y")},
          {"status" => "available", "description" => Date.current.strftime("%d/%m/%Y")}
        ])
      end
    end
  end
end
