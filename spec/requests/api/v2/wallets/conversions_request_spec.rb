# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::V2::Wallets::ConversionsController, type: :request do
  describe "GET /api/v2/wallets/conversions" do
    let(:business) { create(:business, currency:, fair_value: 0.03) }
    let(:user) { create(:user, business:) }
    let!(:unrelated_fund_redemption_config) do
      create(:fund_redemption_config, :points, :bank_transfer, business: create(:business), fair_value: 0.03, min_amount: 1000, multiple: 100)
    end
    let!(:bank_transfer_fund_redemption_config) do
      create(:fund_redemption_config, :points, :bank_transfer, business: user.business, fair_value: 0.0125, min_amount: 1000, multiple: 100)
    end
    let!(:azul_fund_redemption_config) do
      create(:fund_redemption_config, :azul, currency: "BRL", business: user.business, fair_value: 0.03, min_amount: 1000, multiple: 100)
    end

    let(:headers) { Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => user.business.api_secret}, user) }

    let(:response_body) { JSON.parse(response.body) }

    context "when currency is points" do
      let(:currency) { "points" }
      let(:params) { {service: "bank_transfer", amount: 3900} }

      it "renders ok" do
        get("/api/v2/wallets/conversions", headers:, params:)

        expect(response).to be_ok
        expect(response_body).to eq "amount" => 48.75
      end
    end

    context "when currency is brl" do
      let(:currency) { "BRL" }
      let(:params) { {service: "azul", amount: 30.98} }

      it "renders ok" do
        get("/api/v2/wallets/conversions", headers:, params:)

        expect(response).to be_ok
        expect(response_body).to eq "amount" => 1032
      end
    end

    context "with wrong params" do
      let(:currency) { "points" }
      let(:params) { {service: "wrong", amount: 3900} }

      it "renders ok" do
        get("/api/v2/wallets/conversions", headers:, params:)

        expect(response).to be_not_found
      end
    end
  end
end
