# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::V2::Wallets::OrderHistoryController, type: :request do
  describe "GET /api/v2/wallets/order_history" do
    let(:user) { create(:user, business:) }
    let(:organization) { create(:organization, name: "Amazon") }
    let(:branch) { create(:branch, :online, organization:) }
    let(:cupon) { create(:cupon, branch:) }
    let(:order) { create(:order, :online, user:, cupon:) }
    let(:headers) { Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => user.business.api_secret}, user) }
    let(:params) { {status: "pending"} }
    let(:response_body) { JSON.parse(response.body) }

    context "with funds of points" do
      let(:business) { create(:business, :with_cashback, currency: "points", fair_value: 0.01) }
      let!(:fund) do
        create(
          :fund,
          :points,
          :pending,
          user:,
          order:,
          amount: 20000,
          order_amount: 12999,
          created_at: "2024-06-25 10:32:51"
        )
      end
      let!(:unrelated_fund) do
        create(:cashback_record, :pending, user:, order: create(:order, :online, user:, cupon:), order_amount: 12999)
      end

      it "renders ok" do
        get("/api/v2/wallets/order_history", headers:, params:)

        expect(response).to be_ok
        expect(response_body).to eq([
          "date" => "2024-06-25",
          "data" => [
            "id" => fund.id,
            "amount" => 20000,
            "created_at" => "2024-06-25T10:32:51.000-03:00",
            "organization" => {
              "id" => organization.id,
              "name" => "Amazon",
              "logo" => {
                "large" => "http://example.com/images/fallback/organization/logo_image/large_samplefile.jpg",
                "small" => "http://example.com/images/fallback/organization/logo_image/small_samplefile.jpg"
              }
            },
            "status" => "pending"
          ]
        ])
      end
    end

    context "with funds of cashback" do
      let(:business) { create(:business, :with_cashback, currency: "BRL", spread_percent: 0, fair_value: 0.01) }
      let!(:fund) do
        create(
          :cashback_record,
          :transferred,
          user:,
          order:,
          order_amount: 129.99,
          created_at: "2024-06-25 10:32:51"
        )
      end
      let!(:unrelated_fund) do
        create(:fund, :available, :points, user:, order: create(:order, :online, user:, cupon:), order_amount: 12999, expires_at: 1.week.from_now)
      end
      let(:params) { {status: "available"} }

      it "renders ok" do
        get("/api/v2/wallets/order_history", headers:, params:)

        expect(response).to be_ok
        expect(response_body).to eq([
          "date" => "2024-06-25",
          "data" => [
            "id" => fund.id,
            "amount" => 13.0,
            "created_at" => "2024-06-25T10:32:51.000-03:00",
            "organization" => {
              "id" => organization.id,
              "name" => "Amazon",
              "logo" => {
                "large" => "http://example.com/images/fallback/organization/logo_image/large_samplefile.jpg",
                "small" => "http://example.com/images/fallback/organization/logo_image/small_samplefile.jpg"
              }
            },
            "status" => "available"
          ]
        ])
      end
    end
  end
end
