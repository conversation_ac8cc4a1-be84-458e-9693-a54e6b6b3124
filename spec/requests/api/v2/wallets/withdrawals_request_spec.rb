# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::V2::Wallets::WithdrawalsController, type: :request do
  describe "#create" do
    let(:user) { create(:user, business:) }
    let!(:user_wallet) { create(:wallet, currency:, kind:, user:, balance: 15326) }
    let!(:business_wallet) { create(:wallet, currency:, kind:, business:, balance: 975201) }
    let!(:outflow_wallet) { create(:wallet, :outflow, currency:, kind:, balance: 85095713) }
    let(:headers) { Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret}, user) }
    let(:response_body) { JSON.parse(response.body) }

    context "when currency is BRL" do
      let(:kind) { Wallet::Kind::CASHBACK }
      let(:currency) { "BRL" }

      before do
        allow(WalletRedemption::EmailStatusWorker).to receive(:perform_async)
      end

      context "when user can request withdrawal" do
        let(:business) { create(:business, cashback: true, user_request_withdrawal: true) }

        context "and withdraws valid amount" do
          let(:params) { {amount: 153.26} }

          it "renders ok" do
            expect { post("/api/v2/wallets/withdrawals", headers:, params:) }
              .to change(Payout.where(total_amount: 153.26, description: "Pix Resgate #{business.project_config.name}"), :count).by(1)
              .and change { user_wallet.reload.balance }.by(-15326)
              .and change { outflow_wallet.reload.balance }.by(15326)
              .and not_change { business_wallet.reload.balance }

            expect(response).to be_ok
            expect(response_body["message"]).to eq("A solicitação de transferência do valor de R$ 153,26 foi concluída com sucesso. Você receberá um e-mail com mais informações sobre a transação.")
            expect(WalletRedemption::EmailStatusWorker).not_to have_received(:perform_async)
          end
        end

        context "and withdraws invalid amount" do
          let(:params) { {amount: -1} }

          it "renders error" do
            expect { post("/api/v2/wallets/withdrawals", headers:, params:) }
              .to not_change(Payout, :count)
              .and not_change { user_wallet.reload.balance }
              .and not_change { outflow_wallet.reload.balance }
              .and not_change { business_wallet.reload.balance }

            expect(response).to be_unprocessable
            expect(response_body["error"]).to eq("Valor deve ser maior que 0")
            expect(WalletRedemption::EmailStatusWorker).not_to have_received(:perform_async)
          end
        end

        context "and withdraws amount higher than balance" do
          let(:params) { {amount: 153.27} }

          it "renders error" do
            expect { post("/api/v2/wallets/withdrawals", headers:, params:) }
              .to not_change(Payout, :count)
              .and not_change { user_wallet.reload.balance }
              .and not_change { outflow_wallet.reload.balance }
              .and not_change { business_wallet.reload.balance }

            expect(response).to be_unprocessable
            expect(response_body["error"]).to eq("Saldo insuficiente")
            expect(WalletRedemption::EmailStatusWorker).not_to have_received(:perform_async)
          end
        end

        context "and withdraws amount zero" do
          let(:params) { {amount: 0} }

          it "renders error" do
            expect { post("/api/v2/wallets/withdrawals", headers:, params:) }
              .to not_change(Payout, :count)
              .and not_change { user_wallet.reload.balance }
              .and not_change { outflow_wallet.reload.balance }
              .and not_change { business_wallet.reload.balance }

            expect(response).to be_unprocessable
            expect(response_body).to eq("error" => "Valor deve ser maior que 0", "status" => 422)
            expect(WalletRedemption::EmailStatusWorker).not_to have_received(:perform_async)
          end
        end

        context "and service is azul" do
          let(:service) { FundRedemption.services[:azul] }
          let(:business) { create(:business, cashback: true, user_request_withdrawal: true) }
          let!(:fund_redemption_config) do
            create(:fund_redemption_config, :BRL, business:, service:, fair_value: 0.03, min_amount: 1, multiple: 1)
          end
          let(:params) { {amount: 30.98, service:} }

          it "renders ok" do
            expect { post("/api/v2/wallets/withdrawals", headers:, params:) }
              .to change { user_wallet.reload.balance }.by(-3098)
              .and change { user_wallet.entries.debit.where(description: "Saída de saldo na sua carteira. Ela é referente ao cashback para Pontos Azul.").count }.by(1)
              .and change { outflow_wallet.reload.balance }.by(3098)
              .and not_change { business_wallet.reload.balance }
              .and change(FundRedemption.azul.where(currency: "BRL", user:, amount: 3098), :count).by(1)
              .and change(TudoAzulAdHocTransaction.where(points: 1032, taxpayer_id: user.cpf), :count).by(1)

            expect(response).to be_ok
            expect(response_body["message"]).to eq("A solicitação de transferência do valor de R$ 30,98 para a Azul foi concluída com sucesso. Você receberá um e-mail com mais informações sobre a transação.")
            expect(WalletRedemption::EmailStatusWorker).to have_received(:perform_async).once
          end
        end
      end

      context "when user cannot request withdrawal" do
        shared_examples "non withdrawable" do
          let(:params) { {amount: 153.26} }

          it "renders error" do
            expect { post("/api/v2/wallets/withdrawals", headers:, params:) }
              .to not_change(Payout, :count)
              .and not_change { user_wallet.reload.balance }
              .and not_change { outflow_wallet.reload.balance }
              .and not_change { business_wallet.reload.balance }

            expect(response).to be_forbidden
            expect(response_body["error"]).to eq("Houve um erro desconhecido e não foi possível completar a solicitação. Por favor, tente novamente mais tarde.")
            expect(WalletRedemption::EmailStatusWorker).not_to have_received(:perform_async)
          end
        end

        context "because it is not allowed in business" do
          let(:business) { create(:business, cashback: true, user_request_withdrawal: false) }

          it_behaves_like "non withdrawable"
        end

        context "because cashback is disabled in business" do
          let(:business) { create(:business, cashback: false, user_request_withdrawal: true) }

          it_behaves_like "non withdrawable"
        end
      end
    end

    context "when currency is points" do
      let(:kind) { Wallet::Kind::CASHBACK }
      let(:currency) { "points" }
      let(:service) { FundRedemption.services[:bank_transfer] }
      let!(:fund_redemption_config) do
        create(:fund_redemption_config, :points, business:, service:, fair_value: 0.01, min_amount: 1000, multiple: 100)
      end
      let(:funds) do
        [
          create(:fund, :points, :available, user:, credited: true, amount: 14328, expires_at: 1.week.from_now),
          create(:fund, :points, :available, user:, credited: true, amount: 998, expires_at: 1.day.from_now)
        ]
      end

      before do
        allow(WalletRedemption::EmailStatusWorker).to receive(:perform_async)
      end

      context "when user can request withdrawal" do
        let(:business) do
          create(:business, cashback: true, user_request_withdrawal: true, currency:, fair_value: 0.0125)
        end

        context "and withdraws valid amount" do
          let(:amount) { 1000 }
          let(:params) { {amount:, service:} }

          it "renders ok" do
            expect { post("/api/v2/wallets/withdrawals", headers:, params:) }
              .to change { user_wallet.reload.balance }.by(-amount)
              .and change { outflow_wallet.reload.balance }.by(amount)
              .and not_change { business_wallet.reload.balance }
              .and change(FundRedemption.where(service:, amount:, user_id: user.id), :count).by(1)
              .and change { funds[1].reload.remaining_amount }.to(0)
              .and change { funds[0].reload.remaining_amount }.to(14326)

            expect(response).to be_ok
            expect(response_body["message"]).to eq("A solicitação de transferência do valor de R$ 10,00 foi concluída com sucesso. Você receberá um e-mail com mais informações sobre a transação.")
            expect(WalletRedemption::EmailStatusWorker).not_to have_received(:perform_async)
          end
        end

        context "and withdraws amount non-multiple" do
          let(:amount) { 1001 }
          let(:params) { {amount:, service:} }

          it "renders error" do
            expect { post("/api/v2/wallets/withdrawals", headers:, params:) }
              .to not_change { user_wallet.reload.balance }
              .and not_change { outflow_wallet.reload.balance }
              .and not_change { business_wallet.reload.balance }
              .and not_change(FundRedemption, :count)
              .and not_change { funds[0].reload.remaining_amount }
              .and not_change { funds[1].reload.remaining_amount }

            expect(response).to be_unprocessable
            expect(response_body["error"]).to eq("Valor deve ser múltiplo de 100")
            expect(WalletRedemption::EmailStatusWorker).not_to have_received(:perform_async)
          end
        end

        context "and withdraws amount less than minimum" do
          let(:amount) { 900 }
          let(:params) { {amount:, service:} }

          it "renders error" do
            expect { post("/api/v2/wallets/withdrawals", headers:, params:) }
              .to not_change { user_wallet.reload.balance }
              .and not_change { outflow_wallet.reload.balance }
              .and not_change { business_wallet.reload.balance }
              .and not_change(FundRedemption, :count)
              .and not_change { funds[0].reload.remaining_amount }
              .and not_change { funds[1].reload.remaining_amount }

            expect(response).to be_unprocessable
            expect(response_body["error"]).to eq("Valor deve ser maior ou igual a 1000")
            expect(WalletRedemption::EmailStatusWorker).not_to have_received(:perform_async)
          end
        end

        context "and withdraws amount higher than balance" do
          let(:amount) { 16000 }
          let(:params) { {amount:, service:} }

          it "renders error" do
            expect { post("/api/v2/wallets/withdrawals", headers:, params:) }
              .to not_change { user_wallet.reload.balance }
              .and not_change { outflow_wallet.reload.balance }
              .and not_change { business_wallet.reload.balance }
              .and not_change(FundRedemption, :count)
              .and not_change { funds[0].reload.remaining_amount }
              .and not_change { funds[1].reload.remaining_amount }

            expect(response).to be_unprocessable
            expect(response_body["error"]).to eq("Saldo insuficiente")
            expect(WalletRedemption::EmailStatusWorker).not_to have_received(:perform_async)
          end
        end

        context "and withdraws without service" do
          let(:amount) { 1000 }
          let(:params) { {amount:} }

          it "renders error" do
            expect { post("/api/v2/wallets/withdrawals", headers:, params:) }
              .to not_change { user_wallet.reload.balance }
              .and not_change { outflow_wallet.reload.balance }
              .and not_change { business_wallet.reload.balance }
              .and not_change(FundRedemption, :count)
              .and not_change { funds[0].reload.remaining_amount }
              .and not_change { funds[1].reload.remaining_amount }

            expect(response).to be_unprocessable
            expect(response_body["error"]).to eq("Serviço de resgate não está incluído na lista")
            expect(WalletRedemption::EmailStatusWorker).not_to have_received(:perform_async)
          end
        end
      end

      context "when user cannot request withdrawal" do
        shared_examples "non withdrawable" do
          let(:params) { {amount: 153.26} }

          it "renders error" do
            expect { post("/api/v2/wallets/withdrawals", headers:, params:) }
              .to not_change(Payout, :count)
              .and not_change { user_wallet.reload.balance }
              .and not_change { outflow_wallet.reload.balance }
              .and not_change { business_wallet.reload.balance }

            expect(response).to be_forbidden
            expect(response_body["error"]).to eq("Houve um erro desconhecido e não foi possível completar a solicitação. Por favor, tente novamente mais tarde.")
            expect(WalletRedemption::EmailStatusWorker).not_to have_received(:perform_async)
          end
        end

        context "because it is not allowed in business" do
          let(:business) { create(:business, cashback: true, user_request_withdrawal: false) }

          it_behaves_like "non withdrawable"
        end

        context "because cashback is disabled in business" do
          let(:business) { create(:business, cashback: false, user_request_withdrawal: true) }

          it_behaves_like "non withdrawable"
        end
      end
    end
  end
end
