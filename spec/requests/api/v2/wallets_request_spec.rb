# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::V2::WalletsController, type: :request do
  describe "GET /api/v2/wallets" do
    let(:business) { create(:business, :with_cashback, fair_value: 0.0125, spread_percent: 0, currency:) }
    let(:user) { create(:user, business:) }
    let(:headers) { Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => user.business.api_secret}, user) }
    let(:response_body) { JSON.parse(response.body) }

    context "with wallet of points" do
      let(:currency) { "points" }
      let!(:wallet) { create(:wallet, currency:, kind: Wallet::Kind::CASHBACK, user:, balance: 20000) }

      before do
        promotion = create(:promotion, cashback_value: 3.24)
        cupon = create(:cupon, promotion:)
        order = create(:order, cupon:, user:)
        create(:fund, :points, :pending, user:, order:, order_amount: 12998)

        order = create(:order, cupon:, user:)
        create(:fund, :points, :approved, user:, order:, order_amount: 5049)

        order = create(:order, cupon:, user:)
        create(:fund, :points, :available, user:, order:, order_amount: 19999)
      end

      context "when filtering by currency" do
        let(:params) { {type: "points"} }

        it "renders ok" do
          get("/api/v2/wallets", headers:, params:)

          expect(response).to be_ok
          expect(response_body.pluck("id")).to eq([wallet.id])
          expect(response_body).to match_array([
            "id" => wallet.id,
            "balance" => 20000,
            "currency" => "points",
            "delayed_balance" => 452,
            "type" => "points"
          ])
        end
      end
    end

    context "with wallet of cashback" do
      let(:currency) { "BRL" }
      let!(:unrelated_wallet) do
        create(:wallet, kind: Wallet::Kind::MEMBERSHIP, currency:, user:, balance: 0)
      end
      let!(:wallet) do
        create(:wallet, kind: Wallet::Kind::CASHBACK, currency:, user:, balance: 15264)
      end

      before do
        promotion = create(:promotion, cashback_value: 3.24)
        cupon = create(:cupon, promotion:)
        order = create(:order, cupon:, user:)
        create(:cashback_record, :pending, user:, order:, order_amount: 129.98)

        order = create(:order, cupon:, user:)
        create(:cashback_record, :approved, user:, order:, order_amount: 50.49)
        order = create(:order, cupon:, user:)
        create(:cashback_record, :available_lecupon, user:, order:, order_amount: 50.49)

        order = create(:order, cupon:, user:)
        create(:cashback_record, :available, user:, order:, order_amount: 199.99)
      end

      context "when filtering by currency" do
        let(:params) { {type: "cashback"} }

        it "renders ok" do
          get("/api/v2/wallets", headers:, params:)

          expect(response).to be_ok
          expect(response_body.pluck("id")).to eq([wallet.id])
          expect(response_body).to match_array([
            "id" => wallet.id,
            "approved_balance" => 3.28,
            "balance" => 152.64,
            "currency" => "BRL",
            "pending_balance" => 4.21,
            "type" => "cashback"
          ])
        end
      end
    end

    context "when filtering by invalid currency" do
      let(:currency) { "BRL" }
      let(:params) { {type: "invalid"} }

      it "renders ok" do
        get("/api/v2/wallets", headers:, params:)

        expect(response).to be_ok
        expect(response_body).to be_empty
      end
    end
  end
end
