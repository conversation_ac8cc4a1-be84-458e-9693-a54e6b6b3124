require "rails_helper"

RSpec.describe Api::V2::OrganizationsController, type: :request do
  let(:business) { create(:business, :with_cashback, spread_percent: 0) }
  let(:user) { create(:user, business:) }
  let!(:web_headers) do
    Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret, "App-Version" => "4.0.0", "aud" => "web"}, user)
  end
  let!(:headers) do
    Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret}, user)
  end
  let(:organization_serializer_keys) do
    %w[
      id
      description
      distance
      favorited
      facebook
      highest_discount
      highest_fund
      instagram
      logo_image_large
      logo_image_small
      name
      top_background_image_large
      top_background_image_small
      branch_types
    ]
  end
  let(:organization_3_4_5_serializer_keys) do
    %w[
      id
      description
      distance
      favorited
      highest_discount
      highest_cashback
      highest_online_discount
      highest_physical_discount
      logo_image_large
      logo_image_small
      name
      top_background_image_large
      top_background_image_small
      branch_types
    ]
  end

  let(:json_parse_response_body) { JSON.parse(response.body) }

  describe "GET /api/v2/organizations/:id" do
    let(:category_one) { create(:category) }
    let(:create_organization_active) do
      create(:organization,
        :with_cashback,
        categories: [category_one],
        active: true,
        highest_discount_type: "percent",
        highest_discount_value: 11.3,
        highest_cashback_type: "percent",
        highest_cashback_value: 45.5)
    end
    let(:category_two) { create(:category) }
    let(:create_organization_inactive) { create(:organization, active: false, categories: [category_two]) }

    context "given a active organization" do
      it "returns a active organization" do
        organization = create_organization_active

        get("/api/v2/organizations/#{organization.id}", headers: web_headers)

        expect(response).to have_http_status(:success)
        expect(json_parse_response_body.keys).to match_array(organization_serializer_keys)
        expect(json_parse_response_body["highest_discount"]).to eq(
          "type" => "percent",
          "value" => 11.3
        )
        expect(json_parse_response_body["highest_fund"]).to eq(
          "type" => "percent",
          "value" => 45.5
        )
      end

      it "expected returns highest discounts" do
        organization = create_organization_active
        branch_online = create(:branch, :online, organization:)
        create(:cupon, :online, :percent_discount, branch: branch_online, discount_value: 15.5)
        create(:cupon, :online, :percent_discount, branch: branch_online, discount_value: 15.6)
        branch_physical = create(:branch, :physical, organization:)
        create(:cupon, :classic, :percent_discount, branch: branch_physical, discount_value: 15.7)
        promotion = create(:promotion, :online, :available, :fixed_discount, organization:, discount_value: 15.8)
        create(:cupon, :classic, :percent_discount, branch: branch_physical, promotion:, discount_value: 15.8)
        organization.refresh_highest_discount

        get "/api/v2/organizations/#{organization.id}", headers: web_headers

        expect(json_parse_response_body.keys).to match_array(organization_serializer_keys)
        expect(json_parse_response_body["highest_discount"]).to eq(
          "type" => "fixed",
          "value" => 15.8
        )
      end

      it "expected returns highest discounts when app version is equal to compatible serializer version" do
        organization = create_organization_active
        branch_online = create(:branch, :online, organization:)
        create(:cupon, :online, :percent_discount, branch: branch_online, discount_value: 15.5)
        create(:cupon, :online, :percent_discount, branch: branch_online, discount_value: 15.6)
        branch_physical = create(:branch, :physical, organization:)
        create(:cupon, :classic, :percent_discount, branch: branch_physical, discount_value: 15.7)
        promotion = create(:promotion, :online, :available, :percent_discount, organization:, discount_value: 15.8)
        create(:cupon, :classic, :percent_discount, branch: branch_physical, promotion:, discount_value: 15.8)
        organization.refresh_highest_discount

        headers["App-Version"] = "3.4.5"
        get("/api/v2/organizations/#{organization.id}", headers:)

        expect(response).to have_http_status(:success)
        expect(json_parse_response_body.keys).to match_array(organization_3_4_5_serializer_keys)
        expect(json_parse_response_body["highest_discount"]).to eq(15.8)
        expect(json_parse_response_body["highest_online_discount"]).to eq(15.6)
        expect(json_parse_response_body["highest_physical_discount"]).to eq(15.8)
      end

      context "when currency is points" do
        let(:business) do
          create(:business, :with_cashback, spread_percent: 0, currency: "points", fair_value: 0.0125)
        end

        it "returns a active organization" do
          organization = create_organization_active

          get("/api/v2/organizations/#{organization.id}", headers: web_headers)

          expect(response).to have_http_status(:success)
          expect(json_parse_response_body.keys).to match_array(organization_serializer_keys)
          expect(json_parse_response_body["highest_discount"]).to eq(
            "type" => "percent",
            "value" => 11.3
          )
          expect(json_parse_response_body["highest_fund"]).to eq(
            "type" => "parity",
            "value" => 36.4
          )
        end
      end
    end

    context "given a inactive organization" do
      specify do
        organization = create_organization_inactive

        get "/api/v2/organizations/#{organization.id}", headers: web_headers

        expect(response).to have_http_status(:not_found)
      end
    end

    context "given a blocklisted organization" do
      specify do
        organization = create_organization_active
        create(:organization_blocklist, business:, organization:)

        get "/api/v2/organizations/#{organization.id}", headers: web_headers

        expect(response).to have_http_status(:not_found)
      end
    end

    context "given a blocklisted category" do
      specify do
        organization = create_organization_active
        create(:category_blocklist, business:, category: category_one)

        get "/api/v2/organizations/#{organization.id}", headers: web_headers

        expect(response).to have_http_status(:not_found)
      end
    end

    context "given a invalid url id" do
      specify do
        get("/api/v2/organizations/0", headers:)

        expect(response).to have_http_status(:not_found)
      end
    end

    context "when business has set profile for organization" do
      it "returns organizations with information from profile" do
        organization = create_organization_active
        organization_profile = create(
          :organization_profile,
          :with_cashback,
          organization:,
          business:,
          description: FFaker::LoremBR.phrase(5),
          short_description: FFaker::LoremBR.characters(40),
          logo_image: Rack::Test::UploadedFile.new(Rails.root.join("spec/fixtures/samplefile.png")),
          top_background_image: Rack::Test::UploadedFile.new(Rails.root.join("spec/fixtures/samplefile.png"))
        )

        get "/api/v2/organizations/#{organization.id}", headers: web_headers, as: :json

        expect(response).to be_ok
        expect(json_parse_response_body.keys).to match_array(organization_serializer_keys)
        expect(json_parse_response_body["highest_discount"]).to eq(
          "type" => "percent",
          "value" => 11.3
        )
        expect(json_parse_response_body["id"]).to eq(organization.id)
        expect(json_parse_response_body["description"]).to eq(organization_profile.description)
        expect(json_parse_response_body["logo_image_large"]).to eq(organization_profile.logo_image.url(:large))
        expect(json_parse_response_body["logo_image_small"]).to eq(organization_profile.logo_image.url(:small))
      end

      it "returns organizations with information from profile and fills missing data by taking from organization" do
        organization = create_organization_active
        organization_profile = create(
          :organization_profile,
          :with_cashback,
          organization:,
          business:,
          description: FFaker::LoremBR.phrase(5),
          logo_image: nil,
          top_background_image: nil
        )

        get "/api/v2/organizations/#{organization.id}", headers: web_headers, as: :json

        expect(response).to be_ok
        expect(json_parse_response_body.keys).to match_array(organization_serializer_keys)
        expect(json_parse_response_body["highest_discount"]).to eq(
          "type" => "percent",
          "value" => 11.3
        )
        expect(json_parse_response_body["id"]).to eq(organization.id)
        expect(json_parse_response_body["description"]).to eq(organization_profile.description)
        expect(json_parse_response_body["logo_image_large"]).to eq(organization.logo_image.url(:large))
        expect(json_parse_response_body["logo_image_small"]).to eq(organization.logo_image.url(:small))
      end
    end

    context "when organization does not have cashback" do
      let(:organization) { create(:organization, highest_cashback_type: nil, highest_cashback_value: 0.0) }
      let(:serializer_keys) do
        %w[
          id
          description
          distance
          favorited
          facebook
          highest_discount
          instagram
          logo_image_large
          logo_image_small
          name
          top_background_image_large
          top_background_image_small
          branch_types
        ]
      end

      it "renders ok and does not return cashback in serializer" do
        get("/api/v2/organizations/#{organization.id}", headers: web_headers)

        expect(response).to be_ok
        expect(json_parse_response_body.keys).to match_array(serializer_keys)
      end
    end
  end
end
