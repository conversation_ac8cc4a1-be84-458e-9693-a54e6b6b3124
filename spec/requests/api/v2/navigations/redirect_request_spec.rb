# frozen_string_literal: true

require "devise/jwt/test_helpers"
require "rails_helper"

RSpec.describe Api::V2::Navigations::RedirectController, type: :request do
  describe "#show" do
    let(:user) { create(:user) }

    context "when menu is not external" do
      let(:menu) { create(:menu) }

      it "must render not found" do
        get "/api/v2/menu_items/#{menu.id}/redirect", params: {mref: user.id}

        expect(response).to be_not_found
      end
    end

    context "when external url should be called with GET http method" do
      let(:menu) do
        create(:menu,
          :external_link,
          http_method: "get",
          cpf_param_name: "cpf_test",
          destination: "https://google.com.br/")
      end

      it "must simply redirect to the destination url with menu_item params" do
        get "/api/v2/menu_items/#{menu.id}/redirect", params: {mref: user.id}

        expect(response).to have_http_status(:found)
        expect(response.redirect_url).to eq(menu.url_with_query_params(user:))
      end
    end

    context "when external url should be called with GET http method but menu item is not active" do
      let(:menu) do
        create(:menu,
          :external_link,
          active: false,
          http_method: "get",
          cpf_param_name: "cpf_test",
          destination: "https://google.com.br/")
      end

      it "renders not found" do
        get "/api/v2/menu_items/#{menu.id}/redirect", params: {mref: user.id}

        expect(response).to be_not_found
      end
    end

    context "when external url should be called with POST http method", :vcr do
      let(:menu) do
        create(:menu,
          :external_link,
          http_method: "post",
          cpf_param_name: "cpf_test",
          name_param_name: "name_test",
          header_param_name: "header_test",
          header_param_value: "header_value",
          destination: "https://client_api.com/redirect_middleware")
      end

      context "when external POST request attempts to redirect" do
        before do
          stub_request(:post, "https://client_api.com/redirect_middleware")
            .with(
              body: hash_including("cpf_test" => CPF.new(user.cpf).stripped, "name_test" => user.name)
            )
            .to_return(
              status: 302,
              headers: {location: "https://google.com"}
            )
        end

        it "must request external url with menu item params and headers and then redirect" do
          get "/api/v2/menu_items/#{menu.id}/redirect", params: {mref: user.id}

          expect(response).to have_http_status(:found)
          expect(response.redirect_url).to eq("https://google.com")
        end

        context "when sending encrypted hash" do
          before do
            menu.update!(encrypt_request: true)

            stub_request(:post, "https://client_api.com/redirect_middleware")
              .with(
                body: hash_including("cpf_test" => CPF.new(user.cpf).stripped, "name_test" => user.name),
                headers: {"X-Lecupon-Token" => /.*/}
              )
              .to_return(
                status: 302,
                headers: {location: "https://google2.com"}
              )
          end

          it "must include X-LECUPON-TOKEN on the request" do
            get "/api/v2/menu_items/#{menu.id}/redirect", params: {mref: user.id}

            expect(response).to have_http_status(:found)
            expect(response.redirect_url).to eq("https://google2.com")
          end
        end
      end

      context "when external POST request does not redirect" do
        before do
          stub_request(:post, "https://client_api.com/redirect_middleware")
            .with(
              body: hash_including("cpf_test" => CPF.new(user.cpf).stripped, "name_test" => user.name)
            )
            .to_return(
              status: 401,
              body: "CNPJ não autorizado"
            )
        end

        it "must render external response" do
          get "/api/v2/menu_items/#{menu.id}/redirect", params: {mref: user.id}

          expect(response.body).to eq("CNPJ não autorizado")
        end
      end

      context "when the menu is a smart_link" do
        let(:menu) do
          create(:menu,
            :gift_card,
            smart_link: true,
            http_method: "get",
            cpf_param_name: "cpf_test",
            device_type: Navigation::DeviceType::MOBILE,
            destination: "/giftcard")
        end

        context "and the business have a web_domain" do
          it "creates a smart link with redirect and embedded" do
            get "/api/v2/menu_items/#{menu.id}/redirect", params: {mref: user.id}

            expect(response).to have_http_status(:found)
            user.reload
            expect(response.redirect_url).to eq("https://#{menu.business.project_config.web_domain}/login?token=#{user.smart_token}&embedded=true&redirect_to=#{menu.destination}?hide_back=true")
          end
        end

        context "and the business does not have a web_domain" do
          before do
            menu.business.project_config.update!(web_domain: nil)
          end

          it "uses the 'giftcard.lecupon.com' as domain and adds api_secret" do
            get "/api/v2/menu_items/#{menu.id}/redirect", params: {mref: user.id}

            expect(response).to have_http_status(:found)
            user.reload
            expect(response.redirect_url).to eq("https://giftcard.lecupon.com/login?token=#{user.smart_token}&embedded=true&redirect_to=#{menu.destination}?hide_back=true&api_secret=#{menu.business.api_secret}")
          end
        end
      end
    end
  end
end
