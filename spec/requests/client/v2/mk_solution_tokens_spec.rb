require "rails_helper"
require "support/shared_examples/client_employee_request_shared_examples"

RSpec.describe Client::V2::MkSolutionTokensController, type: :request do
  let(:business) { create(:business) }
  let(:authorized_user_group) { create(:authorized_user_group, business:) }

  let(:valid_attributes) {
    {
      base_url: "https://website.com.br",
      user_token: "19483117791b8d318ae00670352672cafc845d010ea21a366cb856c521c20c84",
      password: "password",
      user_tag: authorized_user_group.slug,
      business_id: business.id,
      auth_strategy: "preload"
    }
  }

  let(:invalid_attributes) {
    {
      base_url: nil,
      user_token: "19483117791b8d318ae00670352672cafc845d010ea21a366cb856c521c20c84",
      password: "password",
      user_tag: authorized_user_group.slug,
      business_id: business.id,
      auth_strategy: "preload"
    }
  }

  let(:response_hash) { JSON.parse(response.body) }
  let(:serialized_keys) { %w[id base_url user_token password authorized_user_group] }

  let!(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  before do
    allow(Slack::Api).to receive(:send_message)
  end

  describe "GET /show" do
    let!(:mk_solution_token) { create(:mk_solution_token, business:) }

    it "renders a successful response" do
      path = [
        "/client/v2/businesses/#{business.cnpj}/mk_solution_tokens",
        "/client/v2/mk_solution_tokens"
      ].sample
      get(path, params: {cnpj: business.cnpj}, headers:)

      expect(response_hash.keys).to match_array(serialized_keys)
      expect(response).to be_successful
    end

    context "without client employee authorization" do
      before do
        path = [
          "/client/v2/businesses/#{business.cnpj}/mk_solution_tokens",
          "/client/v2/mk_solution_tokens"
        ].sample
        get path
      end

      it_behaves_like "unauthorized client_employee"
    end

    context "when business unrelated to the current user" do
      let(:client_employee) { create(:client_employee, :admin_client) }

      it "returns not found" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/mk_solution_tokens",
          "/client/v2/mk_solution_tokens"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:not_found)
        expect(response_hash["error"]).to eq("Business não encontrado")
      end
    end

    context "without mk solution token" do
      before { mk_solution_token.destroy! }

      it "returns error" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/mk_solution_tokens",
          "/client/v2/mk_solution_tokens"
        ].sample
        get(path, params: {cnpj: business.cnpj}, headers:)

        expect(response).to have_http_status(:not_found)
      end
    end
  end

  describe "#create" do
    let(:business) { create(:business) }

    let(:mock_token_validator) do
      double(:mock_token_validator, valid?: credentials_validate, errors: nil)
    end

    context "with valid parameters" do
      let(:credentials_validate) { true }

      before do
        allow(MkSolution::Token::Validator).to receive(:new).and_return(mock_token_validator)
      end

      it "creates a new MkSolutionToken" do
        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/mk_solution_tokens",
            "/client/v2/mk_solution_tokens"
          ].sample
          post(path, params: valid_attributes, headers:)

          expect(response).to be_created
          expect(response_hash.keys).to match_array(serialized_keys)
        end.to change(MkSolutionToken, :count).by(1)

        mk_solution_token = MkSolutionToken.find(response_hash["id"])
        expect(response_hash["authorized_user_group"]["slug"]).to eq(mk_solution_token.authorized_user_group.slug)
      end
    end

    context "with invalid parameters" do
      let(:credentials_validate) { false }

      before do
        allow(MkSolution::Token::Validator).to receive(:new).and_return(mock_token_validator)
      end

      it "does not create a new MkSolutionToken" do
        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/mk_solution_tokens",
            "/client/v2/mk_solution_tokens"
          ].sample
          post path, params: invalid_attributes, headers:
        end.to change(MkSolutionToken, :count).by(0)
      end

      it "renders a error" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/mk_solution_tokens",
          "/client/v2/mk_solution_tokens"
        ].sample
        post(path, params: invalid_attributes, headers:)

        expect(response).to be_unprocessable
      end
    end

    context "with wrong url" do
      let(:params) {
        {
          base_url: "mk.website.com.br/mk",
          user_token: "19483117791b8d318ae00670352672cafc845d010ea21a366cb856c521c20c84",
          password: "password",
          user_tag: authorized_user_group.slug,
          business_id: business.id,
          auth_strategy: "preload"
        }
      }

      it "does not create a new MkSolutionToken" do
        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/mk_solution_tokens",
            "/client/v2/mk_solution_tokens"
          ].sample
          post path, params:, headers:
        end.to change(MkSolutionToken, :count).by(0)
      end

      it "renders a error" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/mk_solution_tokens",
          "/client/v2/mk_solution_tokens"
        ].sample
        post(path, params:, headers:)

        expect(response).to be_unprocessable
      end
    end

    context "without client employee authorization" do
      before do
        path = [
          "/client/v2/businesses/#{business.cnpj}/mk_solution_tokens",
          "/client/v2/mk_solution_tokens"
        ].sample
        post path, params: valid_attributes
      end

      it_behaves_like "unauthorized client_employee"
    end

    context "with business unrelated to client employee" do
      context "when is lecupon admin" do
        let(:credentials_validate) { true }
        let!(:client_employee) { create(:client_employee, :admin_lecupon) }

        before do
          allow(MkSolution::Token::Validator).to receive(:new).and_return(mock_token_validator)
        end

        it "creates a new MkSolutionToken" do
          expect do
            path = [
              "/client/v2/businesses/#{business.cnpj}/mk_solution_tokens",
              "/client/v2/mk_solution_tokens"
            ].sample
            post(path, params: valid_attributes, headers:)

            expect(response_hash.keys).to match_array(serialized_keys)
          end.to change(MkSolutionToken, :count).by(1)
        end
      end

      context "when is not lecupon admin" do
        let(:client_employee) { create(:client_employee, :admin_client) }

        it "returns not found" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/mk_solution_tokens",
            "/client/v2/mk_solution_tokens"
          ].sample
          post(path, params: valid_attributes, headers:)

          expect(response).to have_http_status(:not_found)
          expect(response_hash["error"]).to eq("Business não encontrado")
        end
      end
    end
  end

  describe "#update" do
    let!(:mk_solution_token) { create(:mk_solution_token, business:) }

    let(:new_attributes) {
      {base_url: "https://any_other_url.com/"}
    }

    context "with valid parameters" do
      it "updates the requested mk_solution_token" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/mk_solution_tokens",
          "/client/v2/mk_solution_tokens"
        ].sample
        patch(path, params: new_attributes, headers:)

        expect(response_hash.keys).to match_array(serialized_keys)
        mk_solution_token.reload
        expect(mk_solution_token.base_url).to eq("https://any_other_url.com/")
      end
    end

    context "with invalid parameters" do
      it "renders error" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/mk_solution_tokens",
          "/client/v2/mk_solution_tokens"
        ].sample
        patch(path, params: invalid_attributes, headers:)

        expect(response).to be_unprocessable
      end
    end

    context "without client employee authorization" do
      before do
        path = [
          "/client/v2/businesses/#{business.cnpj}/mk_solution_tokens",
          "/client/v2/mk_solution_tokens"
        ].sample
        patch path, params: new_attributes
      end

      it_behaves_like "unauthorized client_employee"
    end

    context "with business_id unrelated to client employee" do
      context "when is lecupon admin" do
        let!(:client_employee) { create(:client_employee, :admin_lecupon) }

        it "updates the requested mk_solution_token" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/mk_solution_tokens",
            "/client/v2/mk_solution_tokens"
          ].sample
          patch(path, params: new_attributes, headers:)

          expect(response_hash.keys).to match_array(serialized_keys)
          mk_solution_token.reload
          expect(mk_solution_token.base_url).to eq("https://any_other_url.com/")
        end
      end

      context "when is not lecupon admin" do
        let(:client_employee) { create(:client_employee, :admin_client) }

        it "returns not found" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/mk_solution_tokens",
            "/client/v2/mk_solution_tokens"
          ].sample
          patch(path, params: new_attributes, headers:)

          expect(response).to have_http_status(:not_found)
          expect(response_hash["error"]).to eq("Business não encontrado")
        end
      end
    end

    context "without mk solution token" do
      before { mk_solution_token.destroy! }

      it "returns error" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/mk_solution_tokens",
          "/client/v2/mk_solution_tokens"
        ].sample
        patch(path, params: new_attributes, headers:)

        expect(response).to have_http_status(:not_found)
      end
    end
  end

  describe "#destroy" do
    let!(:mk_solution_token) { create(:mk_solution_token, business:) }
    let!(:mk_solution_plans) { create(:mk_solution_plan, mk_solution_token:) }

    it "destroys the requested mk_solution_token" do
      expect do
        path = [
          "/client/v2/businesses/#{business.cnpj}/mk_solution_tokens",
          "/client/v2/mk_solution_tokens"
        ].sample
        delete(path, headers:)
        expect(response).to be_no_content
      end.to change(MkSolutionToken, :count).by(-1).and change(MkSolutionPlan, :count).by(-1)
    end

    context "without client employee authorization" do
      before do
        path = [
          "/client/v2/businesses/#{business.cnpj}/mk_solution_tokens",
          "/client/v2/mk_solution_tokens"
        ].sample
        delete path
      end

      it_behaves_like "unauthorized client_employee"
    end

    context "with business unrelated to client employee" do
      context "when is lecupon admin" do
        let!(:client_employee) { create(:client_employee, :admin_lecupon) }

        it "destroys the requested mk_solution_token" do
          expect do
            path = [
              "/client/v2/businesses/#{business.cnpj}/mk_solution_tokens",
              "/client/v2/mk_solution_tokens"
            ].sample
            delete(path, headers:)

            expect(response).to be_no_content
          end.to change(MkSolutionToken, :count).by(-1)
        end
      end

      context "when is not lecupon admin" do
        let(:client_employee) { create(:client_employee, :admin_client) }

        it "returns not found" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/mk_solution_tokens",
            "/client/v2/mk_solution_tokens"
          ].sample
          delete(path, headers:)

          expect(response).to have_http_status(:not_found)
          expect(response_hash["error"]).to eq("Business não encontrado")
        end
      end

      context "without mk solution token" do
        before { mk_solution_token.destroy! }

        it "returns error" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/mk_solution_tokens",
            "/client/v2/mk_solution_tokens"
          ].sample
          delete(path, headers:)

          expect(response).to have_http_status(:not_found)
        end
      end
    end
  end
end
