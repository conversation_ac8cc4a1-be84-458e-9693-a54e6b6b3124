# frozen_string_literal: true

require "rails_helper"

RSpec.describe Client::V2::Reports::CashbackTransfersController, type: :request do
  let(:response_body) { JSON.parse(response.body) }

  describe "#index" do
    it "returns cashback transfers" do
      business = create(:business, cashback: true, cashback_wallet_destination: :membership, spread_percent: 0)

      user_1 = create(:user, business:, name: "Fulano", cpf: "***********")
      order_1 = create(:order, user: user_1, cashback_type: :percent, cashback_value: 10)
      batches_1 = [
        create(:payout, business:, due_on: 11.months.ago),
        create(:payout, business:, due_on: 2.years.ago)
      ]
      create(:cashback_record, :transferred, payout: batches_1[0], order: order_1, user: user_1, order_amount: 225.4, external_id: "123")
      create(:cashback_record, :transferred, payout: batches_1[1], order: order_1, user: user_1, order_amount: 619.2, external_id: "456")
      batches_1[0].update_columns(status: :transferred, total_amount: 22.54, transferred_at: 11.months.ago)
      batches_1[1].update_columns(status: :transferred, total_amount: 61.92, transferred_at: 2.years.ago)

      user_2 = create(:user, business:, name: "Ciclano", cpf: "***********")
      order_2 = create(:order, user: user_2, cashback_type: :percent, cashback_value: 10)
      batches_2 = create_list(:payout, 5, user: user_2, user_business: business)
      create(:cashback_record, :transferred, payout: batches_2[0], order: order_2, user: user_2, order_amount: 1249.1, external_id: "123")
      create(:cashback_record, :transferred, payout: batches_2[1], order: order_2, user: user_2, order_amount: 700.1, external_id: "456")
      create(:cashback_record, :transferred, payout: batches_2[2], order: order_2, user: user_2, order_amount: 390.8, external_id: "789")
      create(:cashback_record, :transferred, payout: batches_2[3], order: order_2, user: user_2, order_amount: 2103.3, external_id: "012")
      create(:cashback_record, :transferred, payout: batches_2[4], order: order_2, user: user_2, order_amount: 1067.4, external_id: "345")
      batches_2[0].update_columns(status: :transferred, total_amount: 124.91, transferred_at: 11.months.ago)
      batches_2[1].update_columns(status: :transferred, total_amount: 70.01, transferred_at: 6.months.ago)
      batches_2[2].update_columns(status: :transferred, total_amount: 39.08, transferred_at: 10.days.ago)
      batches_2[3].update_columns(status: :transferred, total_amount: 210.33, transferred_at: 1.year.ago - 2.days)
      batches_2[4].update_columns(status: :in_transfer, total_amount: 106.74)

      headers = headers(business)
      params = {page: 1, start_date: 1.year.ago, end_date: DateTime.current}

      path = [
        "/client/v2/businesses/#{business.id}/reports/cashback_transfers",
        "/client/v2/reports/cashback_transfers"
      ].sample
      get(path, params:, headers:)

      expect(response).to be_ok
      expect(response_body).to eq({
        "cashback_amount" => 256.54,
        "cashbacks" => [
          {
            "user_name" => "Ciclano",
            "taxpayer_number" => "309.087.230-75",
            "total_amount" => 234.0
          },
          {
            "user_name" => "Fulano",
            "taxpayer_number" => "238.950.780-85",
            "total_amount" => 22.54
          }
        ]
      })
    end

    it "returns error with a period range greater than 365 days" do
      business = create(:business, cashback: true, cashback_wallet_destination: :cashback)

      headers = headers(business)
      params = {page: 1, start_date: 1.year.ago - 2.days, end_date: DateTime.current}

      path = [
        "/client/v2/businesses/#{business.id}/reports/cashback_transfers",
        "/client/v2/reports/cashback_transfers"
      ].sample
      get(path, params:, headers:)
      get(path, params:, headers:)

      expect(response).to be_unprocessable
      expect(response_body["error"]).to eq("O intervalo máximo entre as datas é de 1 ano")
    end

    it "returns error without page" do
      business = create(:business, cashback: true, cashback_wallet_destination: :cashback)

      headers = headers(business)
      params = {start_date: 1.year.ago, end_date: DateTime.current}

      path = [
        "/client/v2/businesses/#{business.id}/reports/cashback_transfers",
        "/client/v2/reports/cashback_transfers"
      ].sample
      get(path, params:, headers:)
      get(path, params:, headers:)

      expect(response).to have_http_status(:precondition_failed)
      expect(response_body["error"]).to eq("A requisição falhou devido a ausência de parâmetro: page")
    end

    it "returns error without start date" do
      business = create(:business, cashback: true, cashback_wallet_destination: :cashback)

      headers = headers(business)
      params = {page: 1, end_date: DateTime.current}

      path = [
        "/client/v2/businesses/#{business.id}/reports/cashback_transfers",
        "/client/v2/reports/cashback_transfers"
      ].sample
      get(path, params:, headers:)

      expect(response).to have_http_status(:precondition_failed)
      expect(response_body["error"]).to eq("A requisição falhou devido a ausência de parâmetro: start_date")
    end

    it "returns error without end date" do
      business = create(:business, cashback: true, cashback_wallet_destination: :cashback)

      headers = headers(business)
      params = {page: 1, start_date: 1.year.ago}

      path = [
        "/client/v2/businesses/#{business.id}/reports/cashback_transfers",
        "/client/v2/reports/cashback_transfers"
      ].sample
      get(path, params:, headers:)

      expect(response).to have_http_status(:precondition_failed)
      expect(response_body["error"]).to eq("A requisição falhou devido a ausência de parâmetro: end_date")
    end

    it "returns empty when business has cashback disabled" do
      business = create(:business, cashback: false)

      headers = headers(business)
      params = {page: 1, start_date: 1.year.ago, end_date: DateTime.current}

      path = [
        "/client/v2/businesses/#{business.id}/reports/cashback_transfers",
        "/client/v2/reports/cashback_transfers"
      ].sample
      get(path, params:, headers:)

      expect(response).to be_forbidden
      expect(response_body["error"]).to eq(I18n.t("inactive_cashback"))
    end
  end

  def headers(business)
    client_employee = create(:client_employee, businesses: [business])
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end
end
