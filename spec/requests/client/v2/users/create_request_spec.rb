# frozen_string_literal: true

require "rails_helper"

RSpec.describe Client::V2::UsersController, type: :request do
  let!(:business) { create(:business, :oab, cnpj: "03814381000130") }

  let!(:client_employee) { create(:client_employee, :admin_client, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  let(:response_hash) { JSON.parse(response.body) }
  let(:serialized_keys) do
    %w[id name email cellphone cpf active activated_at user_tags business_id default_auth_flow telemedicine
      custom_field_1 custom_field_2 custom_field_3 custom_field_4 custom_field_5 custom_field_6 custom_field_7 custom_field_8
      wallet tags]
  end

  describe "#create" do
    let(:params) do
      {
        name: FFaker::Name.name,
        email: FFaker::Internet.email,
        cellphone: FFaker::PhoneNumberBR.mobile_phone_number,
        cpf: FFaker::IdentificationBR.cpf,
        password: "12345678",
        user_tags: ["group-slug"],
        default_auth_flow: "true",
        custom_field_1: "123"
      }
    end

    let(:created_user) { create(:user, params.excluding(:user_tags, :default_auth_flow, :custom_field_1, :telemedicine)) }

    context "when current client employee is admin lecupon" do
      let!(:client_employee) { create(:client_employee, :admin_lecupon) }
      let(:business) { create(:business, :with_cashback) }

      context "when creating user to any business" do
        context "when cpf is banned" do
          before do
            BannedCpf.create!(cpf: params[:cpf])
          end

          it "must render error" do
            path = [
              "/client/v2/businesses/#{business.cnpj}/users",
              "/client/v2/users"
            ].sample
            post(path, headers:, params:)

            expect(response_hash["error"]).to include(I18n.t("user.sign_up.banned_cpf"))
          end
        end

        it "must be created" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/users",
            "/client/v2/users"
          ].sample
          post(path, headers:, params:)

          expect(response).to be_created
          expect(response_hash.keys).to match_array(serialized_keys)
        end

        context "when passing non-null params as null" do
          context "when column has a default value to replace" do
            let(:params) do
              {
                name: FFaker::Name.name,
                email: FFaker::Internet.email,
                cellphone: FFaker::PhoneNumberBR.mobile_phone_number,
                cpf: FFaker::IdentificationBR.cpf,
                password: "12345678",
                user_tags: ["group-slug"],
                default_auth_flow: nil,
                custom_field_1: "123"
              }
            end

            it "must be created and replace non-null param with default value" do
              expect do
                path = [
                  "/client/v2/businesses/#{business.cnpj}/users",
                  "/client/v2/users"
                ].sample
                post(path, headers:, params:)

                expect(response).to be_created
                expect(response_hash.keys).to match_array(serialized_keys)
              end.to change(User, :count)
                .and change(Wallet, :count).by(1)

              user = User.last
              expect(user.default_auth_flow).to eq(false)
            end
          end

          context "when column does not have a default value to replace" do
            let(:incomplete_params) do
              {
                name: nil,
                email: FFaker::Internet.email,
                cellphone: FFaker::PhoneNumberBR.mobile_phone_number,
                cpf: FFaker::IdentificationBR.cpf,
                password: "12345678",
                user_tags: ["group-slug"],
                custom_field_1: "123"
              }
            end

            it "must render error due to missing params" do
              expect do
                path = [
                  "/client/v2/businesses/#{business.cnpj}/users",
                  "/client/v2/users"
                ].sample
                post path, headers:, params: incomplete_params

                expect(response).to be_precondition_failed
              end.not_to change(User, :count)
            end
          end

          context "when passing authorized_user params" do
            let(:params) do
              {
                name: FFaker::Name.name,
                email: FFaker::Internet.email,
                cellphone: FFaker::PhoneNumberBR.mobile_phone_number,
                cpf: FFaker::IdentificationBR.cpf,
                password: "12345678",
                default_auth_flow: true
              }
            end

            it "must assign new data to authorized_user" do
              path = [
                "/client/v2/businesses/#{business.cnpj}/users",
                "/client/v2/users"
              ].sample
              post(path, headers:, params:)

              expect(response).to be_created

              user = User.last
              expect(user.authorized_user.default_auth_flow).to eq(true)
            end
          end
        end
      end
    end

    context "when current client employee is admin client" do
      let!(:client_employee) { create(:client_employee, :admin_client, businesses: [business]) }

      context "when creating user to a related business" do
        it "must be created" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/users",
            "/client/v2/users"
          ].sample
          post(path, headers:, params:)

          expect(response).to be_created
          expect(response_hash.keys).to match_array(serialized_keys)
        end
      end

      context "when business unrelated to the current user" do
        let(:client_employee) { create(:client_employee, :admin_client) }
        let(:unrelated_business) { create(:business) }
        let(:headers) do
          {
            "X-ClientEmployee-Email": client_employee.email,
            "X-ClientEmployee-Token": client_employee.authentication_token,
            "Tenant-id": unrelated_business.cnpj
          }
        end

        it "returns not found" do
          path = [
            "/client/v2/businesses/#{unrelated_business.cnpj}/users",
            "/client/v2/users"
          ].sample
          post(path, headers:, params:)

          expect(response).to have_http_status(:not_found)
          expect(response_hash["error"]).to eq("Business não encontrado")
        end
      end
    end

    context "when missing required param" do
      let(:headers) do
        {
          "X-ClientEmployee-Email": client_employee.email,
          "X-ClientEmployee-Token": client_employee.authentication_token,
          "Tenant-id": business.cnpj
        }
      end
      let(:invalid_params) do
        {
          name: FFaker::Name.name,
          email: FFaker::Internet.email,
          password: "123456789",
          cpf: nil
        }
      end

      it "must render error" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/users",
          "/client/v2/users"
        ].sample
        post(path, headers:, params: invalid_params)

        expect(response).to have_http_status(:precondition_failed)
      end
    end
  end
end
