# frozen_string_literal: true

require "rails_helper"
require "support/shared_examples/client_employee_request_shared_examples"

RSpec.describe Client::V2::Users::UserDestroyRequestsController, type: :request do
  let!(:business) { create(:business, :oab, cnpj: "03814381000130") }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  let(:response_hash) { JSON.parse(response.body) }
  let(:serialized_keys) { %w[reason request_status requester_type created_at] }

  describe "#show" do
    let!(:user) { create(:user, business:) }

    let!(:old_user_destroy_request) do
      create(:user_destroy_request,
        user:,
        request_status: Enums::UserDestroyRequest::Status::CANCELED,
        requester_type: Enums::UserDestroyRequest::Requesters::CLIENT,
        created_at: 2.days.ago)
    end

    let!(:user_destroy_request) do
      create(:user_destroy_request,
        user:,
        request_status: Enums::UserDestroyRequest::Status::UNPROCESSED,
        requester_type: Enums::UserDestroyRequest::Requesters::CLIENT,
        created_at: 1.day.ago)
    end

    context "when client employee is admin lecupon" do
      let(:client_employee) { create(:client_employee, :admin_lecupon) }

      it "must render unprocessed user destroy request" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/users/#{user.id}/user_destroy_requests",
          "/client/v2/users/#{user.id}/user_destroy_requests"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:ok)
        expect(response_hash.dig("request_status", "value")).to eq(Enums::UserDestroyRequest::Status::UNPROCESSED)
        expect(response_hash.keys).to match_array(serialized_keys)
      end
    end

    context "when client employee is admin client" do
      let(:client_employee) { create(:client_employee, :admin_client, businesses: [business]) }

      context "when client employee can manage user" do
        it "must render unprocessed user destroy request" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/users/#{user.id}/user_destroy_requests",
            "/client/v2/users/#{user.id}/user_destroy_requests"
          ].sample
          get(path, headers:)

          expect(response).to have_http_status(:ok)
          expect(response_hash.dig("request_status", "value")).to eq(Enums::UserDestroyRequest::Status::UNPROCESSED)
          expect(response_hash.keys).to match_array(serialized_keys)
        end
      end

      context "when client employe cannot manage user" do
        let(:another_business) { create(:business) }
        let(:client_employee) { create(:client_employee, :admin_client, businesses: [another_business]) }

        before do
          path = [
            "/client/v2/businesses/#{business.cnpj}/users/#{user.id}/user_destroy_requests",
            "/client/v2/users/#{user.id}/user_destroy_requests"
          ].sample
          get(path, headers:)
        end

        it_behaves_like "unauthorized client_employee"
      end
    end

    context "when user does not have an unprocessed destroy requested" do
      let(:client_employee) { create(:client_employee, :admin_client, businesses: [business]) }

      let!(:user) { create(:user, business:) }
      let!(:user_destroy_request) do
        create(:user_destroy_request,
          user:,
          request_status: Enums::UserDestroyRequest::Status::PROCESSED,
          requester_type: Enums::UserDestroyRequest::Requesters::CLIENT)
      end

      it "must render not found error" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/users/#{user.id}/user_destroy_requests",
          "/client/v2/users/#{user.id}/user_destroy_requests"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:not_found)
      end
    end
  end

  describe "#create" do
    let!(:user) { create(:user, business:) }
    let(:params) { {reason: "any reason"} }

    context "when client employee is admin lecupon" do
      let(:client_employee) { create(:client_employee, :admin_lecupon) }
      let(:headers) do
        {
          "X-ClientEmployee-Email": client_employee.email,
          "X-ClientEmployee-Token": client_employee.authentication_token,
          "Tenant-id": business.cnpj
        }
      end

      it "must render sucessfull" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/users/#{user.id}/user_destroy_requests",
          "/client/v2/users/#{user.id}/user_destroy_requests"
        ].sample
        post(path, params:, headers:)

        expect(response).to have_http_status(:ok)
        expect(response_hash.keys).to match_array(serialized_keys)
      end

      it "must create user destroy request" do
        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/users/#{user.id}/user_destroy_requests",
            "/client/v2/users/#{user.id}/user_destroy_requests"
          ].sample
          post path, params:, headers:
        end.to change(UserDestroyRequest, :count).by(1)
      end
    end

    context "when client employee is admin client" do
      let(:client_employee) { create(:client_employee, :admin_client, businesses: [business]) }

      context "when client employee can manage user" do
        it "must render sucessfull" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/users/#{user.id}/user_destroy_requests",
            "/client/v2/users/#{user.id}/user_destroy_requests"
          ].sample
          post(path, params:, headers:)

          expect(response).to have_http_status(:ok)
          expect(response_hash.keys).to match_array(serialized_keys)
        end

        it "must call user destroy request create service" do
          expect do
            path = [
              "/client/v2/businesses/#{business.cnpj}/users/#{user.id}/user_destroy_requests",
              "/client/v2/users/#{user.id}/user_destroy_requests"
            ].sample
            post path, params:, headers:
          end.to change(UserDestroyRequest, :count).by(1)
        end
      end

      context "when client employe cannot manage user" do
        let(:another_business) { create(:business) }
        let(:client_employee) { create(:client_employee, :admin_client, businesses: [another_business]) }

        before do
          path = [
            "/client/v2/businesses/#{business.cnpj}/users/#{user.id}/user_destroy_requests",
            "/client/v2/users/#{user.id}/user_destroy_requests"
          ].sample
          post path, params:, headers:
        end

        it_behaves_like "unauthorized client_employee"
      end
    end
  end

  describe "#destroy" do
    let!(:user) { create(:user, business:) }
    let!(:user_destroy_request) do
      create(:user_destroy_request,
        user:,
        request_status: Enums::UserDestroyRequest::Status::UNPROCESSED,
        requester_type: Enums::UserDestroyRequest::Requesters::CLIENT)
    end

    context "when client employee is admin lecupon" do
      let(:client_employee) { create(:client_employee, :admin_lecupon) }

      it "must update user destroy request status to cancelled" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/users/#{user.id}/user_destroy_requests",
          "/client/v2/users/#{user.id}/user_destroy_requests"
        ].sample
        delete(path, headers:)

        expect(response).to have_http_status(:ok)
        expect(response_hash.dig("request_status", "value")).to eq(Enums::UserDestroyRequest::Status::CANCELED)
        expect(response_hash.keys).to match_array(serialized_keys)
      end
    end

    context "when client employee is admin client" do
      let(:client_employee) { create(:client_employee, :admin_client, businesses: [business]) }

      context "when client employee can manage user" do
        it "must update user destroy request status to cancelled" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/users/#{user.id}/user_destroy_requests",
            "/client/v2/users/#{user.id}/user_destroy_requests"
          ].sample
          delete(path, headers:)

          expect(response).to have_http_status(:ok)
          expect(response_hash.dig("request_status", "value")).to eq(Enums::UserDestroyRequest::Status::CANCELED)
          expect(response_hash.keys).to match_array(serialized_keys)
        end
      end

      context "when client employe cannot manage user" do
        let(:another_business) { create(:business) }
        let(:client_employee) { create(:client_employee, :admin_client, businesses: [another_business]) }

        before do
          path = [
            "/client/v2/businesses/#{business.cnpj}/users/#{user.id}/user_destroy_requests",
            "/client/v2/users/#{user.id}/user_destroy_requests"
          ].sample
          delete(path, headers:)
        end

        it_behaves_like "unauthorized client_employee"
      end
    end

    context "when user does not have an unprocessed destroy requested" do
      let(:client_employee) { create(:client_employee, :admin_client, businesses: [business]) }

      let!(:user) { create(:user, business:) }
      let!(:user_destroy_request) do
        create(:user_destroy_request,
          user:,
          request_status: Enums::UserDestroyRequest::Status::PROCESSED,
          requester_type: Enums::UserDestroyRequest::Requesters::CLIENT)
      end

      it "must render not found error" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/users/#{user.id}/user_destroy_requests",
          "/client/v2/users/#{user.id}/user_destroy_requests"
        ].sample
        delete(path, headers:)

        expect(response).to have_http_status(:not_found)
      end
    end
  end
end
