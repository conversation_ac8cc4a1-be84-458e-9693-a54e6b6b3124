# frozen_string_literal: true

require "rails_helper"
require "support/shared_examples/client_employee_request_shared_examples"

RSpec.describe Client::V2::Users::SmartLoginController, type: :request do
  let!(:business) { create(:business, :oab, cnpj: "03814381000130") }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end
  let(:response_hash) { JSON.parse(response.body) }
  let(:serialized_keys) { %w[app_smart_link web_smart_link smart_token] }

  describe "#create" do
    let!(:user) { create(:user, business:) }
    let(:client_employee) { create(:client_employee, :admin_client, businesses: [business]) }

    it "returns successful" do
      path = [
        "/client/v2/businesses/#{business.cnpj}/users/#{user.id}/smart_login",
        "/client/v2/users/#{user.id}/smart_login"
      ].sample
      post(path, headers:)

      user.reload
      expect(response).to be_successful
      expect(response_hash["access_token"]).to eq(user.api_token)
    end

    context "when user is inactive" do
      let!(:inactive_user) { create(:user, business:, active: false) }

      it "returns record not found" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/users/#{inactive_user.id}/smart_login",
          "/client/v2/users/#{inactive_user.id}/smart_login"
        ].sample
        post(path, headers:)

        expect(response).to be_unprocessable
      end
    end

    context "when authentication headers are missing" do
      context "when client employee credentials are valid" do
        let(:client_employee) { create(:client_employee, :admin_client, businesses: [business], password: "12345678") }
        let(:params) do
          {
            email: client_employee.email,
            password: "12345678"
          }
        end

        it "returns successful" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/users/#{user.id}/smart_login",
            "/client/v2/users/#{user.id}/smart_login"
          ].sample
          post(path, headers:)

          user.reload
          expect(response).to be_successful
          expect(response_hash["access_token"]).to eq(user.api_token)
        end
      end

      context "when client employee credentials are invalid" do
        let(:params) do
          {
            email: client_employee.email,
            password: "wrong-password"
          }
        end

        it "must be unauthorized" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/users/#{user.id}/smart_login",
            "/client/v2/users/#{user.id}/smart_login"
          ].sample
          post(path, params:)

          expect(response).to be_unauthorized
        end
      end
    end

    context "when business unrelated to the current user" do
      let(:client_employee) { create(:client_employee, :admin_client) }

      it "returns not found" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/users/#{user.id}/smart_login",
          "/client/v2/users/#{user.id}/smart_login"
        ].sample
        post(path, headers:)

        expect(response).to have_http_status(:not_found)
        expect(response_hash["error"]).to eq("Business não encontrado")
      end
    end

    context "without client_employee authentication token and credentials" do
      before do
        path = [
          "/client/v2/businesses/#{business.cnpj}/users/#{user.id}/smart_login",
          "/client/v2/users/#{user.id}/smart_login"
        ].sample
        post(path, headers:)
      end

      it_behaves_like "unauthorized client_employee"
    end
  end
end
