# frozen_string_literal: true

require "rails_helper"

RSpec.describe Client::V2::Users::WalletsController, type: :request do
  let!(:client_employee) { create(:client_employee, :admin_client, businesses: [business]) }
  let!(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end
  let(:response_body) { JSON.parse(response.body) }

  describe "#index" do
    let(:business) { create(:business, cashback: true) }
    let(:user) { create(:user, business:) }

    context "when user has wallets" do
      let!(:cashback_wallet) { create(:wallet, :cashback, user:, balance: 2749) }
      let!(:membership_wallet) { create(:wallet, :membership, user:, balance: 0) }
      let!(:wallet_of_another_user) { create(:wallet, :cashback, user: create(:user, business:), balance: 8165) }
      let(:user_identifier) { [user.id, user.cpf].sample }

      context "without filter" do
        it "renders ok" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/users/#{user_identifier}/wallets",
            "/client/v2/users/#{user_identifier}/wallets"
          ].sample
          get(path, headers:)

          expect(response).to have_http_status(:ok)
          expect(response_body).to match_array([
            {
              "id" => cashback_wallet.id,
              "balance" => 2749,
              "type" => "external"
            },
            {
              "id" => membership_wallet.id,
              "balance" => 0,
              "type" => "internal"
            }
          ])
        end
      end

      context "when filtering by currency" do
        let(:params) { {type: "external"} }

        it "renders ok" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/users/#{user_identifier}/wallets",
            "/client/v2/users/#{user_identifier}/wallets"
          ].sample
          get(path, params:, headers:)

          expect(response).to have_http_status(:ok)
          expect(response_body).to match_array([
            {
              "id" => cashback_wallet.id,
              "balance" => 2749,
              "type" => "external"
            }
          ])
        end
      end

      context "when filtering by wrong currency" do
        let(:params) { {type: "invalid"} }

        it "renders ok" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/users/#{user_identifier}/wallets",
            "/client/v2/users/#{user_identifier}/wallets"
          ].sample
          get(path, params:, headers:)

          expect(response).to have_http_status(:ok)
          expect(response_body).to be_empty
        end
      end
    end

    context "when user does not belong to business" do
      let(:user) { create(:user, business: create(:business)) }

      it "renders not found" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/users/#{user.id}/wallets",
          "/client/v2/users/#{user.id}/wallets"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:not_found)
      end
    end

    context "when business is inactive" do
      let(:business) { create(:business, cashback: true, status: Business::Status::INACTIVE) }

      it "renders forbidden" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/users/#{user.id}/wallets",
          "/client/v2/users/#{user.id}/wallets"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:forbidden)
      end
    end

    context "when business disabled cashback" do
      let(:business) { create(:business, cashback: false) }

      it "renders forbidden" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/users/#{user.id}/wallets",
          "/client/v2/users/#{user.id}/wallets"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:forbidden)
      end
    end
  end
end
