# frozen_string_literal: true

require "rails_helper"

RSpec.describe Client::V2::UsersController, type: :request do
  let(:business) { create(:business) }
  let(:user) { create(:user, business:) }
  let(:client_employee) { create(:client_employee, :admin_client, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end
  let(:response_hash) { JSON.parse(response.body) }
  let(:serialized_keys) do
    %w[id name email cellphone cpf active activated_at user_tags business_id default_auth_flow telemedicine
      custom_field_1 custom_field_2 custom_field_3 custom_field_4 custom_field_5 custom_field_6 custom_field_7 custom_field_8
      wallet tags]
  end

  describe "#show" do
    context "when is authorized client employee" do
      context "when user exists in current business users and user does not have wallet" do
        it "returns user by id" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/users/#{user.id}",
            "/client/v2/users/#{user.id}"
          ].sample
          get(path, headers:)

          expect(response).to be_successful
          expect(response_hash.keys).to match_array(serialized_keys)
          expect(response_hash["id"]).to eq(user.id)
          expect(response_hash["wallet"]).to eq(nil)
        end

        it "returns user by cpf" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/users/#{user.cpf}",
            "/client/v2/users/#{user.cpf}"
          ].sample
          get(path, headers:)

          expect(response).to be_successful
          expect(response_hash.keys).to match_array(serialized_keys)
          expect(response_hash["id"]).to eq(user.id)
          expect(response_hash["wallet"]).to eq(nil)
        end
      end

      context "when user exists in current business users and user has wallet and business enabled cashback" do
        let(:business) { create(:business, cashback: true, cashback_wallet_destination: Wallet::Kind::CASHBACK) }
        let!(:user_wallet) { create(:wallet, :cashback, user:, balance: 1672) }

        it "returns user by id" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/users/#{user.id}",
            "/client/v2/users/#{user.id}"
          ].sample
          get(path, headers:)

          expect(response).to be_successful
          expect(response_hash.keys).to match_array(serialized_keys)
          expect(response_hash["id"]).to eq(user.id)
          expect(response_hash["wallet"]).to eq({"balance" => 1672, "id" => user_wallet.id})
        end
      end

      context "when user exists in current business users and user has wallet and business disabled cashback" do
        let(:business) { create(:business, cashback: false, cashback_wallet_destination: Wallet::Kind::CASHBACK) }
        let!(:user_wallet) { create(:wallet, :cashback, user:, balance: 1672) }

        it "returns user by id" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/users/#{user.id}",
            "/client/v2/users/#{user.id}"
          ].sample
          get(path, headers:)

          expect(response).to be_successful
          expect(response_hash.keys).to match_array(serialized_keys)
          expect(response_hash["id"]).to eq(user.id)
          expect(response_hash["wallet"]).to eq(nil)
        end
      end

      context "when user does not exist in current business users" do
        let(:non_business_user_id) { "any_non_business_user_id_id" }

        it "returns not found" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/users/#{non_business_user_id}",
            "/client/v2/users/#{non_business_user_id}"
          ].sample
          get(path, headers:)

          expect(response).to be_not_found
        end
      end

      context "when user exist in another business users" do
        let(:user) { create(:user) }

        it "returns not found by user id" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/users/#{user.id}",
            "/client/v2/users/#{user.id}"
          ].sample
          get(path, headers:)

          expect(response).to be_not_found
        end

        it "returns not found by user cpf" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/users/#{user.cpf}",
            "/client/v2/users/#{user.cpf}"
          ].sample
          get(path, headers:)

          expect(response).to be_not_found
        end
      end
    end

    context "when not authorized" do
      it "returns unauthorized by user id" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/users/#{user.id}",
          "/client/v2/users/#{user.id}"
        ].sample
        get path

        expect(response).to be_unauthorized
      end

      it "returns unauthorized by user cpf" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/users/#{user.cpf}",
          "/client/v2/users/#{user.cpf}"
        ].sample
        get path

        expect(response).to be_unauthorized
      end
    end
  end
end
