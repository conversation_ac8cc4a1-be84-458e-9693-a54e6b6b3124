# frozen_string_literal: true

require "rails_helper"

RSpec.describe Client::V2::Users::SmartLinkController, type: :request do
  let(:response_hash) { JSON.parse(response.body) }

  describe "#create" do
    let(:client_employee) { create(:client_employee, :admin_client, businesses: [business]) }
    let(:business) {
      project_config.reload
      project_config.business
    }
    let(:headers) do
      {
        "X-ClientEmployee-Email": client_employee.email,
        "X-ClientEmployee-Token": client_employee.authentication_token,
        "Tenant-id": business.cnpj
      }
    end

    let(:serialized_keys) { %w[app_smart_link web_smart_link smart_token] }

    context "when cpf is authorized" do
      context "when user was inactived" do
        let!(:project_config) do
          create(
            :project_config,
            google_identifier: "android_id",
            apple_identifier: "ios_id",
            firebase_dynamic_link_domain: "app.page.link",
            web_domain: "web.example.com"
          )
        end
        let!(:inactive_user) { create(:user, business:, active: false) }

        it "returns record not found" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/users/#{inactive_user.id}/smart_link",
            "/client/v2/users/#{inactive_user.id}/smart_link"
          ].sample
          post(path, headers:)

          expect(response).to have_http_status(:unprocessable_entity)
          expect(response_hash["error"]).to eq(I18n.t("smart_link.missing_user"))
        end
      end

      context "when user does not exist" do
        let!(:business) { create(:business, :with_cashback) }

        context "when project config has smart_link_integration" do
          let!(:business) { create(:business, :with_cashback, :with_smart_link_integration) }
          let(:valid_cpf) { "***********" }

          it "must_create_a_user_with_integration", :vcr do
            expect do
              path = [
                "/client/v2/businesses/#{business.cnpj}/users/#{valid_cpf}/smart_link",
                "/client/v2/users/#{valid_cpf}/smart_link"
              ].sample
              post(path, headers:)
            end.to change(User, :count)
              .and change(Wallet, :count)

            expect(response).to have_http_status(:successful)
            expect(response_hash.keys).to match_array(serialized_keys)
          end
        end

        context "when project config is set to create user based on authorized on smart link creation" do
          let(:authorized_user) { create(:authorized_user, business:) }

          before do
            business.update_columns(cashback: true)

            business.project_config.update(create_user_on_smart_link: true, auth_integration_type: nil)
          end

          it "must create a user based on authorized_user" do
            expect do
              path = [
                "/client/v2/businesses/#{business.cnpj}/users/#{authorized_user.cpf}/smart_link",
                "/client/v2/users/#{authorized_user.cpf}/smart_link"
              ].sample
              post path, headers:
            end.to change(User, :count)
            user = User.first
            expect(user.name).to eq(Utils::NameNormalizer.call(authorized_user.name))
            expect(user.email).to eq(authorized_user.email)
            expect(user.cpf).to eq(authorized_user.cpf)
            expect(user.business).to eq(authorized_user.business)
            expect(user.wallets).to exist
          end
        end

        context "when project config is not set to create user and dont have smart link integration" do
          let!(:project_config) do
            create(
              :project_config,
              google_identifier: "android_id",
              apple_identifier: "ios_id",
              firebase_dynamic_link_domain: "app.page.link",
              web_domain: "web.example.com",
              create_user_on_smart_link: false,
              auth_integration_type: nil
            )
          end
          let(:any_cpf) { CPF.new(FFaker::IdentificationBR.cpf).stripped }

          it "must not create user and return not found" do
            expect do
              path = [
                "/client/v2/businesses/#{business.cnpj}/users/#{any_cpf}/smart_link",
                "/client/v2/users/#{any_cpf}/smart_link"
              ].sample
              post(path, headers:)

              expect(response).to have_http_status(:unprocessable_entity)
              expect(response_hash["error"]).to eq(I18n.t("smart_link.missing_user"))
            end.not_to change(User, :count)
          end
        end
      end

      context "when authentication headers are missing" do
        let!(:project_config) do
          create(:project_config,
            web_domain: "web.example.com",
            firebase_dynamic_link_domain: "",
            apple_identifier: "",
            google_identifier: "")
        end
        let(:business) { create(:business, project_config:) }
        let!(:user) { create(:user, business:) }

        context "when client employee credentials are valid" do
          let(:client_employee) { create(:client_employee, :admin_client, businesses: [business], password: "12345678") }
          let(:params) do
            {
              email: client_employee.email,
              password: "12345678"
            }
          end

          it "returns successful" do
            expect(User::SmartTokenExpireWorker).to receive(:perform_in)
            path = [
              "/client/v2/businesses/#{business.cnpj}/users/#{user.id}/smart_link",
              "/client/v2/users/#{user.id}/smart_link"
            ].sample
            post(path, params:)

            user.reload

            expect(response).to have_http_status(:successful)
            expect(response_hash["web_smart_link"]).to eq("https://web.example.com/login?token=#{user.smart_token}")
            expect(response_hash["app_smart_link"]).to be_nil
          end

          context "when passing redirect_to" do
            before do
              params[:redirect_to] = "https://google.com"
            end

            it "returns successful" do
              expect(User::SmartTokenExpireWorker).to receive(:perform_in)
              path = [
                "/client/v2/businesses/#{business.cnpj}/users/#{user.id}/smart_link",
                "/client/v2/users/#{user.id}/smart_link"
              ].sample
              post(path, params:)

              user.reload

              expect(response).to have_http_status(:successful)
              expect(response_hash["web_smart_link"]).to eq("https://web.example.com/login?token=#{user.smart_token}&redirect_to=https://google.com")
              expect(response_hash["app_smart_link"]).to be_nil
            end
          end
        end

        context "when client employee credentials are invalid" do
          let(:params) do
            {
              email: client_employee.email,
              password: "wrong-password"
            }
          end

          it "must be unauthorized" do
            path = [
              "/client/v2/businesses/#{business.cnpj}/users/#{user.id}/smart_link",
              "/client/v2/users/#{user.id}/smart_link"
            ].sample
            post(path, params:)

            expect(response).to be_unauthorized
          end
        end
      end

      context "when web configs are given" do
        let!(:project_config) do
          create(:project_config,
            web_domain: "web.example.com",
            firebase_dynamic_link_domain: "",
            apple_identifier: "",
            google_identifier: "")
        end
        let(:business) { create(:business, project_config:) }
        let!(:user) { create(:user, business:) }

        it "returns successful" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/users/#{user.id}/smart_link",
            "/client/v2/users/#{user.id}/smart_link"
          ].sample
          post(path, headers:)

          user.reload

          expect(response).to have_http_status(:successful)
          expect(response_hash["web_smart_link"]).to eq("https://web.example.com/login?token=#{user.smart_token}")
          expect(response_hash["app_smart_link"]).to be_nil
        end
      end

      context "when mobile config are given" do
        let!(:project_config) do
          create(:project_config,
            web_domain: "",
            firebase_dynamic_link_domain: "foo",
            google_identifier: "bar",
            apple_identifier: "any")
        end
        let(:business) { create(:business, project_config:) }
        let!(:user) { create(:user, business:) }

        it "returns successful" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/users/#{user.id}/smart_link",
            "/client/v2/users/#{user.id}/smart_link"
          ].sample
          post(path, headers:)

          expect(response).to have_http_status(:successful)
          expect(response_hash["app_smart_link"]).to be_present
          expect(response_hash["web_smart_link"]).to be_nil
        end
      end

      context "when web configs and mobile are not given" do
        let!(:project_config) do
          create(:project_config,
            web_domain: "",
            firebase_dynamic_link_domain: "",
            google_identifier: "",
            apple_identifier: "")
        end
        let(:business) { create(:business, project_config:) }
        let!(:user) { create(:user, business:) }

        it "returns error" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/users/#{user.id}/smart_link",
            "/client/v2/users/#{user.id}/smart_link"
          ].sample
          post(path, headers:)

          expect(response).to have_http_status(:unprocessable_entity)
          expect(response_hash["error"]).to eq(I18n.t("smart_link.setup_required"))
        end
      end
    end

    context "when cpf is not authorized" do
      let!(:project_config) do
        create(
          :project_config,
          google_identifier: "android_id",
          apple_identifier: "ios_id",
          firebase_dynamic_link_domain: "app.page.link",
          web_domain: "web.example.com"
        )
      end
      let(:unused_cpf) { CPF.new(FFaker::IdentificationBR.cpf).stripped }

      it "must render unprocessable entity" do
        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/users/#{unused_cpf}/smart_link",
            "/client/v2/users/#{unused_cpf}/smart_link"
          ].sample
          post(path, headers:)

          expect(response).to have_http_status(:unprocessable_entity)
          expect(response_hash["error"]).to eq(I18n.t("smart_link.missing_user"))
        end.not_to change(User, :count)
      end
    end
  end
end
