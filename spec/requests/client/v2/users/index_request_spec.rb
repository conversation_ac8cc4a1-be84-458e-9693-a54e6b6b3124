# frozen_string_literal: true

require "rails_helper"

RSpec.describe Client::V2::UsersController, type: :request do
  let(:business) { create(:business) }
  let(:client_employee) { create(:client_employee, :admin_client, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end
  let(:response_hash) { JSON.parse(response.body) }
  let(:serialized_keys) do
    %w[id name email cellphone cpf active activated_at user_tags business_id default_auth_flow telemedicine
      custom_field_1 custom_field_2 custom_field_3 custom_field_4 custom_field_5 custom_field_6 custom_field_7 custom_field_8
      wallet tags]
  end

  describe "#index" do
    context "when client employee is authorized" do
      context "with users" do
        let!(:authorized_user_group) { create(:authorized_user_group, business:, name: "Grupo") }
        let(:authorized_user) { create(:authorized_user, authorized_user_group:, business:) }
        let!(:user) { create(:user, authorized_user:, business:) }
        let(:authorized_user_two) { create(:authorized_user, authorized_user_group:, business:) }
        let!(:user_two) { create(:user, authorized_user: authorized_user_two, business:) }
        let(:inactive_authorized_user) { create(:authorized_user, authorized_user_group:, active: false, business:) }
        let!(:inactive_user) { create(:user, active: false, authorized_user: inactive_authorized_user, business:) }
        let(:deleted_authorized_user) { create(:authorized_user, authorized_user_group:, business:) }
        let!(:deleted_user) { create(:user, deleted: true, authorized_user: deleted_authorized_user, business:) }
        let!(:expected_user_ids) { [user.id] }
        let(:params) do
          {
            active: "true",
            cpf: "",
            term: user.name,
            user_tags: [authorized_user_group.slug],
            page: 1
          }
        end

        context "when user does not have wallet" do
          it "returns the users filtered" do
            path = [
              "/client/v2/businesses/#{business.cnpj}/users",
              "/client/v2/users"
            ].sample
            get(path, headers:, params:)

            expect(response).to be_successful
            expect(response_hash.map(&:keys)).to all(match_array(serialized_keys))
            expect(response_hash.pluck("id")).to eq(expected_user_ids)
            expect(response_hash.pluck("wallet")).to all eq nil
            expect(response_hash.pluck("user_tags")).to include([{"name" => "Grupo", "slug" => "grupo"}])
          end
        end

        context "when user has wallet and business enabled cashback" do
          let(:business) { create(:business, cashback: true, cashback_wallet_destination: Wallet::Kind::CASHBACK) }
          let!(:user_wallet) { create(:wallet, :cashback, user:, balance: 1672) }

          it "returns the users filtered" do
            path = [
              "/client/v2/businesses/#{business.cnpj}/users",
              "/client/v2/users"
            ].sample
            get(path, headers:, params:)

            expect(response).to be_successful
            expect(response_hash.map(&:keys)).to all(match_array(serialized_keys))
            expect(response_hash.pluck("id")).to eq(expected_user_ids)
            expect(response_hash.pluck("wallet")).to eq([{"balance" => 1672, "id" => user_wallet.id}])
          end
        end

        context "when user has wallet and business disabled cashback" do
          let(:business) { create(:business, cashback: false, cashback_wallet_destination: Wallet::Kind::CASHBACK) }
          let!(:user_wallet) { create(:wallet, :cashback, user:, balance: 1672) }

          it "returns the users filtered" do
            path = [
              "/client/v2/businesses/#{business.cnpj}/users",
              "/client/v2/users"
            ].sample
            get(path, headers:, params:)

            expect(response).to be_successful
            expect(response_hash.map(&:keys)).to all(match_array(serialized_keys))
            expect(response_hash.pluck("id")).to eq(expected_user_ids)
            expect(response_hash.pluck("wallet")).to all eq nil
          end
        end
      end

      context "without users" do
        it "returns empty" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/users",
            "/client/v2/users"
          ].sample
          get(path, headers:)

          expect(response).to be_successful
          expect(response_hash).to be_empty
        end
      end
    end

    context "when not authorized" do
      it "renders unauthorized" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/users",
          "/client/v2/users"
        ].sample
        get path

        expect(response).to be_unauthorized
      end
    end
  end
end
