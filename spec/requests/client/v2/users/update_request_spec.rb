# frozen_string_literal: true

require "rails_helper"
require "support/shared_examples/client_employee_request_shared_examples"

RSpec.describe Client::V2::UsersController, type: :request do
  let!(:business) { create(:business, :oab, cnpj: "03814381000130") }

  let!(:client_employee) { create(:client_employee, :admin_client, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  let(:response_hash) { JSON.parse(response.body) }
  let(:serialized_keys) do
    %w[id name email cellphone cpf active activated_at user_tags business_id default_auth_flow telemedicine
      custom_field_1 custom_field_2 custom_field_3 custom_field_4 custom_field_5 custom_field_6 custom_field_7 custom_field_8
      wallet tags]
  end

  describe "#update" do
    let!(:user) { create(:user, business:, authorized_user_params: {tags: "Old Tag"}) }
    let(:authorized_user) { user.authorized_user }
    let(:params) do
      {
        name: FFaker::Name.name,
        user_tags: ["group-slug"],
        default_auth_flow: "true",
        custom_field_1: "metadata_1",
        tags: ["Tag 1", "Tag 2"]
      }
    end

    context "when current client employee is admin lecupon" do
      let!(:client_employee) { create(:client_employee, :admin_lecupon) }
      let(:business) { create(:business) }

      context "when updating user of any business" do
        context "when reactivating user with banned cpf" do
          let(:params) do
            {active: true}
          end

          before do
            BannedCpf.create!(cpf: user.cpf)
          end

          it "must render error" do
            path = [
              "/client/v2/businesses/#{business.cnpj}/users/#{user.id}",
              "/client/v2/users/#{user.id}"
            ].sample
            patch(path, headers:, params:)

            expect(response_hash["error"]).to include(I18n.t("user.sign_up.banned_cpf"))
          end
        end

        it "returns successful by user id" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/users/#{user.id}",
            "/client/v2/users/#{user.id}"
          ].sample
          expect(authorized_user.tags).to eq(["Old Tag"])
          patch(path, headers:, params:)

          expect(response).to have_http_status(:ok)
          expect(response_hash.keys).to match_array(serialized_keys)
          user.reload
          expect(user.name).to eq(Utils::NameNormalizer.call(params[:name]))
          expect(user.default_auth_flow).to eq(params[:default_auth_flow].to_boolean)
          expect(authorized_user.reload.custom_field_1).to eq(params[:custom_field_1])
          expect(authorized_user.authorized_user_group.slug).to eq(params[:user_tags].first)
          expect(authorized_user.tags).to eq(["Tag 1", "Tag 2"])
        end

        it "returns successful by user cpf" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/users/#{user.cpf}",
            "/client/v2/users/#{user.cpf}"
          ].sample
          patch(path, headers:, params:)

          expect(response).to have_http_status(:ok)
          expect(response_hash.keys).to match_array(serialized_keys)

          user.reload
          expect(user.name).to eq(Utils::NameNormalizer.call(params[:name]))
          expect(user.default_auth_flow).to eq(params[:default_auth_flow].to_boolean)
          expect(authorized_user.custom_field_1).to eq(params[:custom_field_1])
          expect(authorized_user.reload.authorized_user_group.slug).to eq(params[:user_tags].first)
        end

        context "when updating to unrelated business" do
          let(:new_business) { create(:business) }
          let(:params) do
            {
              name: FFaker::Name.name,
              user_tags: ["group-slug"],
              business_id: new_business.id
            }
          end

          it "must render forbidden" do
            expect do
              path = [
                "/client/v2/businesses/#{business.cnpj}/users/#{user.cpf}",
                "/client/v2/users/#{user.cpf}"
              ].sample
              patch(path, headers:, params:)

              expect(response).to have_http_status(:forbidden)
            end.not_to change { user }
          end
        end

        context "when with user_tags params" do
          before do
            authorized_user.update(authorized_user_group: create(:authorized_user_group, business: authorized_user.business))
          end

          it "must update authorized user group" do
            expect do
              path = [
                "/client/v2/businesses/#{business.cnpj}/users/#{user.cpf}",
                "/client/v2/users/#{user.cpf}"
              ].sample
              patch(path, headers:, params:)

              expect(response).to have_http_status(:ok)
              expect(response_hash.keys).to match_array(serialized_keys)
              authorized_user.reload
            end.to change(authorized_user, :authorized_user_group)
              .and change(AuthorizedUserGroup, :count)
          end
        end
      end
    end

    context "when current client employee is admin client" do
      let!(:client_employee) { create(:client_employee, :admin_client, businesses: [business]) }

      context "when updating user of a related business" do
        it "returns successful by user id" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/users/#{user.id}",
            "/client/v2/users/#{user.id}"
          ].sample
          patch(path, headers:, params:)

          expect(response).to have_http_status(:ok)
          expect(response_hash.keys).to match_array(serialized_keys)

          user.reload
          expect(user.name).to eq(Utils::NameNormalizer.call(params[:name]))
          expect(user.default_auth_flow).to eq(params[:default_auth_flow].to_boolean)
          expect(authorized_user.custom_field_1).to eq(params[:custom_field_1])
          expect(authorized_user.reload.authorized_user_group.slug).to eq(params[:user_tags].first)
        end

        it "returns successful by user cpf" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/users/#{user.cpf}",
            "/client/v2/users/#{user.cpf}"
          ].sample
          patch(path, headers:, params:)

          expect(response).to have_http_status(:ok)
          expect(response_hash.keys).to match_array(serialized_keys)

          user.reload
          expect(user.name).to eq(Utils::NameNormalizer.call(params[:name]))
          expect(user.default_auth_flow).to eq(params[:default_auth_flow].to_boolean)
          expect(authorized_user.custom_field_1).to eq(params[:custom_field_1])
          expect(authorized_user.reload.authorized_user_group.slug).to eq(params[:user_tags].first)
        end
      end

      context "when business unrelated to the current user" do
        let(:business) { create(:business) }
        let(:client_employee) { create(:client_employee, :admin_client) }
        let(:headers) do
          {
            "X-ClientEmployee-Email": client_employee.email,
            "X-ClientEmployee-Token": client_employee.authentication_token,
            "Tenant-id": business.cnpj
          }
        end

        it "returns not found" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/users/#{user.id}",
            "/client/v2/users/#{user.id}"
          ].sample
          patch(path, headers:, params:)

          expect(response).to have_http_status(:not_found)
          expect(response_hash["error"]).to eq("Business não encontrado")
        end
      end
    end
  end
end
