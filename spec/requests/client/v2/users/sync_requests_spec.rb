# frozen_string_literal: true

require "rails_helper"
require "support/shared_examples/client_employee_request_shared_examples"
require "sidekiq/testing"

RSpec.describe Client::V2::Users::SyncController, type: :request do
  describe "#create" do
    let(:business) { create(:business) }
    let(:client_employee) { create(:client_employee, :admin_lecupon) }
    let(:headers) do
      {
        "X-ClientEmployee-Email": client_employee.email,
        "X-ClientEmployee-Token": client_employee.authentication_token,
        "Tenant-id": business.cnpj
      }
    end
    let(:response_hash) { JSON.parse(response.body) }

    context "when user params are valid" do
      let(:user_params) do
        {
          users: (0..5).collect do
            {
              name: FFaker::Name.name,
              email: FFaker::Internet.email,
              cellphone: FFaker::PhoneNumberBR.phone_number,
              cpf: CPF.new(FFaker::IdentificationBR.cpf).stripped,
              business_id: business.id,
              authorized_user_params: {tags: ["Tag 1", "Tag 2"]}
            }
          end,
          user_tags: ["group_slug"]
        }
      end

      it "must be included on the list to sync" do
        Sidekiq::Testing.inline! do
          path = [
            "/client/v2/businesses/#{business.cnpj}/users/sync",
            "/client/v2/users/sync"
          ].sample
          post path, headers:, params: user_params

          expect(response_hash["created_count"]).to eq(user_params[:users].count)
          expect(response_hash["updated_count"]).to eq(0)
          expect(response_hash["valid_list_size"]).to eq(user_params[:users].count)
          expect(response_hash["invalid_list_size"]).to eq(0)

          expect(User.last.authorized_user.tags).to match_array(["Tag 1", "Tag 2"])
        end
      end

      context "when user already exists" do
        let(:existing_users) { create_list(:user, 2, business:) }
        let(:user_params) do
          {
            users: existing_users.collect do |existing_user|
              {
                name: FFaker::Name.name,
                email: FFaker::Internet.email,
                cellphone: FFaker::PhoneNumberBR.phone_number,
                cpf: existing_user.cpf,
                business_id: business.id
              }
            end,
            user_tags: ["group_slug"]
          }
        end

        it "must increase updated count" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/users/sync",
            "/client/v2/users/sync"
          ].sample
          post path, headers:, params: user_params

          expect(response_hash["updated_count"]).to eq(existing_users.count)
          expect(response_hash["created_count"]).to eq(0)
          expect(response_hash["valid_list_size"]).to eq(existing_users.count)
          expect(response_hash["invalid_list_size"]).to eq(0)
        end
      end
    end

    context "when user params are invalid" do
      let(:valid_user_params) {
        [{
          name: FFaker::Name.name,
          email: FFaker::Internet.email,
          cellphone: FFaker::PhoneNumberBR.phone_number,
          cpf: CPF.new(FFaker::IdentificationBR.cpf).stripped,
          business_id: business.id
        }]
      }

      let(:invalid_user_params) {
        [{
          name: FFaker::Name.name,
          email: nil,
          cellphone: FFaker::PhoneNumberBR.phone_number,
          cpf: "000",
          business_id: business.id
        }]
      }
      let(:user_params) do
        {
          users: valid_user_params + invalid_user_params,
          user_tags: ["group_slug"]
        }
      end

      it "must not be included on the list to sync" do
        expect(User::BulkImportWorker).to receive(:perform_async)
          .with(valid_user_params.map(&:deep_stringify_keys), business.id, user_params[:user_tags])
        path = [
          "/client/v2/businesses/#{business.cnpj}/users/sync",
          "/client/v2/users/sync"
        ].sample
        post path, headers:, params: user_params

        expect(response_hash["valid_list_size"]).to eq(1)
        expect(response_hash["invalid_list_size"]).to eq(1)
        expect(response_hash["created_count"]).to eq(1)
        expect(response_hash["updated_count"]).to eq(0)
      end
    end

    context "when user_params list has more thant 5000 objects" do
      let(:user_params) do
        {
          users: (0..2001).collect do
            {
              name: "test"
            }
          end,
          user_tags: ["group_slug"]
        }
      end

      it "must render error" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/users/sync",
          "/client/v2/users/sync"
        ].sample
        post path, headers:, params: user_params

        expect(response).to be_unprocessable
        expect(response_hash["error"]).to eq("Limite maximo de 2 mil cadastros.")
      end
    end
  end
end
