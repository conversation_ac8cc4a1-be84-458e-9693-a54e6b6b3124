require "rails_helper"

RSpec.describe Client::V2::GiftcardConfigsController, type: :request do
  let(:business) { create(:business, create_giftcard_config: false) }
  let!(:client_employee) { create(:client_employee, :admin_lecupon) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  let(:response_hash) { JSON.parse(response.body) }

  let(:serialized_keys) {
    %w[
      limit_by_user limit_by_user_period_interval limit_by_user_period_type
    ]
  }

  describe "#show" do
    let!(:giftcard_config) { create(:giftcard_config, business:, limit_by_user: 5) }

    it "renders a successful response" do
      path = [
        "/client/v2/businesses/#{business.cnpj}/giftcard_config",
        "/client/v2/giftcard_config"
      ].sample
      get(path, headers:)

      expect(response).to be_successful
      expect(response_hash.keys).to match_array(serialized_keys)
    end

    context "when current_business is subbusiness" do
      let!(:sub_business) { create(:business, create_giftcard_config: false, main_business: business) }
      let!(:client_employee) { create(:client_employee, businesses: [sub_business]) }
      let(:headers) do
        {
          "X-ClientEmployee-Email": client_employee.email,
          "X-ClientEmployee-Token": client_employee.authentication_token,
          "Tenant-id": sub_business.cnpj
        }
      end

      it "must return main business giftcard config" do
        path = [
          "/client/v2/businesses/#{sub_business.cnpj}/giftcard_config",
          "/client/v2/giftcard_config"
        ].sample
        get(path, headers:)

        expect(response).to be_successful
        expect(response_hash.keys).to match_array(serialized_keys)
        expect(response_hash["limit_by_user"]).to eq(giftcard_config.limit_by_user)
      end
    end

    context "without client employee authorization" do
      it "renders unauthorized" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/giftcard_config",
          "/client/v2/giftcard_config"
        ].sample
        get path

        expect(response).to be_unauthorized
      end
    end

    context "with unrelated client employee authorization" do
      let(:business_two) { create(:business) }
      let!(:client_employee) { create(:client_employee, businesses: [business_two]) }
      let(:headers) do
        {
          "X-ClientEmployee-Email": client_employee.email,
          "X-ClientEmployee-Token": client_employee.authentication_token,
          "Tenant-id": business.cnpj
        }
      end

      it "renders forbidden" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/giftcard_config",
          "/client/v2/giftcard_config"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:not_found)
        expect(response_hash["error"]).to eq("Business não encontrado")
      end
    end
  end

  describe "#update" do
    let!(:giftcard_config) { create(:giftcard_config, business:) }

    let(:new_attributes) do
      {
        limit_by_user: 6
      }
    end

    context "with valid parameters" do
      it "updates the requested giftcard_config" do
        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/giftcard_config",
            "/client/v2/giftcard_config"
          ].sample
          patch(path, params: {giftcard_config: new_attributes}, headers:)

          giftcard_config.reload
        end.to change(giftcard_config, :limit_by_user).to(new_attributes[:limit_by_user])
      end
    end

    context "without client employee authorization" do
      it "renders unauthorized" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/giftcard_config",
          "/client/v2/giftcard_config"
        ].sample
        patch path, params: {giftcard_config: new_attributes}

        expect(response).to be_unauthorized
      end
    end

    context "with business_id unrelated to client employee" do
      context "when is lecupon admin" do
        it "updates the requested giftcard_config" do
          expect do
            path = [
              "/client/v2/businesses/#{business.cnpj}/giftcard_config",
              "/client/v2/giftcard_config"
            ].sample
            patch(path, params: {giftcard_config: new_attributes}, headers:)

            giftcard_config.reload
          end.to change(giftcard_config, :limit_by_user).to(new_attributes[:limit_by_user])
        end
      end

      context "when is not lecupon admin" do
        let(:business_two) { create(:business) }
        let!(:client_employee) { create(:client_employee, businesses: [business_two]) }
        let(:headers) do
          {
            "X-ClientEmployee-Email": client_employee.email,
            "X-ClientEmployee-Token": client_employee.authentication_token,
            "Tenant-id": business.cnpj
          }
        end

        it "renders not_found" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/giftcard_config",
            "/client/v2/giftcard_config"
          ].sample
          patch(path, params: {giftcard_config: new_attributes}, headers:)

          expect(response).to have_http_status(:not_found)
          expect(response_hash["error"]).to eq("Business não encontrado")
        end
      end
    end

    context "when business is sub business" do
      let(:main_business) { create(:business, create_giftcard_config: false) }
      let(:business) { create(:business, main_business:) }
      let(:new_attributes) { {limit_by_user: 7} }
      let!(:giftcard_config) { create(:giftcard_config, business:, limit_by_user: 3) }

      it "must render error" do
        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/giftcard_config",
            "/client/v2/giftcard_config"
          ].sample
          patch(path, params: {giftcard_config: new_attributes}, headers:)

          giftcard_config.reload
        end.not_to change(giftcard_config, :limit_by_user)

        expect(response).to be_unprocessable
        expect(response_hash["error"]).to eq(I18n.t("giftcard_config.errors.child_business_cannot_edit"))
      end
    end
  end
end
