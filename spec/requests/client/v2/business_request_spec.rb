# frozen_string_literal: true

require "rails_helper"
require "sidekiq/testing"
require "support/shared_examples/client_employee_request_shared_examples"

RSpec.describe Client::V2::BusinessesController, type: :request do
  let(:serialized_keys) do
    %w[id name cnpj user_count authorized_user_count active main_business_id
      api_key signature_secret api_secret mail_invite cashback must_approve_organization lbc_giftcard
      cashback_transfer_receiver cashback_transfer_frequency contact_email
      integration_type allow_registration cashback_requestable_amount giftcard prize_draw telemedicine
      biometry organization_manage cashback_manage cashback_wallet_destination user_request_withdrawal
      azul_withdrawal hubspot_company_id status banner telemedicine_config sync_user_updates]
  end
  let(:telemedicine_config_keys) do
    %w[contracted_beneficiaries plan url]
  end
  let(:response_hash) { JSON.parse(response.body) }
  let(:path) { "/client/v2/businesses" }

  describe "#index" do
    let(:headers) { {"X-ClientEmployee-Email": client_employee.email, "X-ClientEmployee-Token": client_employee.authentication_token} }
    let!(:business) { create(:business, user_count: 1, authorized_user_count: 3) }
    let(:main_business_response_hash) { response_hash.find { |b| b["id"] == business.id } }

    context "when client employee is admin lecupon" do
      let!(:client_employee) { create(:client_employee, :admin_lecupon) }

      before do
        create_list(:business, 2)
      end

      it "must return all businesses" do
        get(path, headers:)

        expect(response).to be_successful
        expect(response_hash.count).to eq(Business.count)
        expect(response_hash.map(&:keys)).to all(match_array(serialized_keys))
        expect(main_business_response_hash["user_count"]).to eq(1)
        expect(main_business_response_hash["authorized_user_count"]).to eq(3)
      end
    end

    context "when client employee is admin client" do
      let!(:client_employee) { create(:client_employee, :admin_client, businesses: [business]) }

      before do
        create_list(:business, 2, user_count: 1, authorized_user_count: 3)
      end

      it "must return only client employee businesses" do
        get(path, headers:)

        expect(response).to be_successful
        expect(response_hash.count).to eq(1)
        expect(response_hash.map(&:keys)).to all(match_array(serialized_keys))
        expect(response_hash.first["telemedicine_config"].keys).to match_array(telemedicine_config_keys)
        expect(main_business_response_hash["user_count"]).to eq(1)
        expect(main_business_response_hash["authorized_user_count"]).to eq(3)
      end
    end

    context "when filtering by cnpj" do
      let!(:client_employee) { create(:client_employee, :admin_lecupon) }
      let(:params) { {term: business.cnpj} }

      it "must return filtered business" do
        get(path, headers:, params:)

        expect(response).to be_successful
        expect(response_hash.count).to eq(1)
        expect(main_business_response_hash["id"]).to eq(business.id)
        expect(response_hash.map(&:keys)).to all(match_array(serialized_keys))
      end
    end

    context "when filtering by name" do
      let!(:client_employee) { create(:client_employee, :admin_lecupon) }
      let(:params) { {term: business.name} }

      it "must return filtered business" do
        get(path, headers:, params:)

        expect(response).to be_successful
        expect(response_hash.count).to eq(1)
        expect(response_hash.map(&:keys)).to all(match_array(serialized_keys))
        expect(main_business_response_hash["id"]).to eq(business.id)
      end
    end

    context "when filtering by main businesses" do
      let!(:client_employee) { create(:client_employee, :admin_lecupon) }
      let!(:child_business) { create(:business, main_business: business) }
      let(:params) { {main: true} }

      it "returns only main businesses" do
        get(path, headers:, params:)

        expect(response).to have_http_status(:ok)
        expect(response_hash.map(&:keys)).to all(match_array(serialized_keys))
        expect(response_hash.pluck("id")).to eq([business.id])
      end
    end
  end

  describe "#show" do
    shared_examples "authorized to see business" do
      it "must return business" do
        get("#{path}/#{business.id}", headers:)

        expect(response).to be_successful
        expect(response_hash["id"]).to eq(business.id)
        expect(response_hash.keys).to match_array(serialized_keys)
        expect(response_hash["telemedicine_config"].keys).to match_array(telemedicine_config_keys)
      end
    end

    context "when client employee is admin lecupon" do
      let!(:client_employee) { create(:client_employee, :admin_lecupon) }
      let(:headers) { {"X-ClientEmployee-Email": client_employee.email, "X-ClientEmployee-Token": client_employee.authentication_token} }
      let(:business) { create(:business) }

      it_behaves_like "authorized to see business"
    end

    context "when client employee is admin client" do
      let!(:client_employee) { create(:client_employee, :admin_client, businesses: [business]) }
      let(:headers) { {"X-ClientEmployee-Email": client_employee.email, "X-ClientEmployee-Token": client_employee.authentication_token} }
      let!(:business) { create(:business) }

      context "when its associated to business" do
        it_behaves_like "authorized to see business"
      end

      context "when its not associated to business" do
        before do
          get "#{path}/#{business.id}", headers:
        end

        it_behaves_like "forbidden client_employee"
      end
    end
  end

  describe "#create" do
    let!(:main_business) { create(:business) }

    shared_examples "authorized to create business" do
      let(:valid_params) do
        {
          cnpj: FFaker::IdentificationBR.cnpj,
          name: FFaker::Name.name,
          cashback: false,
          giftcard: false,
          prize_draw: false,
          description: FFaker::Lorem.sentence,
          must_approve_organization: false,
          lbc_giftcard: false,
          spread_percent: 1,
          cashback_requestable_amount: 20,
          main_business_id: main_business.id,
          integration_type: Enums::Business::IntegrationType::API,
          cashback_wallet_destination: Wallet::Kind::CASHBACK,
          user_request_withdrawal: false,
          telemedicine_config: {
            plan: 11001
          }
        }
      end

      it "must create business" do
        organization = create(:organization)
        expect do
          expect(Business::CreateService).to receive(:call).and_call_original
          post "#{path}/", headers:, params: valid_params
          expect(response).to be_created
        end.to change(Business, :count).by(1)
        business = Business.last
        expect(OrganizationBlocklist.where(business: business, organization:)).to be_empty
        expect(business.reload.telemedicine_config.plan).to eq(Telemedicine::Plans::COPART)
        expect(business.reload.telemedicine_config.contracted_beneficiaries).to eq(500)
      end

      it "must create business with telemedicine config" do
        post "#{path}/", headers:, params: valid_params.merge({telemedicine_config: {plan: Telemedicine::Plans::INTEGRAL_PLUS, contracted_beneficiaries: 5000}})

        expect(response).to be_created
        business = Business.last
        expect(business.reload.telemedicine_config.plan).to eq(Telemedicine::Plans::INTEGRAL_PLUS)
        expect(business.reload.telemedicine_config.contracted_beneficiaries).to eq(5000)
      end

      it "must create business and creates blocklist" do
        organization = create(:organization)
        valid_params[:must_approve_organization] = true

        Sidekiq::Testing.inline! do
          post "#{path}/", headers:, params: valid_params

          expect(response).to be_created
          expect(OrganizationBlocklist.where(business: response_hash["id"], organization:)).to exist
        end
      end
    end

    context "when client employee is admin lecupon" do
      let!(:client_employee) { create(:client_employee, :admin_lecupon) }
      let(:headers) { {"X-ClientEmployee-Email": client_employee.email, "X-ClientEmployee-Token": client_employee.authentication_token} }
      let(:business) { create(:business) }

      it_behaves_like "authorized to create business"
    end

    context "when client employee is admin client" do
      let!(:client_employee) { create(:client_employee, :admin_client) }
      let(:headers) { {"X-ClientEmployee-Email": client_employee.email, "X-ClientEmployee-Token": client_employee.authentication_token} }

      before do
        post "#{path}/", headers:
      end

      it_behaves_like "forbidden client_employee"
    end
  end

  describe "#update" do
    let(:business) do
      create(:business, :with_cashback, cashback_wallet_destination: "cashback", user_request_withdrawal: true)
    end

    shared_examples "authorized to update business" do
      let(:valid_params) do
        {
          giftcard: true,
          cashback: false,
          description: FFaker::Lorem.sentence,
          integration_type: Enums::Business::IntegrationType::API,
          cashback_wallet_destination: Wallet::Kind::MEMBERSHIP,
          user_request_withdrawal: true,
          telemedicine_config: {
            plan: Telemedicine::Plans::INTEGRAL_PLUS,
            contracted_beneficiaries: 2000
          }
        }
      end
      let!(:telemedicine_config) { business.telemedicine_config }

      it "must update business" do
        expect do
          expect(Business::UpdateService).to receive(:call).and_call_original
          patch "#{path}/#{business.id}", headers:, params: valid_params

          expect(response).to be_successful
          business.reload
          telemedicine_config.reload
        end.to change(business, :cashback).to(false)
          .and change(business, :description).to(valid_params.dig(:description))
          .and change(business, :giftcard).to(true)
          .and change(business, :user_request_withdrawal).to(false)
          .and change(telemedicine_config, :plan).to(Telemedicine::Plans::INTEGRAL_PLUS)
          .and change(telemedicine_config, :contracted_beneficiaries).to(2000)
      end
    end

    context "when client employee is admin lecupon" do
      let!(:client_employee) { create(:client_employee, :admin_lecupon) }
      let(:headers) { {"X-ClientEmployee-Email": client_employee.email, "X-ClientEmployee-Token": client_employee.authentication_token} }

      it_behaves_like "authorized to update business"
    end

    context "when client employee is admin client" do
      let(:headers) { {"X-ClientEmployee-Email": client_employee.email, "X-ClientEmployee-Token": client_employee.authentication_token} }

      context "when its associated to business" do
        let!(:client_employee) { create(:client_employee, :admin_client, businesses: [business]) }

        it_behaves_like "authorized to update business"
      end

      context "when its not associated to business" do
        let!(:client_employee) { create(:client_employee, :admin_client) }

        before do
          patch "#{path}/#{business.id}", headers:
        end

        it_behaves_like "forbidden client_employee"
      end
    end
  end
end
