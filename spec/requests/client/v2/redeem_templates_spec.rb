# frozen_string_literal: true

require "rails_helper"

RSpec.describe Client::V2::RedeemTemplatesController, type: :request do
  describe "GET /client/v2/businesses/:identifier/redeem_templates" do
    let!(:parent_business) { create(:business, name: "Any parent name") }
    let!(:business) { create(:business, name: "Any name", main_business: parent_business) }

    let(:client_employee) { create(:client_employee, :admin_lecupon) }
    let(:headers) do
      {
        "X-ClientEmployee-Email": client_employee.email,
        "X-ClientEmployee-Token": client_employee.authentication_token,
        "Tenant-id": business.cnpj
      }
    end

    it "must return all redeem templates" do
      all_templates = Enums::CuponSerializerTemplate::TEMPLATE.values.map { |t| t.first.to_s }.uniq

      path = [
        "/client/v2/businesses/#{business.cnpj}/redeem_templates",
        "/client/v2/redeem_templates"
      ].sample
      get(path, headers:)

      expect(JSON.parse(response.body)).to match_array(all_templates)
    end
  end
end
