require "rails_helper"

RSpec.describe Client::V2::SignatureSecretsController, type: :request do
  let(:business) { create(:business) }
  let!(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  let(:response_hash) { JSON.parse(response.body) }

  describe "#update" do
    it "must refresh business signature secret" do
      path = [
        "/client/v2/businesses/#{business.id}/signature_secret",
        "/client/v2/signature_secret"
      ].sample
      patch(path, headers:)
      expect(response).to be_successful

      expect(business.reload.signature_secret).to eq(response_hash["signature_secret"])
    end

    context "when business unrelated to the current user" do
      let(:client_employee) { create(:client_employee, :admin_client) }

      it "returns not found" do
        path = [
          "/client/v2/businesses/#{business.id}/signature_secret",
          "/client/v2/signature_secret"
        ].sample
        patch(path, headers:)

        expect(response).to have_http_status(:not_found)
        expect(response_hash["error"]).to eq("Business não encontrado")
      end
    end

    context "without client_employee authentication" do
      before do
        path = [
          "/client/v2/businesses/#{business.id}/signature_secret",
          "/client/v2/signature_secret"
        ].sample
        patch path
      end

      it_behaves_like "unauthorized client_employee"
    end
  end
end
