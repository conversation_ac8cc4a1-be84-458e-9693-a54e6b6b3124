# frozen_string_literal: true

require "rails_helper"

RSpec.describe Client::V2::CashbacksController, type: :request do
  let(:response_body) { JSON.parse(response.body) }
  let(:user) { create(:user, business:) }
  let(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  describe "#update" do
    let!(:business) { create(:business, cashback_wallet_destination: :cashback, cashback: true, cashback_manage: true) }
    let(:organization) { create(:organization) }
    let(:branch) { create(:branch, organization:) }
    let(:promotion) { create(:promotion, :qrcode, organization:, cashback_type: :percent, cashback_value: 10) }
    let!(:coupon) { create(:cupon, promotion:, branch:, organization:) }
    let!(:cashback) { create(:cashback_record, :pending, user:, order:, order_amount: 103.48) }
    let(:params) { {status: "approved"} }

    context "with cashback created by business" do
      let(:order) { create(:order, user:, cupon: coupon, external_id: SecureRandom.uuid) }

      it "renders created" do
        path = [
          "/client/v2/businesses/#{business.id}/cashbacks/#{cashback.id}",
          "/client/v2/cashbacks/#{cashback.id}"
        ].sample
        expect { patch(path, headers:, params:) }
          .to change { cashback.reload.transaction_status }.to(params[:status])

        expect(response).to be_ok
        expect(response_body.keys).to match_array(%w[id order_id order_amount cashback_amount status])
      end

      context "with wrong params" do
        let(:params) { {status: "available"} }

        it "renders error" do
          path = [
            "/client/v2/businesses/#{business.id}/cashbacks/#{cashback.id}",
            "/client/v2/cashbacks/#{cashback.id}"
          ].sample
          expect { patch(path, headers:, params:) }
            .to not_change { cashback.reload.transaction_status }

          expect(response).to be_unprocessable
          expect(response_body).to eq("error" => "Status do cashback Parâmetro inválido. Os valores aceitos são: 'pending, approved'.")
        end
      end
    end

    context "with cashback not created by business" do
      let(:order) { create(:order, user:, cupon: coupon) }

      it "renders error" do
        path = [
          "/client/v2/businesses/#{business.id}/cashbacks/#{cashback.id}",
          "/client/v2/cashbacks/#{cashback.id}"
        ].sample
        expect { patch(path, headers:, params:) }
          .to not_change { cashback.reload.transaction_status }

        expect(response).to be_not_found
        expect(response_body).to eq("error" => "Registro(s) não encontrado(s).", "status" => 404)
      end
    end
  end
end
