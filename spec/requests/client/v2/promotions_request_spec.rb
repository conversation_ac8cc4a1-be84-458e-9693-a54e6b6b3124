# frozen_string_literal: true

require "rails_helper"

RSpec.describe Client::V2::PromotionsController, type: :request do
  describe "#index" do
    let!(:business) { create(:business, :oab, cnpj: "51559120000164") }
    let!(:client_employee) { create(:client_employee, businesses: [business]) }
    let(:headers) do
      {
        "X-ClientEmployee-Email": client_employee.email,
        "X-ClientEmployee-Token": client_employee.authentication_token,
        "Tenant-id": business.cnpj
      }
    end
    let(:response_hash) { JSON.parse(response.body) }
    let(:promotion_serializer_keys) do
      %w[id redeem_template title rules description discount
        start_date end_date working_days branches organizations coupons status]
    end
    let(:branch_serializer_keys) do
      %w[id name taxpayer_number telephone full_address zipcode
        lat lng opening_time closing_time distance_km organization]
    end
    let(:organization_serializer_keys) do
      %w[id name cover_picture background_picture best_discount_percent category_name
        instagram_url facebook_url twitter_url cashback_percent description highest_discount]
    end
    let(:coupon_serializer_keys) do
      %w[id branch_id organization_id]
    end

    context "when redeem_type is physical" do
      let!(:organization) { create(:organization, :active_with_coupon) }
      let!(:online_promotion) { create(:promotion, :online, :available, organization:) }
      let!(:online_branch) { create(:branch, :online, organization:) }
      let!(:online_coupon) { create(:cupon, :online, promotion: online_promotion, branch: online_branch) }

      let(:near_bucket) { create(:voucher_bucket) }
      let!(:near_physical_promotion) { create(:promotion, :coupon_code, :available, :percent_discount, code: nil, redeems_per_cpf: 999, voucher_bucket: near_bucket) }
      let!(:near_organization) { create(:organization, :active_with_coupon, name: "Mercado") }
      let!(:near_branch) { create(:branch, lat: 1.0, lng: 1.0, organization: near_organization) }
      let!(:near_physical_coupon) do
        create(:cupon, :coupon_code, promotion: near_physical_promotion, branch: near_branch)
      end
      let!(:near_organization_two) { create(:organization, :active_with_coupon, name: "Armazém") }
      let!(:near_branch_two) { create(:branch, lat: 1.0, lng: 1.0, organization: near_organization_two) }
      let!(:near_physical_coupon_two) do
        create(:cupon, :coupon_code, promotion: near_physical_promotion, branch: near_branch_two)
      end
      let!(:near_branch_three) { create(:branch, lat: 10.0, lng: 10.0, organization: near_organization_two) }
      let!(:near_physical_coupon_three) do
        create(:cupon, :coupon_code, promotion: near_physical_promotion, branch: near_branch_three)
      end
      let!(:near_voucher) { create(:voucher, bucket: near_bucket) }

      let(:far_bucket) { create(:voucher_bucket) }
      let!(:far_organization) { create(:organization, :active_with_coupon) }
      let!(:far_physical_promotion) { create(:promotion, :coupon_code, :available, organization: far_organization, voucher_bucket: far_bucket) }
      let!(:far_branch) { create(:branch, lat: 10.0, lng: 10.0, organization: far_organization) }
      let!(:far_physical_coupon) { create(:cupon, :coupon_code, promotion: far_physical_promotion, branch: far_branch) }
      let!(:far_voucher) { create(:voucher, bucket: far_bucket) }

      let(:redeem_type) { "physical" }

      context "with lat/lng" do
        let(:params) { {redeem_type:, lat: 1.0, lng: 1.0, page: 1} }

        it "returns the promotions near lat/lng" do
          Promotion::PropagateChanges.call

          path = [
            "/client/v2/businesses/51559120000164/campaigns",
            "/client/v2/campaigns"
          ].sample
          get(path, params:, headers:)

          expect(response).to have_http_status(:ok)
          expect(response_hash.pluck("id")).to eq([near_physical_promotion.id])
          expect(response_hash.map(&:keys)).to all(match_array(promotion_serializer_keys))
          expect(response_hash.pluck("discount")).to match_array([30])
          first_promotion = response_hash.first
          expect(first_promotion["branches"].pluck("id")).to match_array([near_branch, near_branch_two].map(&:id))
          expect(first_promotion["branches"].map(&:keys)).to all(match_array(branch_serializer_keys))
          expect(first_promotion["branches"].first["organization"].keys).to match_array(organization_serializer_keys)
          expect(first_promotion["organizations"].pluck("id")).to eq([near_organization_two, near_organization].map(&:id))
          expect(first_promotion["organizations"].map(&:keys)).to all(match_array(organization_serializer_keys))
          expect(first_promotion["coupons"].map(&:keys)).to all(match_array(coupon_serializer_keys))
          expect(first_promotion["coupons"].pluck("id")).to match_array([near_physical_coupon_two, near_physical_coupon].map(&:id))
        end
      end

      context "without lat/lng" do
        let(:params) { {redeem_type:, page: 1} }

        it "returns error" do
          path = [
            "/client/v2/businesses/51559120000164/campaigns",
            "/client/v2/campaigns"
          ].sample
          get(path, params:, headers:)

          expect(response).to have_http_status(:unprocessable_entity)
        end
      end

      context "without voucher" do
        let(:params) { {redeem_type:, lat: 1.0, lng: 1.0, page: 1} }

        before do
          Voucher.delete_all
          Promotion::PropagateChanges.call
        end

        it "returns empty" do
          path = [
            "/client/v2/businesses/51559120000164/campaigns",
            "/client/v2/campaigns"
          ].sample
          get(path, params:, headers:)

          expect(response).to have_http_status(:ok)
          expect(response_hash.pluck("id")).to be_empty
        end
      end
    end

    context "when redeem_type is online" do
      let!(:organization) { create(:organization, :active_with_coupon) }
      let!(:branch) { create(:branch, :online, organization:) }
      let!(:online_promotion) { create(:promotion, :online, :available, :percent_discount, organization:, redeems_per_cpf: 999) }
      let!(:online_coupon) { create(:cupon, :online, :percent_discount, promotion: online_promotion, branch:) }

      let!(:near_physical_promotion) { create(:promotion, :cpf, :available) }
      let!(:near_organization) { create(:organization, :active_with_coupon) }
      let!(:near_branch) { create(:branch, lat: 1.0, lng: 1.0, organization: near_organization) }
      let!(:near_physical_coupon) do
        create(:cupon, :cpf, promotion: near_physical_promotion, branch: near_branch)
      end

      let(:params) { {redeem_type: "online", page: 1} }

      it "returns the online promotions" do
        path = [
          "/client/v2/businesses/51559120000164/campaigns",
          "/client/v2/campaigns"
        ].sample
        get(path, params:, headers:)

        expect(response).to have_http_status(:ok)
        expect(response_hash.pluck("id")).to eq([online_promotion.id])
        expect(response_hash.pluck("discount")).to match_array([30])
        expect(response_hash.first.keys).to match_array(promotion_serializer_keys)
        expect(response_hash.first["organizations"].pluck("id")).to eq([organization.id])
        expect(response_hash.first["organizations"].map(&:keys)).to all(match_array(organization_serializer_keys))
      end

      context "when passing near lat/lng" do
        let(:params) { {redeem_type: "online", lat: 1.0, lng: 1.0, page: 1} }

        it "returns the online promotions" do
          path = [
            "/client/v2/businesses/51559120000164/campaigns",
            "/client/v2/campaigns"
          ].sample
          get(path, params:, headers:)

          expect(response).to have_http_status(:ok)
          expect(response_hash.pluck("id")).to eq([online_promotion.id])
          expect(response_hash.first.keys).to match_array(promotion_serializer_keys)
        end
      end

      context "when passing far lat/lng" do
        let(:params) { {redeem_type: "online", lat: 10.0, lng: 10.0, page: 1} }

        it "returns only the online promotions" do
          path = [
            "/client/v2/businesses/51559120000164/campaigns",
            "/client/v2/campaigns"
          ].sample
          get(path, params:, headers:)

          expect(response).to have_http_status(:ok)
          expect(response_hash.pluck("id")).to match_array([online_promotion.id])
          expect(response_hash.first.keys).to match_array(promotion_serializer_keys)
        end
      end
    end

    context "when redeem_type is not informed" do
      let(:params) { {page: 1} }

      it "returns error" do
        path = [
          "/client/v2/businesses/51559120000164/campaigns",
          "/client/v2/campaigns"
        ].sample
        get(path, params:, headers:)

        expect(response).to have_http_status(:unprocessable_entity)
      end
    end

    context "when filtering by category_ids" do
      let!(:online_category) { create(:category) }

      let!(:organization) { create(:organization, :active_with_coupon, categories: [online_category]) }
      let!(:online_branch) { create(:branch, :online, organization:) }
      let!(:online_promotion) { create(:promotion, :online, :available, organization:, redeems_per_cpf: 999) }
      let!(:online_coupon) { create(:cupon, :online, promotion: online_promotion, branch: online_branch) }

      let!(:online_sub_category) { create(:category, main_category: online_category) }

      let!(:sub_category_organization) { create(:organization, :active_with_coupon, categories: [online_sub_category]) }
      let!(:sub_category_branch) { create(:branch, :online, organization: sub_category_organization) }
      let!(:sub_category_promotion) { create(:promotion, :online, :available, organization: sub_category_organization, redeems_per_cpf: 999) }
      let!(:sub_category_coupon) { create(:cupon, :online, promotion: sub_category_promotion, branch: sub_category_branch) }

      let!(:physical_category) { create(:category) }

      let!(:physical_organization) { create(:organization, :active_with_coupon, categories: [physical_category]) }
      let!(:physical_promotion) { create(:promotion, :cpf, :available, redeems_per_cpf: 999) }
      let!(:branch) { create(:branch, lat: 1.0, lng: 1.0, organization: physical_organization) }
      let!(:physical_coupon) do
        create(:cupon, :cpf, promotion: physical_promotion, branch:)
      end

      context "with online redeem_type" do
        context "with online category" do
          let(:params) { {redeem_type: "online", category_ids: [online_category.id], page: 1} }

          it "returns the online promotions within the category" do
            path = [
              "/client/v2/businesses/51559120000164/campaigns",
              "/client/v2/campaigns"
            ].sample
            get(path, params:, headers:)

            expect(response).to have_http_status(:ok)
            expect(response_hash.pluck("id")).to match_array([online_promotion.id])
            expect(response_hash.first.keys).to match_array(promotion_serializer_keys)
          end
        end

        context "with physical category" do
          let(:params) { {redeem_type: "online", category_ids: [physical_category.id], page: 1} }

          it "returns empty" do
            path = [
              "/client/v2/businesses/51559120000164/campaigns",
              "/client/v2/campaigns"
            ].sample
            get(path, params:, headers:)

            expect(response).to have_http_status(:ok)
            expect(response_hash.pluck("id")).to be_empty
          end
        end
      end

      context "with physical redeem_type" do
        context "with physical category" do
          let(:params) { {redeem_type: "physical", lat: 1.0, lng: 1.0, category_ids: [physical_category.id], page: 1} }

          it "returns the physical promotions with the category" do
            path = [
              "/client/v2/businesses/51559120000164/campaigns",
              "/client/v2/campaigns"
            ].sample
            get(path, params:, headers:)

            expect(response).to have_http_status(:ok)
            expect(response_hash.pluck("id")).to eq([physical_promotion.id])
            expect(response_hash.first.keys).to match_array(promotion_serializer_keys)
          end
        end

        context "with online category" do
          let(:params) { {redeem_type: "physical", lat: 1.0, lng: 1.0, category_ids: [online_category.id], page: 1} }

          it "returns empty" do
            path = [
              "/client/v2/businesses/51559120000164/campaigns",
              "/client/v2/campaigns"
            ].sample
            get(path, params:, headers:)

            expect(response).to have_http_status(:ok)
            expect(response_hash.pluck("id")).to be_empty
          end
        end
      end

      context "when category is blocklisted" do
        let!(:blocklist) { create(:category_blocklist, category: online_category, business:) }
        let(:params) { {redeem_type: "online", category_ids: [online_category.id], page: 1} }

        it "returns empty" do
          path = [
            "/client/v2/businesses/51559120000164/campaigns",
            "/client/v2/campaigns"
          ].sample
          get(path, params:, headers:)

          expect(response).to have_http_status(:ok)
          expect(response_hash.pluck("id")).to be_empty
        end
      end
    end

    context "when filtering by organization_id" do
      let!(:organization) { create(:organization, :active_with_coupon) }
      let!(:online_promotion) { create(:promotion, :online, :available, organization:, redeems_per_cpf: 999) }
      let!(:online_branch) { create(:branch, :online, organization:) }
      let!(:online_coupon) { create(:cupon, :online, promotion: online_promotion, branch: online_branch) }

      let!(:physical_organization) { create(:organization, :active_with_coupon) }
      let!(:physical_promotion) { create(:promotion, :cpf, :available, redeems_per_cpf: 999) }
      let!(:branch) { create(:branch, lat: 1.0, lng: 1.0, organization: physical_organization) }
      let!(:physical_coupon) do
        create(:cupon, :cpf, promotion: physical_promotion, branch:)
      end

      context "when redeem_type is online" do
        context "with online organization" do
          let(:params) { {redeem_type: "online", organization_id: organization.id, page: 1} }

          it "returns the promotions filtered" do
            path = [
              "/client/v2/businesses/51559120000164/campaigns",
              "/client/v2/campaigns"
            ].sample
            get(path, params:, headers:)

            expect(response).to have_http_status(:ok)
            expect(response_hash.pluck("id")).to eq([online_promotion.id])
            expect(response_hash.first.keys).to match_array(promotion_serializer_keys)
          end
        end

        context "with physical organization" do
          let(:params) { {redeem_type: "online", organization_id: branch.organization_id, page: 1} }

          it "returns empty" do
            path = [
              "/client/v2/businesses/51559120000164/campaigns",
              "/client/v2/campaigns"
            ].sample
            get(path, params:, headers:)

            expect(response).to have_http_status(:ok)
            expect(response_hash.pluck("id")).to be_empty
          end
        end
      end

      context "when redeem_type is physical" do
        context "with lat/lng and physical organization" do
          let(:params) { {redeem_type: "physical", lat: 1.0, lng: 1.0, organization_id: physical_organization.id, page: 1} }

          it "returns the promotion filtered" do
            path = [
              "/client/v2/businesses/51559120000164/campaigns",
              "/client/v2/campaigns"
            ].sample
            get(path, params:, headers:)

            expect(response).to have_http_status(:ok)
            expect(response_hash.pluck("id")).to eq([physical_promotion.id])
            expect(response_hash.first.keys).to match_array(promotion_serializer_keys)
          end
        end

        context "with lat/lng and online organization" do
          let(:params) { {redeem_type: "physical", lat: 1.0, lng: 1.0, organization_id: organization.id, page: 1} }

          it "returns empty" do
            path = [
              "/client/v2/businesses/51559120000164/campaigns",
              "/client/v2/campaigns"
            ].sample
            get(path, params:, headers:)

            expect(response).to have_http_status(:ok)
            expect(response_hash.pluck("id")).to be_empty
          end
        end
      end
    end

    context "when filtering by distance_km" do
      let!(:organization) { create(:organization, :active_with_coupon) }
      let!(:online_promotion) { create(:promotion, :online, :available, organization:) }
      let!(:online_branch) { create(:branch, :online, organization:) }
      let!(:online_coupon) { create(:cupon, :online, promotion: online_promotion, branch: online_branch) }

      let!(:near_organization) { create(:organization, :active_with_coupon) }
      let!(:near_physical_promotion) { create(:promotion, :available, :cpf, redeems_per_cpf: 999) }
      let!(:near_branch) { create(:branch, lat: 1.0, lng: 1.0, organization: near_organization) }
      let!(:near_physical_coupon) do
        create(:cupon, :cpf, promotion: near_physical_promotion, branch: near_branch)
      end

      let!(:far_organization) { create(:organization, :active_with_coupon) }
      let!(:far_physical_promotion) { create(:promotion, :cpf, :available, redeems_per_cpf: 999) }
      let!(:far_branch) { create(:branch, lat: 10.0, lng: 10.0, organization: far_organization) }
      let!(:far_physical_coupon) { create(:cupon, :cpf, promotion: far_physical_promotion, branch: far_branch) }

      let!(:regionalized_organization) { create(:organization, :active_with_coupon) }
      let!(:regionalized_promotion) { create(:promotion, :regionalized, :available, organization: regionalized_organization, redeems_per_cpf: 999) }
      let!(:regionalized_branch) { create(:branch, lat: 1.0, lng: 1.0, organization: regionalized_organization) }
      let!(:regionalized_coupon) do
        create(:cupon, :online, promotion: regionalized_promotion, branch: regionalized_branch)
      end

      context "when less than or equal to 30 km" do
        let(:params) { {redeem_type: "physical", lat: 1.0, lng: 1.0, page: 1, distance_km: 29} }

        it "returns the physical promotions less or equal to 30 km" do
          path = [
            "/client/v2/businesses/51559120000164/campaigns",
            "/client/v2/campaigns"
          ].sample
          get(path, params:, headers:)

          expect(response).to have_http_status(:ok)
          expect(response_hash.pluck("id")).to match_array([near_physical_promotion.id, regionalized_promotion.id])
          expect(response_hash.first.keys).to match_array(promotion_serializer_keys)
        end
      end

      context "when greater than 30 km" do
        let(:params) { {redeem_type: "physical", lat: 1.0, lng: 1.0, page: 1, distance_km: 31} }

        it "returns error" do
          path = [
            "/client/v2/businesses/51559120000164/campaigns",
            "/client/v2/campaigns"
          ].sample
          get(path, params:, headers:)

          expect(response).to have_http_status(:unprocessable_entity)
        end
      end
    end

    context "when filtering by redeem_template" do
      let!(:organization) { create(:organization, :active_with_coupon) }
      let!(:online_promotion) { create(:promotion, :online, :available, organization:) }
      let!(:online_branch) { create(:branch, :online, organization:) }
      let!(:online_coupon) { create(:cupon, :online, promotion: online_promotion, branch: online_branch) }

      let!(:near_organization_cpf) { create(:organization, :active_with_coupon) }
      let!(:near_promotion_cpf) { create(:promotion, :cpf, :available, redeems_per_cpf: 999) }
      let!(:near_branch_cpf) { create(:branch, lat: 1.0, lng: 1.0, organization: near_organization_cpf) }
      let!(:near_coupon_cpf) do
        create(:cupon, :cpf, promotion: near_promotion_cpf, branch: near_branch_cpf)
      end

      let!(:near_organization_qrcode) { create(:organization, :active_with_coupon) }
      let!(:near_promotion_qrcode) { create(:promotion, :qrcode, :available, redeems_per_cpf: 999) }
      let!(:near_branch_qrcode) { create(:branch, lat: 1.0, lng: 1.0, organization: near_organization_qrcode) }
      let!(:near_coupon_qrcode) do
        create(:cupon, :classic, promotion: near_promotion_qrcode, branch: near_branch_qrcode)
      end

      context "with valid redeem_template" do
        let(:params) { {redeem_type: "physical", lat: 1.0, lng: 1.0, page: 1, redeem_template: "default"} }

        it "returns the promotions with the redeem template" do
          path = [
            "/client/v2/businesses/51559120000164/campaigns",
            "/client/v2/campaigns"
          ].sample
          get(path, params:, headers:)

          expect(response).to have_http_status(:ok)
          expect(response_hash.pluck("id")).to eq([near_promotion_cpf.id])
          expect(response_hash.first.keys).to match_array(promotion_serializer_keys)
        end
      end

      context "with invalid redeem_template" do
        let(:params) { {redeem_type: "physical", lat: 1.0, lng: 1.0, page: 1, redeem_template: "none"} }

        it "returns error" do
          path = [
            "/client/v2/businesses/51559120000164/campaigns",
            "/client/v2/campaigns"
          ].sample
          get(path, params:, headers:)

          expect(response).to have_http_status(:unprocessable_entity)
        end
      end

      context "with valid redeem_template list" do
        let(:params) do
          {redeem_type: "physical", lat: 1.0, lng: 1.0, page: 1, redeem_template: ["default", "qrcode"]}
        end

        it "returns the promotions with the redeem templates" do
          path = [
            "/client/v2/businesses/51559120000164/campaigns",
            "/client/v2/campaigns"
          ].sample
          get(path, params:, headers:)

          expect(response).to have_http_status(:ok)
          expect(response_hash.pluck("id")).to match_array([near_promotion_cpf.id, near_promotion_qrcode.id])
          expect(response_hash.map(&:keys)).to all(match_array(promotion_serializer_keys))
        end
      end

      context "with any invalid redeem_template in list" do
        let(:params) { {redeem_type: "physical", lat: 1.0, lng: 1.0, page: 1, redeem_template: ["default", "none"]} }

        it "returns error" do
          path = [
            "/client/v2/businesses/51559120000164/campaigns",
            "/client/v2/campaigns"
          ].sample
          get(path, params:, headers:)

          expect(response).to have_http_status(:unprocessable_entity)
        end
      end
    end

    context "when filtering by term" do
      let!(:physical_category) { create(:category, title: "Farmácia") }
      let!(:physical_tag) { create(:tag, name: "remédio") }

      let!(:near_organization_cpf) do
        create(
          :organization,
          :active_with_coupon,
          name: "Drogasil",
          categories: [physical_category]
        )
      end
      let!(:near_promotion_cpf) { create(:promotion, :cpf, :available, title: "foo promotion", redeems_per_cpf: 999) }
      let!(:near_branch_cpf) { create(:branch, lat: 1.0, lng: 1.0, organization: near_organization_cpf) }
      let!(:near_coupon_cpf) do
        create(:cupon, :cpf, promotion: near_promotion_cpf, branch: near_branch_cpf)
      end

      let!(:near_organization_qrcode) do
        create(
          :organization,
          :active_with_coupon,
          name: "Araujo",
          categories: [physical_category],
          tags: [physical_tag]
        )
      end
      let!(:near_promotion_qrcode) { create(:promotion, :qrcode, :available, redeems_per_cpf: 999) }
      let!(:near_branch_qrcode) { create(:branch, lat: 1.0, lng: 1.0, organization: near_organization_qrcode) }
      let!(:near_coupon_qrcode) do
        create(:cupon, :classic, promotion: near_promotion_qrcode, branch: near_branch_qrcode)
      end

      let(:default_params) { {lat: 1.0, lng: 1.0, page: 1, redeem_type: "physical"} }

      context "with organization name" do
        let(:params) { default_params.merge(term: "droga") }

        it "returns the promotions filtered by name" do
          path = [
            "/client/v2/businesses/51559120000164/campaigns",
            "/client/v2/campaigns"
          ].sample
          get(path, params:, headers:)

          expect(response).to have_http_status(:ok)
          expect(response_hash.pluck("id")).to eq([near_promotion_cpf.id])
          expect(response_hash.map(&:keys)).to all(match_array(promotion_serializer_keys))
        end
      end

      context "with category title" do
        let(:params) { default_params.merge(term: "armacia") }

        it "returns the promotions filtered by name" do
          path = [
            "/client/v2/businesses/51559120000164/campaigns",
            "/client/v2/campaigns"
          ].sample
          get(path, params:, headers:)

          expect(response).to have_http_status(:ok)
          expect(response_hash.pluck("id")).to eq([near_promotion_cpf.id, near_promotion_qrcode.id])
          expect(response_hash.map(&:keys)).to all(match_array(promotion_serializer_keys))
        end
      end

      context "with tag title" do
        let(:params) { default_params.merge(term: "remedio") }

        it "returns the promotions filtered by name" do
          path = [
            "/client/v2/businesses/51559120000164/campaigns",
            "/client/v2/campaigns"
          ].sample
          get(path, params:, headers:)

          expect(response).to have_http_status(:ok)
          expect(response_hash.pluck("id")).to eq([near_promotion_qrcode.id])
          expect(response_hash.map(&:keys)).to all(match_array(promotion_serializer_keys))
        end
      end

      context "with promotion term" do
        let(:params) { default_params.merge(term: "foo promotion") }

        it "returns the promotions filtered by name" do
          path = [
            "/client/v2/businesses/51559120000164/campaigns",
            "/client/v2/campaigns"
          ].sample
          get(path, params:, headers:)

          expect(response).to have_http_status(:ok)
          expect(response_hash.pluck("id")).to eq([near_promotion_cpf.id])
          expect(response_hash.map(&:keys)).to all(match_array(promotion_serializer_keys))
        end
      end
    end

    context "when filtering by coupon ids" do
      let!(:near_organization_cpf) { create(:organization, :active_with_coupon) }
      let!(:near_promotion_cpf) { create(:promotion, :cpf, :available, redeems_per_cpf: 999) }
      let!(:near_branch_cpf) { create(:branch, lat: 1.0, lng: 1.0, organization: near_organization_cpf) }
      let!(:near_coupon_cpf) do
        create(:cupon, :cpf, promotion: near_promotion_cpf, branch: near_branch_cpf)
      end

      let!(:near_organization_qrcode) { create(:organization, :active_with_coupon) }
      let!(:near_promotion_qrcode) { create(:promotion, :qrcode, :available) }
      let!(:near_branch_qrcode) { create(:branch, lat: 1.0, lng: 1.0, organization: near_organization_qrcode) }
      let!(:near_coupon_qrcode) do
        create(:cupon, :classic, promotion: near_promotion_qrcode, branch: near_branch_qrcode)
      end

      let(:params) do
        {redeem_type: "physical", lat: 1.0, lng: 1.0, page: 1, coupon_ids: [near_coupon_cpf.id]}
      end

      it "returns the promotions filtered by coupon ids" do
        path = [
          "/client/v2/businesses/51559120000164/campaigns",
          "/client/v2/campaigns"
        ].sample
        get(path, params:, headers:)

        expect(response).to have_http_status(:ok)
        expect(response_hash.pluck("id")).to match_array([near_promotion_cpf.id])
        expect(response_hash.map(&:keys)).to all(match_array(promotion_serializer_keys))
      end
    end

    describe "current_contract_text attribute" do
      let!(:organization) { create(:organization, :active_with_coupon) }
      let!(:online_promotion) { create(:promotion, :online, :available, organization:, redeems_per_cpf: 999) }
      let!(:online_branch) { create(:branch, :online, organization:) }
      let!(:online_coupon) { create(:cupon, :online, promotion: online_promotion, branch: online_branch) }

      let(:params) { {redeem_type: "online", page: 1, business_ids: [business.id]} }

      let(:response_hash) { JSON.parse(response.body) }

      let(:rules_last_line) do
        response_hash.first["rules"].split("\n")[-1]
      end

      before do
        online_promotion.update!(business:)
        online_coupon.update!(business:)
      end

      context "when contract_custom_text is nil" do
        before { online_promotion.update!(contract_custom_text: nil) }

        context "when business have promotion_custom_text message" do
          let!(:custom_text) do
            create(:custom_text, business:, message_type: Enums::CustomTextType::PROMOTION_CUSTOM_TEXT, message: "Mensagem qualquer")
          end

          it "must return business promotion_custom_text" do
            path = [
              "/client/v2/businesses/51559120000164/campaigns",
              "/client/v2/campaigns"
            ].sample
            get(path, params:, headers:)

            expect(rules_last_line).to eq(custom_text.message)
          end

          context "when business promotion_custom_text is defined but empty" do
            before { custom_text.update!(message: "") }

            it "must return empty business promotion_custom_text" do
              path = [
                "/client/v2/businesses/51559120000164/campaigns",
                "/client/v2/campaigns"
              ].sample
              get(path, params:, headers:)

              expect(rules_last_line).to eq(nil)
            end
          end
        end

        context "when business does not have promotion_custom_text message" do
          it "must return default text" do
            path = [
              "/client/v2/businesses/51559120000164/campaigns",
              "/client/v2/campaigns"
            ].sample
            get(path, params:, headers:)

            expect(rules_last_line).to eq("Rede de Parcerias Alloyal")
          end
        end
      end

      context "when contract_custom_text is not nil" do
        before { online_promotion.update(contract_custom_text: "Rede de Parcerias Qualquer") }

        it "must return contract_custom_text" do
          path = [
            "/client/v2/businesses/51559120000164/campaigns",
            "/client/v2/campaigns"
          ].sample
          get(path, params:, headers:)

          expect(rules_last_line).to eq("Rede de Parcerias Qualquer")
        end
      end
    end

    context "without page" do
      let(:params) { {redeem_type: "physical", lat: 1.0, lng: 1.0} }

      it "returns error" do
        path = [
          "/client/v2/businesses/51559120000164/campaigns",
          "/client/v2/campaigns"
        ].sample
        get(path, params:, headers:)

        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end
end
