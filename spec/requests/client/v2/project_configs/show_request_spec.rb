require "rails_helper"

RSpec.describe Client::V2::ProjectConfigsController, type: :request do
  let(:business) { create(:business, create_project_config: false) }

  let!(:client_employee) { create(:client_employee, :admin_lecupon) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  let(:response_hash) { JSON.parse(response.body) }

  let(:serialized_keys) {
    %w[
      name primary_color secondary_color font_color background_color
      clever_tap clever_tap_account_id clever_tap_project_token clever_tap_passcode clever_tap_region
      favicon_url apple_icon_url google_icon_url term_of_use_url web_domain
      google_identifier firebase_dynamic_link_domain apple_identifier apple_key_file_url
      google_key_file_url apple_api_key_id apple_team_id apple_issuer_id
      firebase_google_file_url firebase_apple_file_url api_key api_secret
      email_sender email_logo_url user_manager sign_up_url user_update_url only_exclusive_categories
      firebase_project_id android_version ios_version google_store_url apple_store_url
      google_jks_file_url use_google_jks password_recovery_url open_signup_business_id
      horizontal_logo_url vertical_logo_url horizontal_white_logo_url
      logo_large_url logo_small_url logo_url svg_logo_url updated_at single_sign_on_url single_sign_on_iss
      support support_url help_center_url inngage_token gtm_tag_id show_my_account need_cellphone_on_registration need_email_on_registration
      facebook_sdk facebook_sdk_app_id facebook_sdk_app_name facebook_sdk_client_token
      telemedicine_plan telemedicine_url home_search home_view_mode giftcard_faq_url create_user_on_smart_link
      custom_user_transfer_account_number show_only_organizations_with_coupons shorebird_application_id
      single_sign_on_logout_url freshworks_app_id freshworks_app_key freshworks_domain policy_url send_transactional_emails
    ]
  }

  describe "#show" do
    let!(:project_config) { create(:project_config, business:) }

    it "renders a successful response" do
      path = [
        "/client/v2/businesses/#{business.cnpj}/project_config",
        "/client/v2/project_config"
      ].sample
      get(path, headers:)

      expect(response).to have_http_status(:successful)
      expect(response_hash.keys).to match_array(serialized_keys)
    end

    context "when current_business is subbusiness" do
      let!(:sub_business) { create(:business, create_project_config: false, main_business: business) }
      let!(:client_employee) { create(:client_employee, businesses: [sub_business]) }
      let(:headers) do
        {
          "X-ClientEmployee-Email": client_employee.email,
          "X-ClientEmployee-Token": client_employee.authentication_token,
          "Tenant-id": sub_business.cnpj
        }
      end

      it "must return main business project config" do
        path = [
          "/client/v2/businesses/#{sub_business.cnpj}/project_config",
          "/client/v2/project_config"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:successful)
        expect(response_hash.keys).to match_array(serialized_keys)
        expect(response_hash["apple_identifier"]).to eq(project_config.apple_identifier)
      end
    end

    context "without client employee authorization" do
      it "renders unauthorized" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/project_config",
          "/client/v2/project_config"
        ].sample
        get path

        expect(response).to have_http_status(:unauthorized)
      end
    end

    context "with unrelated client employee authorization" do
      let(:business_two) { create(:business) }
      let!(:client_employee) { create(:client_employee, businesses: [business_two]) }
      let(:headers) do
        {
          "X-ClientEmployee-Email": client_employee.email,
          "X-ClientEmployee-Token": client_employee.authentication_token,
          "Tenant-id": business.cnpj
        }
      end

      it "renders forbidden" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/project_config",
          "/client/v2/project_config"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:not_found)
        expect(response_hash["error"]).to eq("Business não encontrado")
      end
    end
  end
end
