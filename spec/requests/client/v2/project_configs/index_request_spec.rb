require "rails_helper"

RSpec.describe Client::V2::ProjectConfigsController, type: :request do
  let(:serialized_keys) {
    %w[
      id business_taxpayer_id business_name name primary_color secondary_color font_color background_color svg_logo_url
      clever_tap clever_tap_account_id clever_tap_project_token clever_tap_passcode clever_tap_region
      favicon_url web_icon_url apple_icon_url google_icon_url logo_small_url logo_url logo_large_url
      term_of_use_url web_domain google_identifier firebase_dynamic_link_domain
      apple_identifier apple_key_file_url google_key_file_url apple_api_key_id apple_team_id
      apple_issuer_id firebase_google_file_url firebase_apple_file_url api_key api_secret
      email_sender user_manager sign_up_url user_update_url only_exclusive_categories
      firebase_project_id android_version ios_version google_store_url apple_store_url
      password_recovery_url horizontal_logo_url vertical_logo_url horizontal_white_logo_url
      support support_url help_center_url inngage_token gtm_tag_id show_my_account
      facebook_sdk facebook_sdk_app_id facebook_sdk_app_name facebook_sdk_client_token
      telemedicine_plan telemedicine_url home_search home_view_mode giftcard_faq_url shorebird_application_id
      freshworks_app_id freshworks_app_key freshworks_domain
    ]
  }

  describe "#index" do
    context "when user is a business admin" do
      let!(:business) { create(:business, create_project_config: false, integration_type: "app") }
      let!(:project_config) { create(:project_config, business:, name: "App 1") }
      let!(:inactive_business) { create(:business, status: Business::Status::SUSPENDED_BY_OVERDUE, create_project_config: false, integration_type: "api") }
      let!(:inactive_project_config) { create(:project_config, business: inactive_business, name: "App 2") }
      let!(:another_active_business) { create(:business, create_project_config: false, integration_type: "app") }
      let!(:another_active_project_config) { create(:project_config, business: another_active_business, name: "App 3") }
      let(:response_hash) { JSON.parse(response.body) }
      let!(:client_employee) { create(:client_employee, :admin_lecupon) }
      let(:headers) do
        {"X-ClientEmployee-Email": client_employee.email, "X-ClientEmployee-Token": client_employee.authentication_token}
      end

      it "renders a successful response" do
        get "/client/v2/project_configs",
          headers:,
          as: :json

        expect(response).to be_successful
        expect(response_hash.pluck("name")).to match_array([project_config, inactive_project_config, another_active_project_config].map(&:name))
        expect(response_hash.map(&:keys)).to all(match_array(serialized_keys))
      end

      context "when filtering by iss" do
        let!(:project_config) { create(:project_config, business:, name: "App 1", single_sign_on_iss: "business_iss") }

        it "renders a successful response filtering by iss" do
          get "/client/v2/project_configs",
            headers:,
            params: {iss: "business_iss"},
            as: :json

          expect(response).to be_successful
          expect(response_hash.pluck("name")).to match_array([project_config].map(&:name))
          expect(response_hash.first.keys).to match_array(serialized_keys)
        end
      end

      context "when filtering by active" do
        it "renders a successful response filtering by active" do
          get "/client/v2/project_configs",
            headers:,
            params: {active: true},
            as: :json

          expect(response).to be_successful
          expect(response_hash.pluck("name")).to match_array([project_config, another_active_project_config].map(&:name))
          expect(response_hash.first.keys).to match_array(serialized_keys)
        end
      end

      context "when filtering by integration type" do
        it "renders a successful response filtering by integration type" do
          get "/client/v2/project_configs",
            headers:,
            params: {integration_type: "app"},
            as: :json

          expect(response).to be_successful
          expect(response_hash.pluck("name")).to match_array([project_config, another_active_project_config].map(&:name))
          expect(response_hash.first.keys).to match_array(serialized_keys)
        end
      end

      context "without client employee authorization" do
        it "renders unauthorized" do
          get "/client/v2/project_configs",
            as: :json

          expect(response).to be_unauthorized
        end
      end
    end

    context "when user is not a business admin" do
      let(:business_two) { create(:business) }
      let!(:client_employee) { create(:client_employee, businesses: [business_two]) }
      let(:headers) do
        {"X-ClientEmployee-Email": client_employee.email, "X-ClientEmployee-Token": client_employee.authentication_token}
      end

      it "renders forbidden" do
        get "/client/v2/project_configs",
          headers:,
          as: :json

        expect(response).to be_forbidden
      end
    end
  end
end
