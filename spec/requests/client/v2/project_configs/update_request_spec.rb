require "rails_helper"
require "sidekiq/testing"

RSpec.describe Client::V2::ProjectConfigsController, type: :request do
  let(:business) { create(:business, create_project_config: false) }

  let!(:client_employee) { create(:client_employee, :admin_lecupon) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  let(:response_hash) { JSON.parse(response.body) }

  describe "#update" do
    let!(:project_config) { create(:project_config, business:) }
    let(:vertical_logo) { Rack::Test::UploadedFile.new(Rails.root.join("spec/fixtures/samplefile.png")) }
    let(:horizontal_logo) { Rack::Test::UploadedFile.new(Rails.root.join("spec/fixtures/samplefile.svg")) }
    let(:horizontal_white_logo) { Rack::Test::UploadedFile.new(Rails.root.join("spec/fixtures/samplefile.svg")) }
    let(:favicon) { Rack::Test::UploadedFile.new(Rails.root.join("spec/fixtures/favicon.ico")) }

    let(:new_attributes) do
      {
        favicon:,
        vertical_logo:,
        svg_logo: vertical_logo,
        horizontal_logo:,
        horizontal_white_logo:,
        android_version: "2.12.2",
        ios_version: "2.12.2",
        google_store_url: "https://play.google.com/store/apps/details?id=br.com.example",
        apple_store_url: "https://apps.apple.com/br/app/example-app/id0123456789",
        show_my_account: false
      }
    end

    context "with valid parameters" do
      it "updates the requested project_config" do
        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/project_config",
            "/client/v2/project_config"
          ].sample
          patch(path, params: {project_config: new_attributes}, headers:)

          project_config.reload
        end.to change(project_config, :vertical_logo)
          .and change(project_config, :favicon)
          .and change(project_config, :horizontal_logo)
          .and change(project_config, :horizontal_white_logo)
          .and change(project_config, :vertical_logo)
          .and change(project_config, :android_version).from(nil).to("2.12.2")
          .and change(project_config, :ios_version).from(nil).to("2.12.2")
          .and change(project_config, :google_store_url).from(nil).to("https://play.google.com/store/apps/details?id=br.com.example")
          .and change(project_config, :apple_store_url).from(nil).to("https://apps.apple.com/br/app/example-app/id0123456789")
          .and change(project_config, :show_my_account).from(true).to(false)

        expect(response_hash["favicon_url"]).to be_present
        expect(response_hash["vertical_logo_url"]).to be_present
        expect(response_hash["horizontal_logo_url"]).to be_present
        expect(response_hash["horizontal_white_logo_url"]).to be_present
        expect(response_hash["show_my_account"]).to eq(false)
      end

      context "when passing mailer_config params" do
        let(:new_attributes) do
          {
            email_logo: vertical_logo,
            email_sender: "<EMAIL>",
            send_transactional_emails: false
          }
        end
        let(:mailer_config) { business.mailer_config }

        it "must update mailer_config" do
          expect do
            path = [
              "/client/v2/businesses/#{business.cnpj}/project_config",
              "/client/v2/project_config"
            ].sample
            patch(path, params: {project_config: new_attributes}, headers:)

            mailer_config.reload
          end.to change(mailer_config, :logo)
            .and change(mailer_config, :email_sender)
            .and change(mailer_config, :send_transactional_emails)
        end
      end

      context "when clever_tap is true" do
        let(:new_attributes) do
          {
            email_logo: vertical_logo,
            email_sender: "<EMAIL>"
          }
        end
        let(:mailer_config) { business.mailer_config }

        it "must update mailer_config" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/project_config",
            "/client/v2/project_config"
          ].sample
          patch(path, params: {project_config: {clever_tap: true}}, headers:)

          expect(response).to have_http_status(:unprocessable_entity)
          expect(response_hash["error"]).to eq(
            "Identificador do Projeto CleverTap não pode ficar em branco, Senha CleverTap não pode ficar em branco, Token do Projeto CleverTap não pode ficar em branco, Região CleverTap não pode ficar em branco"
          )
        end
      end
    end

    context "when passing telemedicine config attributes" do
      let!(:business) { create(:business, create_telemedicine_config: false, telemedicine: true) }
      let!(:telemedicine_config) { create(:telemedicine_config, plan: Telemedicine::Plans::INTEGRAL, business:, contracted_beneficiaries: 1500) }
      let(:new_attributes) { {telemedicine_plan: Telemedicine::Plans::COPART} }

      it "must update telemedicine config" do
        patch "/client/v2/businesses/#{business.cnpj}/project_config", params: {project_config: new_attributes}, headers: headers
        telemedicine_config.reload
        expect(telemedicine_config.plan).to eq(Telemedicine::Plans::COPART)
      end
    end

    context "with an invalid logo format" do
      let(:horizontal_logo) { Rack::Test::UploadedFile.new(Rails.root.join("spec/fixtures/samplefile.png")) }

      let(:new_attributes) do
        {horizontal_logo:}
      end

      it "must render extension error" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/project_config",
          "/client/v2/project_config"
        ].sample
        patch(path, params: {project_config: new_attributes}, headers:)

        expect(response).to be_unprocessable
        expect(response_hash["error"]).to eq("Horizontal logo não permite arquivos \"png\". Tipos permitidos: svg")
      end
    end

    context "without client employee authorization" do
      it "renders unauthorized" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/project_config",
          "/client/v2/project_config"
        ].sample
        patch(path, params: {project_config: new_attributes})

        expect(response).to be_unauthorized
      end
    end

    context "with business_id unrelated to client employee" do
      context "when is lecupon admin" do
        it "updates the requested project_config" do
          expect do
            path = [
              "/client/v2/businesses/#{business.cnpj}/project_config",
              "/client/v2/project_config"
            ].sample
            patch(path, params: {project_config: new_attributes}, headers:)

            project_config.reload
          end.to change(project_config, :vertical_logo)
          expect(response_hash["vertical_logo_url"]).to be_present
        end
      end

      context "when is not lecupon admin" do
        let(:business_two) { create(:business) }

        let!(:client_employee) { create(:client_employee, businesses: [business_two]) }

        let(:headers) do
          {
            "X-ClientEmployee-Email": client_employee.email,
            "X-ClientEmployee-Token": client_employee.authentication_token,
            "Tenant-id": business.cnpj
          }
        end

        it "renders forbidden" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/project_config",
            "/client/v2/project_config"
          ].sample
          patch(path, params: {project_config: new_attributes}, headers:)

          expect(response).to have_http_status(:not_found)
          expect(response_hash["error"]).to eq("Business não encontrado")
        end
      end
    end

    context "when business is sub business" do
      let(:main_business) { create(:business, create_project_config: false) }
      let(:business) { create(:business, main_business:) }
      let(:new_attributes) { {email_sender: "<EMAIL>"} }
      let!(:project_config) { create(:project_config, business:) }

      it "must render error" do
        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/project_config",
            "/client/v2/project_config"
          ].sample
          patch(path, params: {project_config: new_attributes}, headers:)

          project_config.reload
        end.not_to change(project_config, :email_sender)

        expect(response).to be_unprocessable
        expect(response_hash["error"]).to eq(I18n.t("project_config.errors.child_business_cannot_edit"))
      end
    end

    context "when user_manager is external" do
      context "when has required external fields" do
        let(:new_attributes) do
          {
            vertical_logo:,
            svg_logo: vertical_logo,
            android_version: "2.12.2",
            ios_version: "2.12.2",
            google_store_url: "https://play.google.com/store/apps/details?id=br.com.example",
            apple_store_url: "https://apps.apple.com/br/app/example-app/id0123456789",
            password_recovery_url: "https://foo.com/password-recovery",
            user_update_url: "https://foo.com/user-update",
            sign_up_url: "https://foo.com/sign-up-url"
          }
        end

        it "updates the requested project_config" do
          expect do
            path = [
              "/client/v2/businesses/#{business.cnpj}/project_config",
              "/client/v2/project_config"
            ].sample
            patch(path, params: {project_config: new_attributes}, headers:)

            project_config.reload
          end.to change(project_config, :vertical_logo)
            .and change(project_config, :android_version).from(nil).to("2.12.2")
            .and change(project_config, :ios_version).from(nil).to("2.12.2")
            .and change(project_config, :google_store_url).from(nil).to("https://play.google.com/store/apps/details?id=br.com.example")
            .and change(project_config, :apple_store_url).from(nil).to("https://apps.apple.com/br/app/example-app/id0123456789")
            .and change(project_config, :password_recovery_url).from(nil).to("https://foo.com/password-recovery")
            .and change(project_config, :user_update_url).from(nil).to("https://foo.com/user-update")
            .and change(project_config, :sign_up_url).from(nil).to("https://foo.com/sign-up-url")

          expect(response_hash["vertical_logo_url"]).to be_present
        end
      end

      context "when updating web_domain" do
        let(:new_attributes) { {web_domain: "new_domain.com"} }
        let(:aws_acm_client) do
          double("aws_acm_client",
            create: "new_certificate_arn",
            show: stubbed_certificate_response,
            delete: true)
        end
        let(:stubbed_certificate_response) do
          double("response",
            certificate: double("existing_certificate",
              domain_name: "newwebdomain.com",
              certificate_arn: "old_certificate_arn"),
            domain_validation_options: [
              {domain_name: "example.com",
               validation_domain: "example.com",
               validation_status: "SUCCESS",
               resource_record: {name: "_98fad938f20360cff077fb9b27aa1433.example.com.", type: "CNAME", value: "_a3ac3e557ddaeee6e42eb403c308a8fe.djqtsrsxkq.acm-validations.aws."},
               validation_method: "DNS"}
            ])
        end
        let(:aws_cloudfront_client) do
          double("aws_cloudfront_client",
            create: stubbed_distribution_response,
            update_domain: true,
            clear_domain: true)
        end
        let(:stubbed_distribution_response) do
          double("distribution",
            id: "ABCDEF123",
            domain_name: "deoqbnmvc4kwn.cloudfront.net",
            distribution_config: {
              enabled: true,
              aliases: {quantity: 1, items: ["newdomain.com.br"]},
              caller_reference: "original_caller_reference",
              all_other_params: {foo: "bar"}
            })
        end
        let(:web_application) { business.web_application }

        before do
          allow(AwsClient::Cloudfront::Distribution).to receive(:new).and_return(aws_cloudfront_client)
          allow(AwsClient::Acm::Certificate).to receive(:new).and_return(aws_acm_client)
        end

        context "when the web domain is present" do
          it "must call web application setup" do
            expect do
              path = [
                "/client/v2/businesses/#{business.cnpj}/project_config",
                "/client/v2/project_config"
              ].sample
              patch(path, params: {project_config: new_attributes}, headers:)

              expect(response).to be_successful
              web_application.reload
            end.to change(web_application, :cloudfront_distribution_url).to("deoqbnmvc4kwn.cloudfront.net")
              .and change(web_application, :acm_certificate_arn).to("new_certificate_arn")
              .and change(web_application, :certified_cnames)
          end

          context "when the web domain is blank" do
            let(:new_attributes) { {web_domain: ""} }

            it "must call web application setup" do
              expect do
                path = [
                  "/client/v2/businesses/#{business.cnpj}/project_config",
                  "/client/v2/project_config"
                ].sample
                patch(path, params: {project_config: new_attributes}, headers:)

                expect(response).to be_successful
                business.project_config.reload
              end.to change(business.project_config, :web_domain).to(nil)
            end
          end
        end

        context "when trying to update in a short space of time" do
          before do
            error = Aws::CloudFront::Errors::IllegalUpdate.new("update_distribution", "Only one viewer certificate change may be in progress at a time.")
            allow(aws_cloudfront_client).to receive(:create).and_raise(error)
            allow(Rails.application.credentials).to receive(:web_application_feature_flag).and_return(true)
          end

          it "return error" do
            expect do
              path = [
                "/client/v2/businesses/#{business.cnpj}/project_config",
                "/client/v2/project_config"
              ].sample
              patch(path, params: {project_config: new_attributes}, headers:)

              expect(response).to have_http_status(:unprocessable_entity)
              expect(response_hash["error"]).to eq("Foi realizado uma atualização de certificado recentemente! Tente novamente mais tarde.")
              web_application.reload
            end.to not_change(web_application, :cloudfront_distribution_url)
              .and not_change(web_application, :acm_certificate_arn)
              .and not_change(web_application, :certified_cnames)
          end
        end
      end
    end
  end
end
