# frozen_string_literal: true

require "rails_helper"

RSpec.describe Client::V2::CashbacksController, type: :request do
  let(:business) { create(:business, :oab, :with_cashback, cnpj: "03814381000130") }
  let(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end
  let(:json_parse_response_body) { JSON.parse(response.body) }

  describe "GET /client/v2/businesses/:business_identifier/cashbacks" do
    let!(:user_one) { create(:user, business:, cpf: "***********", name: "<PERSON><PERSON><PERSON>") }
    let!(:order_one) { create(:order, :online, user: user_one) }
    let!(:cashback_record_one) do
      create(:cashback_record,
        order: order_one,
        user: user_one,
        transaction_status: ["approved", "available_lecupon", "in_transfer_lecupon"].sample,
        order_amount: 150.51,
        total_commission_amount: 15.05,
        business_spread_amount: 1.51,
        cashback_amount: 13.54)
    end
    let!(:order_two) { create(:order, :online, user: user_one) }
    let!(:cashback_record_two) do
      create(:cashback_record,
        order: order_two,
        user: user_one,
        transaction_status: ["approved", "available_lecupon", "in_transfer_lecupon"].sample,
        order_amount: 100,
        total_commission_amount: 10,
        business_spread_amount: 1,
        cashback_amount: 9)
    end

    let!(:user_two) { create(:user, business:, cpf: "***********", name: "Ciclano") }
    let!(:order_three) { create(:order, :online, user: user_two) }
    let!(:cashback_record_three) do
      create(:cashback_record,
        order: order_three,
        user: user_two,
        transaction_status: ["in_transfer", "locked"].sample,
        order_amount: 1400,
        total_commission_amount: 140,
        business_spread_amount: 14,
        cashback_amount: 126)
    end
    let!(:order_four) { create(:order, :online, user: user_two) }
    let!(:cashback_record_four) do
      create(:cashback_record,
        order: order_four,
        user: user_two,
        transaction_status: ["in_transfer", "locked"].sample,
        order_amount: 200,
        total_commission_amount: 20,
        business_spread_amount: 2,
        cashback_amount: 18)
    end
    let!(:order_five) { create(:order, :online, user: user_two) }
    let!(:cashback_record_five) do
      create(:cashback_record,
        order: order_five,
        user: user_two,
        transaction_status: ["in_transfer", "locked"].sample,
        order_amount: 300,
        total_commission_amount: 30,
        business_spread_amount: 3,
        cashback_amount: 27)
    end
    let!(:order_six) { create(:order, :online, user: user_two) }
    let!(:cashback_record_six) do
      create(:cashback_record,
        order: order_six,
        user: user_two,
        transaction_status: ["in_transfer", "locked"].sample,
        order_amount: 400,
        total_commission_amount: 40,
        business_spread_amount: 4,
        cashback_amount: 36)
    end
    let!(:order_seven) { create(:order, :online, user: user_two) }
    let!(:cashback_record_seven) do
      create(:cashback_record,
        order: order_seven,
        user: user_two,
        transaction_status: ["in_transfer", "locked"].sample,
        order_amount: 300,
        total_commission_amount: 30,
        business_spread_amount: 3,
        cashback_amount: 27)
    end

    let!(:unrelated_business) { create(:business, :publicar, :with_cashback, cnpj: "32839587000113") }
    let!(:unrelated_user) { create(:user, business: unrelated_business, cpf: "***********", name: "Beltrano") }
    let!(:unrelated_order) { create(:order, :online, user: unrelated_user) }
    let!(:unrelated_cashback_record) do
      create(:cashback_record,
        order: unrelated_order,
        user: unrelated_user,
        transaction_status: ["in_transfer", "locked"].sample,
        order_amount: 1400,
        total_commission_amount: 140,
        business_spread_amount: 14,
        cashback_amount: 126)
    end

    context "when query is valid" do
      it "returns the cashbacks of the business, page 1" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/cashbacks",
          "/client/v2/cashbacks"
        ].sample
        get(path, params: {page: 1, start_date: Time.zone.today - 1.year, end_date: Time.zone.today}, headers:)

        expect(response).to have_http_status(:ok)
        expect(json_parse_response_body).to eq({
          "cashback_amount" => 256.54,
          "cashbacks" => [
            {
              "user_name" => "Ciclano",
              "taxpayer_number" => "309.087.230-75",
              "total_amount" => 234.0
            },
            {
              "user_name" => "Fulano",
              "taxpayer_number" => "238.950.780-85",
              "total_amount" => 22.54
            }
          ]
        })
      end

      it "returns the cashbacks of the business, page 2" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/cashbacks",
          "/client/v2/cashbacks"
        ].sample
        get(path, params: {page: 2, start_date: Time.zone.today - 1.year, end_date: Time.zone.today}, headers:)

        expect(response).to have_http_status(:ok)
        expect(json_parse_response_body).to eq({
          "cashback_amount" => 256.54,
          "cashbacks" => []
        })
      end

      it "returns the cashbacks of the business filtered by cpf" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/cashbacks",
          "/client/v2/cashbacks"
        ].sample
        get(path,
          params: {page: 1, start_date: Time.zone.today - 1.year, end_date: Time.zone.today, cpf: "238.950.780-85"},
          headers:)

        expect(response).to have_http_status(:ok)
        expect(json_parse_response_body).to eq({
          "cashback_amount" => 22.54,
          "cashbacks" => [
            {
              "user_name" => "Fulano",
              "taxpayer_number" => "238.950.780-85",
              "total_amount" => 22.54
            }
          ]
        })
      end

      it "returns the cashbacks of the business filtered by status visible to business" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/cashbacks",
          "/client/v2/cashbacks"
        ].sample
        get(path,
          params: {page: 1, start_date: Time.zone.today - 1.year, end_date: Time.zone.today, status: "approved"},
          headers:)

        expect(response).to have_http_status(:ok)
        expect(json_parse_response_body).to eq({
          "cashback_amount" => 22.54,
          "cashbacks" => [
            {
              "user_name" => "Fulano",
              "taxpayer_number" => "238.950.780-85",
              "total_amount" => 22.54
            }
          ]
        })
      end

      it "returns empty filtered by status not visible to business" do
        status_not_visible = (Enums::CashbackRecordStatus.all - Enums::CashbackRecordStatus.statuses_visible_to_user).sample

        path = [
          "/client/v2/businesses/#{business.cnpj}/cashbacks",
          "/client/v2/cashbacks"
        ].sample
        get(path,
          params: {page: 1, start_date: Time.zone.today - 1.year, end_date: Time.zone.today, status: status_not_visible},
          headers:)

        expect(response).to have_http_status(:ok)
        expect(json_parse_response_body).to eq({
          "cashback_amount" => 0.0,
          "cashbacks" => []
        })
      end

      it "returns cashbacks excluding those with business cashback disable" do
        cashback_record_one.update_columns(computable: false)

        path = [
          "/client/v2/businesses/#{business.cnpj}/cashbacks",
          "/client/v2/cashbacks"
        ].sample
        get(path,
          params: {page: 1, start_date: Date.current - 1.year, end_date: Date.current},
          headers:)

        expect(response).to be_ok
        expect(json_parse_response_body).to eq({
          "cashback_amount" => 243.0,
          "cashbacks" => [
            {
              "user_name" => "Ciclano",
              "taxpayer_number" => "309.087.230-75",
              "total_amount" => 234.0
            },
            {
              "user_name" => "Fulano",
              "taxpayer_number" => "238.950.780-85",
              "total_amount" => 9.0
            }
          ]
        })
      end
    end

    context "when query is invalid" do
      context "with a period range greather than 365 days" do
        it "returns error" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/cashbacks",
            "/client/v2/cashbacks"
          ].sample
          get(path,
            params: {page: 1, start_date: Time.zone.today - 1.year - 2.days, end_date: Time.zone.today},
            headers:)

          expect(response).to have_http_status(:unprocessable_entity)
        end
      end

      context "without page" do
        it "returns error" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/cashbacks",
            "/client/v2/cashbacks"
          ].sample
          get(path,
            params: {start_date: Time.zone.today - 1.year, end_date: Time.zone.today},
            headers:)

          expect(response).to have_http_status(:unprocessable_entity)
        end
      end

      context "without start date" do
        it "returns error" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/cashbacks",
            "/client/v2/cashbacks"
          ].sample
          get(path,
            params: {page: 1, end_date: Time.zone.today},
            headers:)

          expect(response).to have_http_status(:unprocessable_entity)
        end
      end

      context "without end date" do
        it "returns error" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/cashbacks",
            "/client/v2/cashbacks"
          ].sample
          get(path,
            params: {page: 1, start_date: Time.zone.today},
            headers:)

          expect(response).to have_http_status(:unprocessable_entity)
        end
      end

      context "when business has cashback disabled" do
        before do
          business.update!(cashback: false)
        end

        it "returns empty" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/cashbacks",
            "/client/v2/cashbacks"
          ].sample
          get(path,
            params: {page: 1, start_date: Time.zone.today - 1.year, end_date: Time.zone.today},
            headers:)

          expect(response).to have_http_status(:forbidden)
          expect(json_parse_response_body["error"]).to eq(I18n.t("inactive_cashback"))
        end
      end
    end
  end
end
