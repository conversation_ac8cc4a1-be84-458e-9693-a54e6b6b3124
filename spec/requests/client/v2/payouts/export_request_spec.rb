# frozen_string_literal: true

require "rails_helper"

RSpec.describe Client::V2::Payouts::ExportController, type: :request do
  include ActiveSupport::Testing::TimeHelpers

  let(:json_parse_response_body) { JSON.parse(response.body) }

  let!(:business) { create(:business, :with_cashback) }

  let(:user_one) { create(:user, cpf: "***********") }
  let(:payout_one) do
    create(:payout,
      user: user_one,
      user_business: business,
      receiver_taxpayer_number: "***********",
      total_amount: 22.54,
      status: Payout::Status::IN_TRANSFER,
      created_at: "2022-04-01 16:33:09")
  end
  let(:payout_two) do
    create(:payout,
      user: user_one,
      user_business: business,
      total_amount: 0.0,
      receiver_taxpayer_number: "***********",
      status: Payout::Status::IN_TRANSFER,
      created_at: "2022-03-31 23:59:58")
  end

  let(:user_two) { create(:user, cpf: "***********") }
  let(:payout_three) do
    create(:payout,
      user: user_two,
      user_business: business,
      total_amount: 144,
      receiver_taxpayer_number: "***********",
      status: Payout::Status::IN_TRANSFER,
      created_at: "2022-04-01 17:01:09")
  end
  let(:client_employee) { create(:client_employee, :admin_lecupon) }
  let(:headers) do
    {"X-ClientEmployee-Email": client_employee.email, "X-ClientEmployee-Token": client_employee.authentication_token}
  end

  before do
    travel_to Time.zone.parse("2022-04-05 16:33:09")
  end

  describe "POST /client/v2/cashback_batches/export" do
    let(:expected_file_content) do
      <<~CSV
        chave,valor,descricao
        ***********,22.54,Pix Resgate #{business.project_config.name}
        ***********,144.0,Pix Resgate #{business.project_config.name}
      CSV
    end

    context "when payoutes sucess locked" do
      let(:params) do
        {ids: [payout_one, payout_three].map(&:id)}
      end

      it "returns generated csv" do
        post("/client/v2/cashback_batches/export", params:, headers:)

        expect(response).to have_http_status(:ok)
        expect(response.headers["Content-Type"]).to eq("text/csv")
        expect(response.headers["Content-Disposition"]).to include('attachment; filename="remessa-20220405163309.csv"')
        expect(response.body).to eq(expected_file_content)
        expect([payout_one, payout_three].each(&:reload)).to all(be_locked)
      end
    end

    context "when payouts are locked or in_transfer" do
      let(:params) do
        {ids: [payout_one, payout_three].map(&:id)}
      end

      it "generate csv" do
        post("/client/v2/cashback_batches/export", params:, headers:)

        expect(response).to have_http_status(:ok)
        expect(response.headers["Content-Type"]).to eq("text/csv")
        expect(response.headers["Content-Disposition"]).to include('attachment; filename="remessa-20220405163309.csv"')
        expect(response.body).to eq(expected_file_content)
        expect([payout_one, payout_three].each(&:reload)).to all(be_locked)
      end
    end

    context "when status is invalid" do
      let(:payout_open) do
        create(:payout,
          :with_user,
          user_business: business,
          status: Payout::Status::OPEN)
      end
      let(:payout_in_transfer) do
        user = create(:user)
        create(:payout,
          user_business: business,
          user:,
          receiver_taxpayer_number: user.cpf,
          total_amount: 27.08,
          status: Payout::Status::IN_TRANSFER)
      end

      it "expect does not block any resources and return error" do
        params = {ids: [payout_open, payout_in_transfer].map(&:id)}

        post("/client/v2/cashback_batches/export", params:, headers:)

        expect(response).to have_http_status(:unprocessable_entity)
        expect(json_parse_response_body["error"]).to include("Status devem ser: em transferência")
        expect(payout_open.reload).to be_open
        expect(payout_in_transfer.reload).to be_in_transfer
      end
    end
  end
end
