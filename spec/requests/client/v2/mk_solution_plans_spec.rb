require "rails_helper"
require "support/shared_examples/client_employee_request_shared_examples"

RSpec.describe Client::V2::MkSolutionPlansController, type: :request do
  let(:business) { create(:business, :mk_solution_business) }

  let(:valid_attributes) {
    {
      name: "Plano X",
      external_id: "70",
      mk_solution_token_id: business.mk_solution_token.id
    }
  }

  let(:invalid_attributes) {
    {
      name: "Plano X",
      external_id: nil,
      mk_solution_token_id: business.mk_solution_token.id
    }
  }

  let(:response_hash) { JSON.parse(response.body) }
  let(:serialized_keys) { %w[id name external_id] }

  let!(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  describe "#index" do
    let!(:mk_solution_plans) { create_list(:mk_solution_plan, 2, mk_solution_token: business.mk_solution_token) }

    it "renders a successful response" do
      path = [
        "/client/v2/businesses/#{business.cnpj}/mk_solution_plans",
        "/client/v2/mk_solution_plans"
      ].sample
      get(path, params: {cnpj: business.cnpj}, headers:)

      expect(response).to be_successful
      expect(response_hash.map(&:keys)).to all(match_array(serialized_keys))
      expect(JSON.parse(response.body).pluck("external_id")).to match_array(mk_solution_plans.pluck(:external_id))
    end

    context "without client employee authorization" do
      before do
        path = [
          "/client/v2/businesses/#{business.cnpj}/mk_solution_plans",
          "/client/v2/mk_solution_plans"
        ].sample
        get path
      end

      it_behaves_like "unauthorized client_employee"
    end

    context "with unrelated client employee authorization" do
      let(:client_employee) { create(:client_employee, :admin_client) }

      it "returns not found" do
        get("/client/v2/businesses/#{business.cnpj}/mk_solution_plans", headers:)

        expect(response).to have_http_status(:not_found)
        expect(response_hash["error"]).to eq("Business não encontrado")
      end
    end
  end

  describe "#show" do
    let!(:mk_solution_plan) { create(:mk_solution_plan, mk_solution_token: business.mk_solution_token) }

    it "renders a successful response" do
      path = [
        "/client/v2/businesses/#{business.cnpj}/mk_solution_plans/#{mk_solution_plan.id}",
        "/client/v2/mk_solution_plans/#{mk_solution_plan.id}"
      ].sample
      get(path, params: {cnpj: business.cnpj}, headers:)

      expect(response).to be_successful
      expect(response_hash.keys).to match_array(serialized_keys)
      expect(JSON.parse(response.body).dig("external_id")).to eq(mk_solution_plan.external_id)
    end

    context "without client employee authorization" do
      before do
        path = [
          "/client/v2/businesses/#{business.cnpj}/mk_solution_plans/#{mk_solution_plan.id}",
          "/client/v2/mk_solution_plans/#{mk_solution_plan.id}"
        ].sample
        get path
      end

      it_behaves_like "unauthorized client_employee"
    end

    context "with unrelated client employee authorization" do
      let(:client_employee) { create(:client_employee, :admin_client) }

      it "returns not found" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/mk_solution_plans/#{mk_solution_plan.id}",
          "/client/v2/mk_solution_plans/#{mk_solution_plan.id}"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:not_found)
        expect(response_hash["error"]).to eq("Business não encontrado")
      end
    end
  end

  describe "#create" do
    let(:business) { create(:business, :mk_solution_business) }

    context "with valid parameters" do
      it "creates a new MkSolutionToken" do
        expect {
          path = [
            "/client/v2/businesses/#{business.cnpj}/mk_solution_plans",
            "/client/v2/mk_solution_plans"
          ].sample
          post(path, params: {mk_solution_plan: valid_attributes}, headers:)

          expect(response).to be_successful
          expect(response_hash.keys).to match_array(serialized_keys)
          business.reload
        }.to change(MkSolutionPlan, :count).by(1).and change(business.mk_solution_plans, :count).by(1)
      end
    end

    context "with invalid parameters" do
      it "does not create a new MkSolutionToken" do
        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/mk_solution_plans",
            "/client/v2/mk_solution_plans"
          ].sample
          post path, params: {mk_solution_plan: invalid_attributes}, headers:
        end.to change(MkSolutionPlan, :count).by(0)
      end

      it "renders a error" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/mk_solution_plans",
          "/client/v2/mk_solution_plans"
        ].sample
        post(path, params: {mk_solution_plan: invalid_attributes}, headers:)

        expect(response).to be_unprocessable
      end
    end

    context "without client employee authorization" do
      before do
        path = [
          "/client/v2/businesses/#{business.cnpj}/mk_solution_plans",
          "/client/v2/mk_solution_plans"
        ].sample
        post path, params: {mk_solution_plan: valid_attributes}
      end

      it_behaves_like "unauthorized client_employee"
    end

    context "with business unrelated to client employee" do
      context "when is lecupon admin" do
        let!(:client_employee) { create(:client_employee, :admin_lecupon) }

        it "creates a new MkSolutionToken" do
          expect do
            path = [
              "/client/v2/businesses/#{business.cnpj}/mk_solution_plans",
              "/client/v2/mk_solution_plans"
            ].sample
            post path, params: {mk_solution_plan: valid_attributes}, headers:
          end.to change(MkSolutionPlan, :count).by(1)
        end
      end

      context "when is not lecupon admin" do
        let(:client_employee) { create(:client_employee, :admin_client) }

        it "returns not found" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/mk_solution_plans",
            "/client/v2/mk_solution_plans"
          ].sample
          post(path, params: {mk_solution_plan: valid_attributes}, headers:)

          expect(response).to have_http_status(:not_found)
          expect(response_hash["error"]).to eq("Business não encontrado")
        end
      end
    end
  end

  describe "#update" do
    let!(:mk_solution_plan) { create(:mk_solution_plan, mk_solution_token: business.mk_solution_token) }

    let(:new_attributes) do
      {name: "Plano Z"}
    end

    context "with valid parameters" do
      it "updates the requested mk_solution_plan" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/mk_solution_plans/#{mk_solution_plan.id}",
          "/client/v2/mk_solution_plans/#{mk_solution_plan.id}"
        ].sample
        patch(path, params: {mk_solution_plan: new_attributes}, headers:)

        expect(response_hash.keys).to match_array(serialized_keys)
        mk_solution_plan.reload
        expect(mk_solution_plan.name).to eq("Plano Z")
      end
    end

    context "with invalid parameters" do
      it "renders error" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/mk_solution_plans/#{mk_solution_plan.id}",
          "/client/v2/mk_solution_plans/#{mk_solution_plan.id}"
        ].sample
        patch(path, params: {mk_solution_plan: invalid_attributes}, headers:)

        expect(response).to be_unprocessable
      end
    end

    context "without client employee authorization" do
      before do
        path = [
          "/client/v2/businesses/#{business.cnpj}/mk_solution_plans/#{mk_solution_plan.id}",
          "/client/v2/mk_solution_plans/#{mk_solution_plan.id}"
        ].sample
        patch path, params: {mk_solution_plan: new_attributes}
      end

      it_behaves_like "unauthorized client_employee"
    end

    context "with business_id unrelated to client employee" do
      context "when is lecupon admin" do
        let!(:client_employee) { create(:client_employee, :admin_lecupon) }

        it "updates the requested mk_solution_plan" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/mk_solution_plans/#{mk_solution_plan.id}",
            "/client/v2/mk_solution_plans/#{mk_solution_plan.id}"
          ].sample
          patch(path, params: {mk_solution_plan: new_attributes}, headers:)

          expect(response_hash.keys).to match_array(serialized_keys)
          mk_solution_plan.reload
          expect(mk_solution_plan.name).to eq("Plano Z")
        end
      end

      context "when is not lecupon admin" do
        let(:client_employee) { create(:client_employee, :admin_client) }

        it "returns not found" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/mk_solution_plans/#{mk_solution_plan.id}",
            "/client/v2/mk_solution_plans/#{mk_solution_plan.id}"
          ].sample
          patch(path, params: {mk_solution_plan: new_attributes}, headers:)

          expect(response).to have_http_status(:not_found)
          expect(response_hash["error"]).to eq("Business não encontrado")
        end
      end
    end
  end

  describe "#destroy" do
    let!(:mk_solution_plan) { create(:mk_solution_plan, mk_solution_token: business.mk_solution_token) }

    it "destroys the requested mk_solution_plan" do
      expect do
        path = [
          "/client/v2/businesses/#{business.cnpj}/mk_solution_plans/#{mk_solution_plan.id}",
          "/client/v2/mk_solution_plans/#{mk_solution_plan.id}"
        ].sample
        delete(path, headers:)

        expect(response).to be_no_content
      end.to change(MkSolutionPlan, :count).by(-1)
    end

    context "without client employee authorization" do
      before do
        delete "/client/v2/businesses/#{business.cnpj}/mk_solution_plans/#{mk_solution_plan.id}"
      end

      it_behaves_like "unauthorized client_employee"
    end

    context "with business unrelated to client employee" do
      context "when is lecupon admin" do
        let!(:client_employee) { create(:client_employee, :admin_lecupon) }

        it "destroys the requested mk_solution_plan" do
          expect do
            path = [
              "/client/v2/businesses/#{business.cnpj}/mk_solution_plans/#{mk_solution_plan.id}",
              "/client/v2/mk_solution_plans/#{mk_solution_plan.id}"
            ].sample
            delete(path, headers:)

            expect(response).to be_no_content
          end.to change(MkSolutionPlan, :count).by(-1)
        end
      end

      context "when is not lecupon admin" do
        let(:client_employee) { create(:client_employee, :admin_client) }

        it "returns not found" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/mk_solution_plans/#{mk_solution_plan.id}",
            "/client/v2/mk_solution_plans/#{mk_solution_plan.id}"
          ].sample
          delete(path, headers:)

          expect(response).to have_http_status(:not_found)
          expect(response_hash["error"]).to eq("Business não encontrado")
        end
      end
    end
  end
end
