require "rails_helper"

RSpec.describe Client::V2::Navigations::MenuIconsController, type: :request do
  let!(:business) { create(:business, :oab) }
  let!(:sub_business) { create(:business, main_business: business) }
  let!(:client_employee) { create(:client_employee, businesses: [business, sub_business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end
  let(:json_parse_response_body) { JSON.parse(response.body) }

  describe "GET /client/v2/businesses/:identifier/menu_icons" do
    let(:serialized_keys) do
      %w[name]
    end

    it "returns all icons" do
      path = [
        "/client/v2/businesses/#{business.cnpj}/menu_icons",
        "/client/v2/menu_icons"
      ].sample
      get(path, as: :json, headers:)

      expect(response).to have_http_status(:ok)
      expect(json_parse_response_body.map(&:keys)).to all(match_array(serialized_keys))
    end
  end
end
