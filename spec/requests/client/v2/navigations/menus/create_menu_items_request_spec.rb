require "rails_helper"

RSpec.describe Client::V2::Navigations::MenusController, type: :request do
  let!(:business) { create(:business, :oab) }
  let!(:sub_business) { create(:business, main_business: business) }
  let!(:client_employee) { create(:client_employee, businesses: [business, sub_business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end
  let(:json_parse_response_body) { JSON.parse(response.body) }

  describe "#create" do
    let(:serialized_keys) do
      %w[
        id
        cnpj_param_name
        cpf_param_name
        custom_field_1_param_name
        custom_field_2_param_name
        custom_field_3_param_name
        custom_field_4_param_name
        custom_field_5_param_name
        custom_field_6_param_name
        custom_field_7_param_name
        custom_field_8_param_name
        destination
        email_param_name
        encrypt_request
        external
        fixed
        header_param_name
        header_param_value
        http_method
        icon
        item_order
        menu_type
        subtitle
        title
        device_type
        mobile_navigation_option
        web_navigation_option
        kind
        provider
      ]
    end

    context "when de menu already exist" do
      it "creates the menu_item" do
        _menu_one = create(:menu, business:, position: 1)
        _menu_two = create(:menu, business:, position: 2)
        _menu_999 = create(:menu, business:).update_columns(position: 999)

        params = {
          cnpj: business.cnpj,
          title: "new menu",
          destination: "https://wwww.example.com",
          menu_type: "profile_icon",
          icon: "list",
          provider: Navigation.providers[:external_link],
          mobile_navigation_option: "browser",
          web_navigation_option: "blank"
        }

        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/menu_items",
            "/client/v2/menu_items"
          ].sample
          post path, params:, headers:, as: :json
        end.to change(Navigation, :count).by(1)
        expect(response).to have_http_status(:created)
        menu = Navigation.kind_menu.last
        expect(menu.exclusive_business).to eq(nil)
        expect(menu.provider).to eq(Navigation.providers[:external_link])
        expect(json_parse_response_body.keys).to match_array(serialized_keys)
        expect(json_parse_response_body["item_order"]).to eq(1000)
        expect(json_parse_response_body["kind"]).to eq("menu")
      end

      it "with invalid destination" do
        _menu_one = create(:menu, business:, position: 1)
        _menu_two = create(:menu, business:, position: 2)
        _menu_999 = create(:menu, business:).update_columns(position: 999)

        params = {
          cnpj: business.cnpj,
          title: "new menu",
          destination: " https://wwww.example.com ",
          menu_type: "profile_icon",
          icon: "list",
          external: true,
          mobile_navigation_option: "browser",
          web_navigation_option: "blank"
        }

        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/menu_items",
            "/client/v2/menu_items"
          ].sample
          post path, params:, headers:, as: :json
        end.to change(Navigation, :count).by(0)
        expect(response).to have_http_status(:unprocessable_entity)
        expect(json_parse_response_body["error"]).to eq("Destino não é possível usar uma URL externa em menus internos, Destino Formato fornecido no destination é inválido")
      end
    end

    context "when current business is sub business" do
      let(:headers) do
        {
          "X-ClientEmployee-Email": client_employee.email,
          "X-ClientEmployee-Token": client_employee.authentication_token,
          "Tenant-id": sub_business.cnpj
        }
      end

      it "creates the menu" do
        create(:client_employee, businesses: [sub_business])

        params = {
          cnpj: business.cnpj,
          title: "new menu",
          destination: "https://wwww.example.com",
          menu_type: "profile_icon",
          icon: "list",
          external: true,
          mobile_navigation_option: "browser",
          web_navigation_option: "blank"
        }
        path = [
          "/client/v2/businesses/#{sub_business.cnpj}/menu_items",
          "/client/v2/menu_items"
        ].sample
        post path, params:, headers:, as: :json

        expect(response).to have_http_status(:created)
        expect(Navigation.last.exclusive_business).to eq(sub_business)
        expect(json_parse_response_body.keys).to match_array(serialized_keys)
      end
    end

    context "when the menu is a smart_link" do
      let(:headers) do
        {
          "X-ClientEmployee-Email": client_employee.email,
          "X-ClientEmployee-Token": client_employee.authentication_token,
          "Tenant-id": sub_business.cnpj
        }
      end

      it "cannot be of web type" do
        create(:client_employee, businesses: [sub_business])

        params = {
          cnpj: business.cnpj,
          title: "new menu",
          destination: "https://wwww.example.com",
          menu_type: "profile_icon",
          icon: "list",
          external: true,
          device_type: Navigation::DeviceType::WEB,
          smart_link: true,
          mobile_navigation_option: "browser",
          web_navigation_option: "blank"
        }

        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/menu_items",
            "/client/v2/menu_items"
          ].sample
          post path, params:, headers:, as: :json
        end.not_to change(Navigation, :count)

        expect(response).to have_http_status(:unprocessable_content)
        expect(json_parse_response_body["error"]).to eq("Tipo de dispositivo invalido para smart link")
      end

      it "creates the menu" do
        create(:client_employee, businesses: [sub_business])

        params = {
          cnpj: business.cnpj,
          title: "new menu",
          destination: "https://wwww.example.com",
          menu_type: "profile_icon",
          icon: "list",
          device_type: "mobile",
          external: true,
          smart_link: true,
          mobile_navigation_option: "browser",
          web_navigation_option: "blank"
        }

        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/menu_items",
            "/client/v2/menu_items"
          ].sample
          post path, params:, headers:, as: :json
        end.to change(Navigation, :count).by(1)

        expect(response).to have_http_status(:created)
        menu = Navigation.kind_menu.last
        expect(menu.smart_link).to be true
      end
    end

    describe "setting the navigation_option" do
      let(:params) do
        {
          cnpj: business.cnpj,
          title: "new menu",
          destination: "https://wwww.example.com",
          menu_type: "profile_icon",
          icon: "list",
          external: true
        }
      end

      context "when the params includes 'mobile_navigation_option'" do
        before do
          params[:mobile_navigation_option] = Navigation.mobile_navigation_options[:browser]
          params[:web_navigation_option] = Navigation.web_navigation_options[:blank]
        end

        it "sets the mobile_navigation_option to browser" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/menu_items",
            "/client/v2/menu_items"
          ].sample
          post path, params:, headers:, as: :json

          expect(response).to have_http_status(:created)
          menu = Navigation.last
          expect(menu.mobile_navigation_option).to eq(Navigation.mobile_navigation_options[:browser])
        end
      end

      context "when the params includes an embedded navigation option" do
        before do
          params[:mobile_navigation_option] = Navigation.mobile_navigation_options[:embedded]
          params[:web_navigation_option] = Navigation.web_navigation_options[:blank]
        end

        it "sets the navigation_option to embedded" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/menu_items",
            "/client/v2/menu_items"
          ].sample
          post path, params:, headers:, as: :json

          expect(response).to have_http_status(:created)
          menu = Navigation.last
          expect(menu.mobile_navigation_option).to eq(Navigation.mobile_navigation_options[:embedded])
        end
      end
    end
  end
end
