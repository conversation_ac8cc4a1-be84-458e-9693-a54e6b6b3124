require "rails_helper"

RSpec.describe Client::V2::Navigations::MenusController, type: :request do
  let!(:business) { create(:business, :oab) }
  let!(:sub_business) { create(:business, main_business: business) }
  let!(:client_employee) { create(:client_employee, businesses: [business, sub_business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end
  let(:json_parse_response_body) { JSON.parse(response.body) }

  describe "#update" do
    let(:serialized_keys) do
      %w[
        id
        cnpj_param_name
        cpf_param_name
        custom_field_1_param_name
        custom_field_2_param_name
        custom_field_3_param_name
        custom_field_4_param_name
        custom_field_5_param_name
        custom_field_6_param_name
        custom_field_7_param_name
        custom_field_8_param_name
        destination
        email_param_name
        encrypt_request
        external
        fixed
        header_param_name
        header_param_value
        http_method
        icon
        item_order
        menu_type
        subtitle
        title
        device_type
        mobile_navigation_option
        web_navigation_option
        kind
        provider
      ]
    end
    let(:create_menu_one) { create(:menu, business:, position: 1) }
    let(:create_menu_two) { create(:menu, business:, position: 2) }

    context "when send valid menu_type param" do
      it "updates the menu" do
        menu_one = create_menu_one
        create_menu_two

        params = {
          cnpj: business.cnpj,
          title: "new menu",
          destination: "https://wwww.example.com",
          item_order: 2,
          external: true
        }

        path = [
          "/client/v2/businesses/#{business.cnpj}/menu_items/#{menu_one.id}",
          "/client/v2/menu_items/#{menu_one.id}"
        ].sample
        patch path, params:, headers:, as: :json

        menu_one.reload
        expect(menu_one.position).to eq(2)
        expect(response).to have_http_status(:success)
        expect(json_parse_response_body.keys).to match_array(serialized_keys)
      end
    end

    context "when send invalid menu_type param" do
      specify do
        menu_one = create_menu_one
        params = {
          menu_type: "INVALID",
          cnpj: business.cnpj,
          title: "new menu",
          destination: "https://wwww.example.com",
          item_order: 10,
          external: true
        }

        path = [
          "/client/v2/businesses/#{business.cnpj}/menu_items/#{menu_one.id}",
          "/client/v2/menu_items/#{menu_one.id}"
        ].sample
        patch path, params:, headers:, as: :json

        expect(response).to have_http_status(:unprocessable_entity)
      end

      specify do
        menu_one = create_menu_one

        params = {
          cnpj: business.cnpj,
          title: "new menu",
          destination: "https://wwww.example.com",
          item_order: 10,
          menu_type: "",
          external: true
        }
        path = [
          "/client/v2/businesses/#{business.cnpj}/menu_items/#{menu_one.id}",
          "/client/v2/menu_items/#{menu_one.id}"
        ].sample
        patch path, params:, headers:, as: :json

        expect(response).to have_http_status(:unprocessable_entity)
      end
    end

    context "when position already exist" do
      it do
        _menu_one = create(:menu, business:, position: 1)
        menu_two = create(:menu, business:, position: 2)
        _menu_999 = create(:menu, business:).update_columns(position: 999)

        params = {
          cnpj: business.cnpj,
          title: "new menu",
          destination: "https://wwww.example.com",
          item_order: 1,
          external: true
        }
        path = [
          "/client/v2/businesses/#{business.cnpj}/menu_items/#{menu_two.id}",
          "/client/v2/menu_items/#{menu_two.id}"
        ].sample
        patch path, params:, headers:, as: :json

        expect(response).to have_http_status(:success)
        expect(Navigation.kind_menu.order(:position).pluck(:position)).to eq([1, 2, 999])
      end
    end

    context "when update only position" do
      it "expect not update icon" do
        menu = create(
          :menu,
          exclusive_business: business,
          business:,
          position: 1,
          icon: "search"
        )
        params = {position: 2}

        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/menu_items/#{menu.id}",
            "/client/v2/menu_items/#{menu.id}"
          ].sample
          patch path, params:, headers:, as: :json
        end.to not_change { menu.reload.icon }
        expect(response).to have_http_status(:success)
      end
    end

    context "when there are sub business" do
      let(:headers) do
        {
          "X-ClientEmployee-Email": client_employee.email,
          "X-ClientEmployee-Token": client_employee.authentication_token,
          "Tenant-id": sub_business.cnpj
        }
      end
      let(:create_sub_menu_one) { create(:menu, business:, exclusive_business: sub_business, position: 3) }
      let(:create_sub_menu_two) { create(:menu, business:, exclusive_business: sub_business, position: 4) }
      let(:create_menu_three) { create(:menu, business:, position: 5) }

      it "reorders business and sub_business successfully" do
        menu_one = create_menu_one
        create_menu_two
        sub_menu_one = create_sub_menu_one
        sub_menu_two = create_sub_menu_two
        sub_menu_three = create_menu_three

        params = {
          cnpj: sub_business.cnpj,
          title: "new menu",
          destination: "https://wwww.example.com",
          item_order: 1,
          external: true
        }

        expect do
          path = [
            "/client/v2/businesses/#{sub_business.cnpj}/menu_items/#{sub_menu_three.id}",
            "/client/v2/menu_items/#{sub_menu_three.id}"
          ].sample
          patch path, params:, as: :json, headers: headers

          menu_one.reload
          sub_menu_one.reload
          sub_menu_two.reload
          sub_menu_three.reload
        end.to change { [menu_one.position, sub_menu_one.position, sub_menu_two.position, sub_menu_three.position] }.from([1, 3, 4, 5]).to([2, 4, 5, 1])

        expect(response).to have_http_status(:success)
      end
    end

    context "when the sub business is telemedicine and the main is not" do
      let(:create_telemedicine_sub_menu) { create(:menu, business:, exclusive_business: sub_business, destination: "/telemedicine", position: 3) }

      before do
        business.update(telemedicine: false)
        sub_business.update(telemedicine: true)
      end

      context "and I update the position of the telemedicine menu" do
        let(:headers) do
          {
            "X-ClientEmployee-Email": client_employee.email,
            "X-ClientEmployee-Token": client_employee.authentication_token,
            "Tenant-id": sub_business.cnpj
          }
        end

        it "reorders successfully" do
          menu_one = create_menu_one
          menu_two = create_menu_two
          telemedicine_sub_menu = create_telemedicine_sub_menu

          params = {
            cnpj: sub_business.cnpj,
            title: "new menu",
            destination: "https://wwww.example.com",
            item_order: 1,
            external: true
          }

          expect {
            path = [
              "/client/v2/businesses/#{sub_business.cnpj}/menu_items/#{telemedicine_sub_menu.id}",
              "/client/v2/menu_items/#{telemedicine_sub_menu.id}"
            ].sample
            patch path, params:, as: :json, headers: headers

            menu_one.reload
            menu_two.reload
            telemedicine_sub_menu.reload
          }.to change { [menu_one.position, menu_two.position, telemedicine_sub_menu.position] }.from([1, 2, 3]).to([2, 3, 1])

          expect(response).to have_http_status(:success)
        end
      end

      context "and I update the telemedicine menu indirectly" do
        let(:headers) do
          {
            "X-ClientEmployee-Email": client_employee.email,
            "X-ClientEmployee-Token": client_employee.authentication_token,
            "Tenant-id": sub_business.cnpj
          }
        end

        it "reorders successfully" do
          menu_one = create_menu_one
          menu_two = create_menu_two
          telemedicine_sub_menu = create_telemedicine_sub_menu

          params = {
            cnpj: business.cnpj,
            title: "new menu",
            destination: "https://wwww.example.com",
            item_order: 3,
            external: true
          }

          expect do
            path = [
              "/client/v2/businesses/#{sub_business.cnpj}/menu_items/#{menu_one.id}",
              "/client/v2/menu_items/#{menu_one.id}"
            ].sample
            patch path, params:, as: :json, headers: headers

            menu_one.reload
            menu_two.reload
            telemedicine_sub_menu.reload
          end.to change { [menu_one.position, menu_two.position, telemedicine_sub_menu.position] }.from([1, 2, 3]).to([3, 1, 2])

          expect(response).to have_http_status(:success)
        end
      end
    end

    context "when update menu_type" do
      it do
        menu = create(
          :menu,
          business:,
          disposition: "drawer"
        )

        params = {menu_type: "drawer"}
        path = [
          "/client/v2/businesses/#{business.cnpj}/menu_items/#{menu.id}",
          "/client/v2/menu_items/#{menu.id}"
        ].sample
        patch path, params:, headers:, as: :json

        expect(response).to have_http_status(:success)
        expect(menu.reload.disposition).to eq("drawer")
      end
    end
  end
end
