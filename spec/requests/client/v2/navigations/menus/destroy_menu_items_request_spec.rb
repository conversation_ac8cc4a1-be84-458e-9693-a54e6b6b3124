require "rails_helper"

RSpec.describe Client::V2::Navigations::MenusController, type: :request do
  let!(:business) { create(:business, :oab) }
  let!(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end
  let(:json_parse_response_body) { JSON.parse(response.body) }

  describe "#destroy" do
    it "deletes the menu" do
      menu = create(:menu, business:, disposition: "profile_icon")

      path = [
        "/client/v2/businesses/#{business.cnpj}/menu_items/#{menu.id}",
        "/client/v2/menu_items/#{menu.id}"
      ].sample
      delete path, headers:, as: :json

      expect(response).to have_http_status(:ok)
      expect(Navigation.kind_menu.count).to eq(0)
    end

    context "when menu is from another business" do
      it do
        another_business = create(:business)
        another_menu = create(:menu, business: another_business, disposition: "profile_icon")

        path = [
          "/client/v2/businesses/#{business.cnpj}/menu_items/#{another_menu.id}",
          "/client/v2/menu_items/#{another_menu.id}"
        ].sample
        delete path, headers:, as: :json

        expect(response).to have_http_status(:not_found)
      end
    end

    context "when navigation is banner" do
      it do
        business.update(telemedicine: true)
        menu = create(:banner, :telemedicine, business:)

        path = [
          "/client/v2/businesses/#{business.cnpj}/menu_items/#{menu.id}",
          "/client/v2/menu_items/#{menu.id}"
        ].sample
        delete path, headers:, as: :json

        expect(response).to have_http_status(:not_found)
        expect(Navigation.kind_banner.count).to eq(1)
      end
    end
  end
end
