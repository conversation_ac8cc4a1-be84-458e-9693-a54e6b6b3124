require "rails_helper"

RSpec.describe Client::V2::Navigations::MenusController, type: :request do
  let!(:business) { create(:business, :oab) }
  let!(:sub_business) { create(:business, main_business: business) }
  let!(:client_employee) { create(:client_employee, businesses: [business, sub_business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end
  let(:json_parse_response_body) { JSON.parse(response.body) }

  describe "#index" do
    let(:serialized_keys) do
      %w[
        id
        cnpj_param_name
        cpf_param_name
        custom_field_1_param_name
        custom_field_2_param_name
        custom_field_3_param_name
        custom_field_4_param_name
        custom_field_5_param_name
        custom_field_6_param_name
        custom_field_7_param_name
        custom_field_8_param_name
        destination
        email_param_name
        encrypt_request
        external
        fixed
        header_param_name
        header_param_value
        http_method
        icon
        item_order
        menu_type
        subtitle
        title
        device_type
        mobile_navigation_option
        web_navigation_option
        kind
        provider
      ]
    end
    let(:create_main_business_menu_one) { create(:menu, title: "Menu Main 1", business:, exclusive_business: nil, disposition: "profile_icon", position: 1) }
    let(:create_main_business_menu_two) { create(:menu, title: "Menu Main 2", business:, exclusive_business: nil, disposition: "profile_icon", position: 2) }
    let(:create_exclusive_business_menu_one) { create(:menu, title: "Menu Sub 1", business:, exclusive_business: sub_business, disposition: "profile_icon", position: 3) }
    let(:create_exclusive_business_menu_two) { create(:menu, title: "Menu Sub 2", business:, exclusive_business: sub_business, disposition: "profile_icon", position: 4) }
    let(:create_main_business_menu_three) { create(:menu, title: "Menu Main 3", business:, exclusive_business: nil, disposition: "profile_icon", position: 5) }
    let(:create_main_business_menu_four) { create(:menu, title: "Menu Main 4", business:, exclusive_business: nil, disposition: "profile_icon", position: 6) }
    let(:create_different_disposition_menu) { create(:menu, title: "Menu Drawer", business: sub_business, disposition: "drawer", position: 1) }

    context "when current_business are main_business" do
      it "returns profile_icon menus of the business" do
        main_business_menu_one = create_main_business_menu_one
        main_business_menu_two = create_main_business_menu_two
        create_exclusive_business_menu_one
        create_exclusive_business_menu_two
        main_business_menu_three = create_main_business_menu_three
        main_business_menu_four = create_main_business_menu_four
        create_different_disposition_menu

        params = {menu_type: "profile_icon"}
        path = [
          "/client/v2/businesses/#{business.cnpj}/menu_items",
          "/client/v2/menu_items"
        ].sample
        get path, headers:, params:, as: :json

        expect(response).to have_http_status(:success)
        expect(json_parse_response_body.count).to eq(4)
        expect(json_parse_response_body.pluck("id")).to match_array([
          main_business_menu_one.id,
          main_business_menu_two.id,
          main_business_menu_three.id,
          main_business_menu_four.id
        ])
        expect(json_parse_response_body.map(&:keys)).to all(match_array(serialized_keys))
        expect(json_parse_response_body.pluck("item_order")).to eq([1, 2, 5, 6])
        expect(json_parse_response_body.pluck("item_order")).not_to include([3, 4])
      end
    end

    context "when current_business are sub_business" do
      let(:headers) do
        {
          "X-ClientEmployee-Email": client_employee.email,
          "X-ClientEmployee-Token": client_employee.authentication_token,
          "Tenant-id": sub_business.cnpj
        }
      end

      before do
        create_main_business_menu_one
        create_main_business_menu_two
        create_exclusive_business_menu_one
        create_exclusive_business_menu_two
        create_main_business_menu_three
        create_main_business_menu_four
        create_different_disposition_menu
      end

      it "returns all menus of the business" do
        params = {menu_type: "profile_icon"}
        path = [
          "/client/v2/businesses/#{sub_business.cnpj}/menu_items",
          "/client/v2/menu_items"
        ].sample
        get path, headers:, params:, as: :json

        expect(response).to have_http_status(:success)
        expect(json_parse_response_body.count).to eq(6)
        expect(json_parse_response_body.map(&:keys)).to all(match_array(serialized_keys))
      end

      it "returns the menu in order" do
        params = {menu_type: "profile_icon"}
        path = [
          "/client/v2/businesses/#{sub_business.cnpj}/menu_items",
          "/client/v2/menu_items"
        ].sample
        get path, headers:, params:, as: :json

        expect(json_parse_response_body.pluck("item_order")).to eq([1, 2, 3, 4, 5, 6])
      end
    end

    context "when menu_type param is invalid" do
      it do
        params = {menu_type: ""}
        path = [
          "/client/v2/businesses/#{sub_business.cnpj}/menu_items",
          "/client/v2/menu_items"
        ].sample
        get path, headers:, params:, as: :json

        expect(response).to have_http_status(:precondition_failed)
        expect(json_parse_response_body["error"]).to eq("A requisição falhou devido a ausência de parâmetro: menu_type")
      end

      it do
        params = {menu_type: "INVALIDTYPE"}
        path = [
          "/client/v2/businesses/#{sub_business.cnpj}/menu_items",
          "/client/v2/menu_items"
        ].sample
        get path, params:, headers:, as: :json

        expect(response).to have_http_status(:unprocessable_entity)
        expect(json_parse_response_body["error"]).to eq("Parâmetro menu_type inválido. Os valores aceitos são: 'drawer, footer, header, navigation, profile_icon'.")
      end
    end
  end
end
