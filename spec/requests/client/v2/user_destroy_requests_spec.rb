# frozen_string_literal: true

require "rails_helper"

RSpec.describe Client::V2::UserDestroyRequestsController, type: :request do
  let!(:business) { create(:business, :oab, cnpj: "03814381000130") }
  let!(:client_employee) { create(:client_employee, :admin_client, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  let(:response_hash) { JSON.parse(response.body) }

  describe "#index" do
    let!(:users) { create_list(:user, 3, business:) }

    context "when is authorized client employee" do
      before do
        users.each do |user|
          user.user_destroy_requests.create(reason: "Any reason", requester_type: Enums::UserDestroyRequest::Requesters::USER)
        end
      end

      context "when user destroy request exists for users on current business" do
        it "must return user" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/user_destroy_requests",
            "/client/v2/user_destroy_requests"
          ].sample
          get(path, headers:)

          expect(response).to be_successful
          expect(response_hash.first.keys).to match_array(%w[id reason request_status requester_type user_id created_at updated_at])
          expect(response_hash.count).to eq(users.count)
        end
      end

      context "when filtering users by cpf" do
        let(:params) { {cpf: users.first.cpf} }
        let!(:another_user) { create(:user, business:) }
        let!(:another_user_destroy_request) { another_user.user_destroy_requests.create(reason: "Any reason", requester_type: Enums::UserDestroyRequest::Requesters::USER) }

        it "must return user" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/user_destroy_requests",
            "/client/v2/user_destroy_requests"
          ].sample
          get(path, headers:, params:)

          expect(response).to be_successful
          expect(response_hash.pluck("user_id")).not_to include(another_user.id)

          expect(response_hash.count).to eq(1)
          expect(response_hash.first["user_id"]).to eq(users.first.id)
        end
      end

      context "when filtering users by request status" do
        let(:params) { {request_status: Enums::UserDestroyRequest::Status::PROCESSED} }
        let(:processed_destroy_request) { UserDestroyRequest.first }
        let(:unprocessed_destroy_request) { UserDestroyRequest.second }

        before do
          processed_destroy_request.update!(request_status: Enums::UserDestroyRequest::Status::PROCESSED)

          unprocessed_destroy_request.update!(request_status: Enums::UserDestroyRequest::Status::UNPROCESSED)
        end

        it "must return user" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/user_destroy_requests",
            "/client/v2/user_destroy_requests"
          ].sample
          get(path, headers:, params:)

          expect(response).to be_successful
          expect(response_hash.pluck("id")).to include(processed_destroy_request.id)
          expect(response_hash.pluck("id")).not_to include(unprocessed_destroy_request.id)
        end
      end

      context "when does not exist user destroy requests on current business" do
        before do
          UserDestroyRequest.delete_all
        end

        let!(:another_business_user) { create(:user) }

        it "must return empty" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/user_destroy_requests",
            "/client/v2/user_destroy_requests"
          ].sample
          get(path, headers:)

          expect(response_hash).to be_empty
        end
      end
    end

    context "when not authorized" do
      it "must return unauthorized" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/user_destroy_requests",
          "/client/v2/user_destroy_requests"
        ].sample
        get path

        expect(response).to be_unauthorized
      end
    end
  end
end
