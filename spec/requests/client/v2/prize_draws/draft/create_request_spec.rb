require "rails_helper"
require "sidekiq/testing"

RSpec.describe Client::V2::PrizeDraws::DraftController, type: :request do
  let(:business) { create(:business) }
  let(:client_employee) { create(:client_employee, password: 123456, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  let(:json_parse_response_body) { JSON.parse(response.body) }

  describe "#create" do
    let(:prize_draw_serializer_keys) do
      %w[
        id
        product_image
        product_name
        code
        caixa_identifier
        ends_at
        run_at
        rules
        sefaz_registration
        starts_at
        status
      ]
    end

    let(:product_image) do
      tempfile = Tempfile.new(["product_image", ".png"], "tmp")

      Rack::Test::UploadedFile.new(tempfile)
    end

    it "returns a successful response" do
      params = attributes_for(:prize_draw, product_image:, sefaz_registration: FFaker::Number.number(digits: 10))

      expect do
        path = [
          "/client/v2/businesses/#{business.cnpj}/prize_draws/draft",
           "/client/v2/prize_draws/draft"
        ].sample
        post path, params:, headers:
      end.to change(PrizeDraw, :count).by(1)
      expect(response).to have_http_status(:created)
      expect(json_parse_response_body.keys).to match_array(prize_draw_serializer_keys)
    end

    context "when the starts_at is invalid" do
      it "returns response with error" do
        params = attributes_for(
          :prize_draw,
          product_image:,
          starts_at: DateTime.yesterday,
          ends_at: DateTime.current,
          run_at: DateTime.current + 1.month
        )

        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/prize_draws/draft",
             "/client/v2/prize_draws/draft"
          ].sample
          post path, params:, headers:
        end.to change(PrizeDraw, :count).by(0)
        expect(response).to have_http_status(:unprocessable_entity)

        expect(json_parse_response_body["error"])
          .to include("data/hora início deve ser maior ou igual a")
      end
    end

    context "when the product_image has a invalid" do
      let(:invalid_product_image) do
        tempfile = Tempfile.new(["product_image", ".pdf"], "tmp")

        Rack::Test::UploadedFile.new(tempfile)
      end

      it "returns response with error" do
        params = attributes_for(:prize_draw, product_image: invalid_product_image)

        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/prize_draws/draft",
             "/client/v2/prize_draws/draft"
          ].sample
          post path, params:, headers:
        end.to change(PrizeDraw, :count).by(0)
        expect(response).to have_http_status(:unprocessable_entity)
        expect(json_parse_response_body["error"])
          .to match(/Tipos permitidos: png, jpeg, jpg, svg/)
      end
    end
  end
end
