require "rails_helper"

RSpec.describe Client::V2::PrizeDraws::DraftController, type: :request do
  let!(:business) { create(:business) }
  let!(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  let(:json_parse_response_body) { JSON.parse(response.body) }

  describe "#update" do
    let(:prize_draw_serializer_keys) do
      %w[
        id
        product_image
        product_name
        code
        ends_at
        run_at
        sefaz_registration
        starts_at
        status
      ]
    end

    let(:png_tempfile) do
      Rack::Test::UploadedFile.new(Tempfile.new(["premio", ".png"], "tmp"))
    end

    let(:draft_prize_draw) { create_prize_draw(status: :draft, business:) }
    let(:prize_draw_params) do
      attributes_for(:prize_draw)
        .merge(
          product_image: png_tempfile,
          rules: FFaker::LoremBR.paragraph,
          sefaz_registration: FFaker::Number.number(digits: 10)
        )
    end

    context "when try update" do
      it "is expected success" do
        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/prize_draws/#{draft_prize_draw.id}/draft",
            "/client/v2/prize_draws/#{draft_prize_draw.id}/draft"
          ].sample
          put(path, params: prize_draw_params, headers:)

          draft_prize_draw.reload
        end.to change(draft_prize_draw, :product_name)
          .and change { draft_prize_draw.product_image.url }
          .and change(draft_prize_draw, :starts_at)
          .and change(draft_prize_draw, :ends_at)
          .and change(draft_prize_draw, :rules)
          .and change(draft_prize_draw, :run_at)
          .and change(draft_prize_draw, :sefaz_registration)
          .and not_change(draft_prize_draw, :business_id)
        expect(response).to have_http_status(:ok)
      end
    end

    context "when trying to update some columns" do
      it "is expected success" do
        prize_draw_params = attributes_for(:prize_draw).except(:product_image, :rules, :sefaz_registration)

        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/prize_draws/#{draft_prize_draw.id}/draft",
            "/client/v2/prize_draws/#{draft_prize_draw.id}/draft"
          ].sample
          put(path, params: prize_draw_params, headers:)

          draft_prize_draw.reload
        end.to change(draft_prize_draw, :product_name)
          .and not_change { draft_prize_draw.product_image.url }
          .and change(draft_prize_draw, :starts_at)
          .and change(draft_prize_draw, :ends_at)
          .and not_change(draft_prize_draw, :rules)
          .and change(draft_prize_draw, :run_at)
          .and not_change(draft_prize_draw, :sefaz_registration)
          .and not_change(draft_prize_draw, :business_id)
        expect(response).to have_http_status(:ok)
      end
    end

    context "when prize draw is to_be_published" do
      it "is expected that not be possible to update" do
        prize_draw = create_prize_draw(status: :to_be_published, business:)

        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/prize_draws/#{prize_draw.id}/draft",
            "/client/v2/prize_draws/#{prize_draw.id}/draft"
          ].sample
          put path, params: prize_draw_params, headers:
        end.to not_change { prize_draw.reload.attributes }

        expect(response).to have_http_status(:not_found)
      end
    end

    context "when prize draw is published" do
      it "is expected that not be possible to update" do
        prize_draw = create_prize_draw(status: :published, business:)

        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/prize_draws/#{prize_draw.id}/draft",
            "/client/v2/prize_draws/#{prize_draw.id}/draft"
          ].sample
          put path, params: prize_draw_params, headers:
        end.to not_change { prize_draw.attributes }

        expect(response).to have_http_status(:not_found)
      end
    end

    context "when prize draw is finished" do
      it "is expected that not be possible to update" do
        prize_draw = create_prize_draw(status: :finished, business:)

        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/prize_draws/#{prize_draw.id}/draft",
            "/client/v2/prize_draws/#{prize_draw.id}/draft"
          ].sample
          put path, params: prize_draw_params, headers:
        end.to not_change { prize_draw.attributes }

        expect(response).to have_http_status(:not_found)
      end
    end

    context "when prize draw is canceled" do
      it "is expected that not be possible to update" do
        prize_draw = create_prize_draw(status: :canceled, business:)

        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/prize_draws/#{prize_draw.id}/draft",
            "/client/v2/prize_draws/#{prize_draw.id}/draft"
          ].sample
          put path, params: prize_draw_params, headers:
        end.to not_change { prize_draw.attributes }

        expect(response).to have_http_status(:not_found)
      end
    end

    context "when prize_draw is from another business" do
      it "is expected that not be possible to update" do
        another_business = create(:business)
        prize_draw = create_prize_draw(status: :draft, business: another_business)

        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/prize_draws/#{prize_draw.id}/draft",
            "/client/v2/prize_draws/#{prize_draw.id}/draft"
          ].sample
          put path, params: prize_draw_params, headers:
        end.to not_change { prize_draw.attributes }

        expect(response).to have_http_status(:not_found)
      end
    end

    def create_prize_draw(status:, business:)
      allow(PrizeDraw::Status::Machine).to receive(:successors).and_return({"draft" => ["to_be_published", "published", "finished", "canceled"]})
      create(
        :prize_draw,
        initial_state: status,
        business:
      )
    end
  end
end
