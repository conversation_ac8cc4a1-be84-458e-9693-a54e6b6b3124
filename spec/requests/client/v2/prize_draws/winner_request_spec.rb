require "rails_helper"

RSpec.describe Client::V2::PrizeDraws::WinnerController, type: :request do
  let(:json_parse_response_body) { JSON.parse(response.body) }

  describe "GET /client/v2/businesses/:business_identifier/prize_draws/:id/winner" do
    let!(:business) { create(:business) }
    let!(:client_employee) { create(:client_employee, :admin_lecupon) }
    let(:headers) do
      {
        "X-ClientEmployee-Email": client_employee.email,
        "X-ClientEmployee-Token": client_employee.authentication_token,
        "Tenant-id": business.cnpj
      }
    end

    let(:winner_serializer_keys) do
      %w[
        address_city
        address_complement
        address_neighborhood
        address_number
        address_state
        address_street
        address_zip_code
        drawn_number
        email
        full_name
        phone
      ]
    end

    context "when the prize draw has a winner" do
      it "" do
        prize_draw = create_prize_draw_with_winner(winner_lucky_number: "76543", business:)

        path = [
          "/client/v2/businesses/#{business.cnpj}/prize_draws/#{prize_draw.id}/winner",
          "/client/v2/prize_draws/#{prize_draw.id}/winner"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:ok)
        expect(json_parse_response_body.keys).to match_array(winner_serializer_keys)
        expect(json_parse_response_body["drawn_number"]).to eq("76543")
      end
    end

    context "when the prize draw does not have a winner" do
      it "" do
        prize_draw = create(
          :prize_draw,
          initial_state: :published,
          business:
        )
        prize_draw.transition_to!(:finished)

        path = [
          "/client/v2/businesses/#{business.cnpj}/prize_draws/#{prize_draw.id}/winner",
          "/client/v2/prize_draws/#{prize_draw.id}/winner"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:not_found)
      end
    end

    context "when prize draw is from another business" do
      let!(:business) { create(:business) }
      let!(:client_employee) { create(:client_employee, :admin_lecupon) }
      let(:headers) do
        {
          "X-ClientEmployee-Email": client_employee.email,
          "X-ClientEmployee-Token": client_employee.authentication_token,
          "Tenant-id": business.cnpj
        }
      end

      specify do
        another_business = create(:business)
        prize_draw = create_prize_draw_with_winner(winner_lucky_number: "76543", business: another_business)

        path = [
          "/client/v2/businesses/#{business.cnpj}/prize_draws/#{prize_draw.id}/winner",
          "/client/v2/prize_draws/#{prize_draw.id}/winner"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:not_found)
      end
    end

    def create_prize_draw_with_winner(winner_lucky_number:, business:)
      prize_draw = create(
        :prize_draw,
        initial_state: :published,
        business:
      )
      user = create(:user, business: prize_draw.business)
      allow(prize_draw).to receive(:drawn_number).and_return(winner_lucky_number)
      allow(PrizeDraw::Generate::LuckyNumber).to receive(:call).and_return(winner_lucky_number)
      create(:prize_draw_entries, prize_draw:, user:)

      PrizeDraw::Run.new(prize_draw:).call

      prize_draw
    end
  end
end
