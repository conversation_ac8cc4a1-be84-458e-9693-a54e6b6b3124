require "rails_helper"
require "sidekiq/testing"

RSpec.describe Client::V2::PrizeDrawsController, type: :request do
  let!(:business) { create(:business) }
  let!(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  let(:json_parse_response_body) { JSON.parse(response.body) }

  describe "#index" do
    let(:prize_draw_serializer_keys) do
      %w[
        id
        product_name
        code
        caixa_identifier
        ends_at
        run_at
        starts_at
        status
        winner_found
      ]
    end

    it "returns a successful response" do
      create(:prize_draw, run_at: 2.months.from_now, business:)
      prize_draw_two = create(:prize_draw, sefaz_registration: FFaker::Number.number(digits: 10), business:)
      prize_draw_two.transition_to(:published)

      path = [
        "/client/v2/businesses/#{business.cnpj}/prize_draws",
        "/client/v2/prize_draws"
      ].sample
      get(path, headers:)

      expect(response).to have_http_status(:ok)
      expect(json_parse_response_body.count).to eq(2)
      expect(json_parse_response_body.map(&:keys)).to all(match_array(prize_draw_serializer_keys))
      expect(json_parse_response_body.first["id"]).to eq(prize_draw_two.id)
    end

    context "when has a finished prize draw with a winner" do
      it "returns a successful response" do
        create(:prize_draw, business:)
        create_prize_draw_with_winner(business:)
        create_prize_draw_without_winner(business:)

        path = [
          "/client/v2/businesses/#{business.cnpj}/prize_draws",
          "/client/v2/prize_draws"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:ok)
        expect(json_parse_response_body.count).to eq(3)
        expect(json_parse_response_body.map { _1["winner_found"] }).to contain_exactly(true, false, false)
      end
    end

    context "when prize draw is from another business" do
      it "is expected not returns it" do
        another_business = create(:business)
        create_prize_draw_with_winner(business: another_business)

        path = [
          "/client/v2/businesses/#{business.cnpj}/prize_draws",
          "/client/v2/prize_draws"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:ok)
        expect(json_parse_response_body.count).to eq(0)
      end
    end

    context "with query params status" do
      it "expect returns only draft prize draw" do
        create(:prize_draw, business:)
        prize_draw_two = create(
          :prize_draw,
          business:
        )
        prize_draw_two.transition_to(:published)

        params = {status: "draft"}
        path = [
          "/client/v2/businesses/#{business.cnpj}/prize_draws",
          "/client/v2/prize_draws"
        ].sample
        get(path, params:, headers:)

        expect(response).to have_http_status(:ok)
        expect(json_parse_response_body.count).to eq(1)
      end
    end

    context "with page param" do
      it "expect returns empty" do
        create(:prize_draw, business:)

        params = {page: "2"}
        path = [
          "/client/v2/businesses/#{business.cnpj}/prize_draws",
          "/client/v2/prize_draws"
        ].sample
        get(path, params:, headers:)

        expect(response).to have_http_status(:ok)
        expect(json_parse_response_body).to be_empty
      end
    end

    def create_prize_draw_with_winner(business:)
      prize_draw = create(
        :prize_draw,
        initial_state: :published,
        business:
      )
      user = create(:user, business: prize_draw.business)
      entry = create(:prize_draw_entries, prize_draw:, user:)
      allow(prize_draw).to receive(:drawn_number).and_return(entry.lucky_number)
      PrizeDraw::Run.new(prize_draw:).call

      prize_draw
    end

    def create_prize_draw_without_winner(business:)
      prize_draw = create(
        :prize_draw,
        initial_state: :published,
        business:
      )
      user = create(:user, business: prize_draw.business)
      create(:prize_draw_entries, prize_draw:, user:)

      prize_draw.transition_to!(:finished)
      prize_draw
    end
  end
end
