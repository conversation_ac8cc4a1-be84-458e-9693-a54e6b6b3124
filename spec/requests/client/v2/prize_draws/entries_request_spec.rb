require "rails_helper"

RSpec.describe Client::V2::PrizeDraws::EntriesController, type: :request do
  let!(:business) { create(:business) }
  let!(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  let(:json_parse_response_body) { JSON.parse(response.body) }

  describe "#index" do
    let(:entry_serializer_keys) do
      %w[id acceptance_of_terms address_city address_complement address_neighborhood address_number
        address_state address_street address_zip_code created_at email full_name phone user]
    end
    let(:user_serializer_keys) do
      %w[id taxpayer_number]
    end

    it "returns a successful response" do
      prize_draw = create_prize_draw_with_entries(business:)

      path = [
        "/client/v2/businesses/#{business.cnpj}/prize_draws/#{prize_draw.id}/entries",
        "/client/v2/prize_draws/#{prize_draw.id}/entries"
      ].sample
      get(path, headers:)

      expect(response).to have_http_status(:ok)
      expect(json_parse_response_body.count).to eq(2)
      expect(json_parse_response_body.map(&:keys)).to all(match_array(entry_serializer_keys))
      expect(json_parse_response_body.map { _1["user"].keys }).to all(match_array(user_serializer_keys))
    end

    context "when prize draw is from another business" do
      it "is expected not returns it" do
        another_business = create(:business)
        prize_draw = create_prize_draw_with_entries(business: another_business)

        path = [
          "/client/v2/businesses/#{business.cnpj}/prize_draws/#{prize_draw.id}/entries",
          "/client/v2/prize_draws/#{prize_draw.id}/entries"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:not_found)
      end
    end

    context "with page param" do
      it "expect returns empty" do
        prize_draw = create_prize_draw_with_entries(business:)

        params = {page: "2"}
        path = [
          "/client/v2/businesses/#{business.cnpj}/prize_draws/#{prize_draw.id}/entries",
          "/client/v2/prize_draws/#{prize_draw.id}/entries"
        ].sample
        get(path, params:, headers:)

        expect(response).to have_http_status(:ok)
        expect(json_parse_response_body).to be_empty
      end
    end

    def create_prize_draw_with_entries(business:, amount: 2)
      prize_draw = create(
        :prize_draw,
        initial_state: :published,
        business:
      )
      create_list(:prize_draw_entries, amount, business:, prize_draw:)

      prize_draw
    end
  end
end
