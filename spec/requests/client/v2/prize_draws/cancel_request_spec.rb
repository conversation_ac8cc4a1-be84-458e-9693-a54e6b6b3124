require "rails_helper"

RSpec.describe Client::V2::PrizeDraws::CancelController, type: :request do
  let(:json_parse_response_body) { JSON.parse(response.body) }

  describe "#update" do
    context "when client_employee is admin_client" do
      let!(:business) { create(:business) }
      let!(:client_employee) { create(:client_employee, :admin_client, businesses: [business]) }
      let(:headers) do
        {
          "X-ClientEmployee-Email": client_employee.email,
          "X-ClientEmployee-Token": client_employee.authentication_token,
          "Tenant-id": business.cnpj
        }
      end

      specify do
        prize_draw = create_prize_draw(status: :published, business:)
        allow(PrizeDraw::Status::Machine).to receive(:successors).and_call_original

        path = [
          "/client/v2/businesses/#{business.cnpj}/prize_draws/#{prize_draw.id}/cancel",
          "/client/v2/prize_draws/#{prize_draw.id}/cancel"
        ].sample
        put(path, headers:)

        expect(response).to have_http_status(:ok)
        expect(json_parse_response_body["message"]).to eq("Sorteio cancelado com sucesso.")
        expect(prize_draw.current_state).to eq("canceled")
      end
    end

    context "when client_employee is admin_lecupon" do
      let!(:business) { create(:business) }
      let!(:client_employee) { create(:client_employee, :admin_lecupon) }
      let(:headers) do
        {
          "X-ClientEmployee-Email": client_employee.email,
          "X-ClientEmployee-Token": client_employee.authentication_token,
          "Tenant-id": business.cnpj
        }
      end

      specify do
        prize_draw = create_prize_draw(status: :published, business:)
        allow(PrizeDraw::Status::Machine).to receive(:successors).and_call_original

        path = [
          "/client/v2/businesses/#{business.cnpj}/prize_draws/#{prize_draw.id}/cancel",
          "/client/v2/prize_draws/#{prize_draw.id}/cancel"
        ].sample
        put(path, headers:)

        expect(response).to have_http_status(:ok)
        expect(json_parse_response_body["message"]).to eq("Sorteio cancelado com sucesso.")
        expect(prize_draw.current_state).to eq("canceled")
      end

      context "when the prize draw is already canceled" do
        it "returns a error response" do
          prize_draw = create_prize_draw(status: :canceled, business:)

          path = [
            "/client/v2/businesses/#{business.cnpj}/prize_draws/#{prize_draw.id}/cancel",
            "/client/v2/prize_draws/#{prize_draw.id}/cancel"
          ].sample
          put(path, headers:)

          expect(response).to have_http_status(:unprocessable_entity)
          expect(json_parse_response_body["error"])
            .to eq("Status Não é possível cancelar o sorteio. Estado atual do sorteio: Cancelado")
        end
      end

      context "when prize draw is from another businnes" do
        specify do
          another_business = create(:business)
          prize_draw = create_prize_draw(status: :draft, business: another_business)

          path = [
            "/client/v2/businesses/#{business.cnpj}/prize_draws/#{prize_draw.id}/cancel",
            "/client/v2/prize_draws/#{prize_draw.id}/cancel"
          ].sample
          put(path, headers:)

          expect(response).to have_http_status(:not_found)
        end
      end
    end

    def create_prize_draw(status:, business:)
      allow(PrizeDraw::Status::Machine).to receive(:successors).and_return({"draft" => ["to_be_published", "published", "finished", "canceled"]})
      create(
        :prize_draw,
        initial_state: status,
        business:
      )
    end
  end
end
