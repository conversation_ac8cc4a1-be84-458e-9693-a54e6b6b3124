require "rails_helper"
require "sidekiq/testing"

RSpec.describe Client::V2::PrizeDraws::PublishController, type: :request do
  let(:json_parse_response_body) { JSON.parse(response.body) }

  describe "#update" do
    context "when client_employee is admin_client" do
      let!(:business) { create(:business) }
      let!(:client_employee) { create(:client_employee, :admin_client, businesses: [business]) }
      let(:headers) do
        {
          "X-ClientEmployee-Email": client_employee.email,
          "X-ClientEmployee-Token": client_employee.authentication_token,
          "Tenant-id": business.cnpj
        }
      end

      specify do
        prize_draw = create(:prize_draw, business:)

        path = [
          "/client/v2/businesses/#{business.cnpj}/prize_draws/#{prize_draw.id}/publish",
          "/client/v2/prize_draws/#{prize_draw.id}/publish"
        ].sample
        put(path, headers:)

        expect(response).to have_http_status(:forbidden)
      end
    end

    context "when client_employee is admin_lecupon" do
      let!(:business) { create(:business) }
      let!(:client_employee) { create(:client_employee, :admin_lecupon) }
      let(:headers) do
        {
          "X-ClientEmployee-Email": client_employee.email,
          "X-ClientEmployee-Token": client_employee.authentication_token,
          "Tenant-id": business.cnpj
        }
      end

      context "when the prize draw is draft" do
        it "returns a successful response" do
          prize_draw = create_prize_draw(business:)

          path = [
            "/client/v2/businesses/#{business.cnpj}/prize_draws/#{prize_draw.id}/publish",
            "/client/v2/prize_draws/#{prize_draw.id}/publish"
          ].sample
          put(path, headers:)

          expect(response).to have_http_status(:ok)
          expect(json_parse_response_body["message"]).to eq("Publicação agendada com sucesso para: #{I18n.l(prize_draw.starts_at, format: :long)}")
        end
      end

      context "when the prize draw is already published" do
        it "returns a error response" do
          prize_draw = create_prize_draw(business:)
          prize_draw.transition_to(:published)

          path = [
            "/client/v2/businesses/#{business.cnpj}/prize_draws/#{prize_draw.id}/publish",
            "/client/v2/prize_draws/#{prize_draw.id}/publish"
          ].sample
          put(path, headers:)

          expect(response).to have_http_status(:unprocessable_entity)
          expect(json_parse_response_body["error"])
            .to eq("Status Não é possível agendar a publicação do sorteio. Estado atual do sorteio: Publicado")
        end
      end

      context "when the prize_draw doesn,t have required fields" do
        it do
          prize_draw = create(
            :prize_draw,
            caixa_identifier: nil,
            rules: nil,
            sefaz_registration: nil,
            business:
          )

          path = [
            "/client/v2/businesses/#{business.cnpj}/prize_draws/#{prize_draw.id}/publish",
            "/client/v2/prize_draws/#{prize_draw.id}/publish"
          ].sample
          put(path, headers:)

          expect(response).to have_http_status(:unprocessable_entity)
          expect(json_parse_response_body["error"])
            .to eq("numero concurso da caixa não pode ficar em branco, numero do registro no sefaz não pode ficar em branco, regulamento do sorteio não pode ficar em branco")
        end
      end

      context "when prize draw is from another businnes" do
        let!(:business) { create(:business) }
        let!(:client_employee) { create(:client_employee, :admin_lecupon) }
        let(:headers) do
          {
            "X-ClientEmployee-Email": client_employee.email,
            "X-ClientEmployee-Token": client_employee.authentication_token,
            "Tenant-id": business.cnpj
          }
        end

        specify do
          another_business = create(:business)
          prize_draw = create_prize_draw(business: another_business)

          path = [
            "/client/v2/businesses/#{business.cnpj}/prize_draws/#{prize_draw.id}/publish",
            "/client/v2/prize_draws/#{prize_draw.id}/publish"
          ].sample
          put(path, headers:)

          expect(response).to have_http_status(:not_found)
        end
      end

      def create_prize_draw(business:)
        create(
          :prize_draw,
          business:
        )
      end
    end
  end
end
