require "rails_helper"

RSpec.describe Client::V2::PrizeDrawsController, type: :request do
  let!(:business) { create(:business) }
  let!(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  let(:json_parse_response_body) { JSON.parse(response.body) }

  describe "#show" do
    let(:prize_draw_serializer_keys) do
      %w[
        id
        product_image
        product_name
        code
        caixa_identifier
        ends_at
        rules
        run_at
        sefaz_registration
        starts_at
        status
        total_entries
      ]
    end

    context "when prize_draw is draft" do
      specify do
        prize_draw = create_prize_draw(status: :draft, business:)

        path = [
          "/client/v2/businesses/#{business.cnpj}/prize_draws/#{prize_draw.id}",
          "/client/v2/prize_draws/#{prize_draw.id}"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:ok)
        expect(json_parse_response_body.keys).to match_array(prize_draw_serializer_keys)
      end
    end

    context "when prize_draw is to_be_published" do
      specify do
        prize_draw = create_prize_draw(status: :to_be_published, business:)

        path = [
          "/client/v2/businesses/#{business.cnpj}/prize_draws/#{prize_draw.id}",
          "/client/v2/prize_draws/#{prize_draw.id}"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:ok)
      end
    end

    context "when prize_draw is published" do
      specify do
        prize_draw = create_prize_draw(status: :published, business:)
        create(:prize_draw_entries, lucky_number: "98765", prize_draw:, business: prize_draw.business)

        path = [
          "/client/v2/businesses/#{business.cnpj}/prize_draws/#{prize_draw.id}",
          "/client/v2/prize_draws/#{prize_draw.id}"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:ok)
        expect(json_parse_response_body["total_entries"]).to eq(1)
      end
    end

    context "when prize_draw is finished" do
      specify do
        prize_draw = create_prize_draw(status: :finished, business:)

        path = [
          "/client/v2/businesses/#{business.cnpj}/prize_draws/#{prize_draw.id}",
          "/client/v2/prize_draws/#{prize_draw.id}"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:ok)
      end
    end

    context "when prize_draw is canceled" do
      specify do
        prize_draw = create_prize_draw(status: :canceled, business:)

        path = [
          "/client/v2/businesses/#{business.cnpj}/prize_draws/#{prize_draw.id}",
          "/client/v2/prize_draws/#{prize_draw.id}"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:ok)
      end
    end

    context "when prize draw is from another business" do
      specify do
        another_business = create(:business)
        prize_draw = create_prize_draw(status: :draft, business: another_business)

        path = [
          "/client/v2/businesses/#{business.cnpj}/prize_draws/#{prize_draw.id}",
          "/client/v2/prize_draws/#{prize_draw.id}"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:not_found)
      end
    end

    def create_prize_draw(status:, business:)
      allow(PrizeDraw::Status::Machine).to receive(:successors).and_return({"draft" => ["to_be_published", "published", "finished", "canceled"]})
      create(
        :prize_draw,
        initial_state: status,
        business:
      )
    end
  end
end
