require "rails_helper"
require "support/shared_examples/client_employee_request_shared_examples"

RSpec.describe Client::V2::IxcContractsController, type: :request do
  let(:business) { create(:business, :ixc_business) }

  let(:valid_attributes) {
    {
      name: "Plano X",
      external_id: "70",
      ixc_token_id: business.ixc_token.id
    }
  }

  let(:invalid_attributes) {
    {
      name: "Plano X",
      external_id: nil,
      ixc_token_id: business.ixc_token.id
    }
  }

  let(:response_hash) { JSON.parse(response.body) }
  let(:serialized_keys) { %w[id name external_id] }

  let!(:client_employee) { create(:client_employee, businesses: [business]) }

  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  describe "#index" do
    let!(:ixc_contracts) { create_list(:ixc_contract, 2, ixc_token: business.ixc_token) }

    it "renders a successful response" do
      path = [
        "/client/v2/businesses/#{business.cnpj}/ixc_contracts",
        "/client/v2/ixc_contracts"
      ].sample
      get(path, params: {cnpj: business.cnpj}, headers:)

      expect(response_hash.map(&:keys)).to all(match_array(serialized_keys))
      expect(response).to be_successful
      expect(JSON.parse(response.body).pluck("external_id")).to match_array(ixc_contracts.pluck(:external_id))
    end

    context "without client employee authorization" do
      before do
        path = [
          "/client/v2/businesses/#{business.cnpj}/ixc_contracts",
          "/client/v2/ixc_contracts"
        ].sample
        get path
      end

      it_behaves_like "unauthorized client_employee"
    end

    context "when business unrelated to the current user" do
      let(:client_employee) { create(:client_employee, :admin_client) }

      it "returns not found" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/ixc_contracts",
          "/client/v2/ixc_contracts"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:not_found)
        expect(response_hash["error"]).to eq("Business não encontrado")
      end
    end
  end

  describe "#show" do
    let!(:ixc_contract) { create(:ixc_contract, ixc_token: business.ixc_token) }

    it "renders a successful response" do
      path = [
        "/client/v2/businesses/#{business.cnpj}/ixc_contracts/#{ixc_contract.id}",
        "/client/v2/ixc_contracts/#{ixc_contract.id}"
      ].sample
      get(path, params: {cnpj: business.cnpj}, headers:)

      expect(response_hash.keys).to match_array(serialized_keys)
      expect(response).to be_successful
      expect(JSON.parse(response.body).dig("external_id")).to eq(ixc_contract.external_id)
    end

    context "without client employee authorization" do
      it "renders unauthorized" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/ixc_contracts/#{ixc_contract.id}",
          "/client/v2/ixc_contracts/#{ixc_contract.id}"
        ].sample
        get path

        expect(response).to be_unauthorized
      end
    end

    context "with unrelated client employee authorization" do
      let(:business_two) { create(:business) }

      let!(:client_employee) { create(:client_employee, businesses: [business_two]) }

      it "renders not_found" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/ixc_contracts/#{ixc_contract.id}",
          "/client/v2/ixc_contracts/#{ixc_contract.id}"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:not_found)
        expect(response_hash["error"]).to eq("Business não encontrado")
      end
    end
  end

  describe "#create" do
    let(:business) { create(:business, :ixc_business) }

    context "with valid parameters" do
      it "creates a new IxcToken" do
        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/ixc_contracts",
            "/client/v2/ixc_contracts"
          ].sample
          post(path, params: {ixc_contract: valid_attributes}, headers:)

          expect(response_hash.keys).to match_array(serialized_keys)
          expect(response).to be_successful
          business.reload
        end.to change(IxcContract, :count).by(1).and change(business.ixc_contracts, :count).by(1)
      end
    end

    context "with invalid parameters" do
      it "does not create a new IxcToken" do
        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/ixc_contracts",
            "/client/v2/ixc_contracts"
          ].sample
          post path, params: {ixc_contract: invalid_attributes}, headers:
        end.to change(IxcContract, :count).by(0)
      end

      it "renders a error" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/ixc_contracts",
          "/client/v2/ixc_contracts"
        ].sample
        post(path, params: {ixc_contract: invalid_attributes}, headers:)

        expect(response).to be_unprocessable
      end
    end

    context "without client employee authorization" do
      it "renders unauthorized" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/ixc_contracts",
          "/client/v2/ixc_contracts"
        ].sample
        post path, params: {ixc_contract: valid_attributes}

        expect(response).to be_unauthorized
      end
    end

    context "with business unrelated to client employee" do
      context "when is lecupon admin" do
        let!(:client_employee) { create(:client_employee, :admin_lecupon) }

        it "creates a new IxcToken" do
          expect do
            path = [
              "/client/v2/businesses/#{business.cnpj}/ixc_contracts",
              "/client/v2/ixc_contracts"
            ].sample
            post path, params: {ixc_contract: valid_attributes}, headers:
          end.to change(IxcContract, :count).by(1)
        end
      end

      context "when is not lecupon admin" do
        let(:business_two) { create(:business) }

        let!(:client_employee) { create(:client_employee, businesses: [business_two]) }

        it "renders forbidden" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/ixc_contracts",
            "/client/v2/ixc_contracts"
          ].sample
          post(path, params: {ixc_contract: valid_attributes}, headers:)

          expect(response).to have_http_status(:not_found)
          expect(response_hash["error"]).to eq("Business não encontrado")
        end
      end
    end
  end

  describe "#update" do
    let!(:ixc_contract) { create(:ixc_contract, ixc_token: business.ixc_token) }

    let(:new_attributes) do
      {name: "Plano Z"}
    end

    context "with valid parameters" do
      it "updates the requested ixc_contract" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/ixc_contracts/#{ixc_contract.id}",
          "/client/v2/ixc_contracts/#{ixc_contract.id}"
        ].sample
        patch(path, params: {ixc_contract: new_attributes}, headers:)

        expect(response_hash.keys).to match_array(serialized_keys)
        ixc_contract.reload
        expect(ixc_contract.name).to eq("Plano Z")
      end
    end

    context "with invalid parameters" do
      it "renders error" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/ixc_contracts/#{ixc_contract.id}",
          "/client/v2/ixc_contracts/#{ixc_contract.id}"
        ].sample
        patch(path, params: {ixc_contract: invalid_attributes}, headers:)

        expect(response).to be_unprocessable
      end
    end

    context "without client employee authorization" do
      it "renders unauthorized" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/ixc_contracts/#{ixc_contract.id}",
          "/client/v2/ixc_contracts/#{ixc_contract.id}"
        ].sample
        patch path, params: {ixc_contract: new_attributes}

        expect(response).to be_unauthorized
      end
    end

    context "with business_id unrelated to client employee" do
      context "when is lecupon admin" do
        let!(:client_employee) { create(:client_employee, :admin_lecupon) }

        it "updates the requested ixc_contract" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/ixc_contracts/#{ixc_contract.id}",
            "/client/v2/ixc_contracts/#{ixc_contract.id}"
          ].sample
          patch(path, params: {ixc_contract: new_attributes}, headers:)

          ixc_contract.reload
          expect(ixc_contract.name).to eq("Plano Z")
        end
      end

      context "when is not lecupon admin" do
        let(:business_two) { create(:business) }

        let!(:client_employee) { create(:client_employee, businesses: [business_two]) }

        it "renders not_found" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/ixc_contracts/#{ixc_contract.id}",
            "/client/v2/ixc_contracts/#{ixc_contract.id}"
          ].sample
          patch(path, params: {ixc_contract: new_attributes}, headers:)

          expect(response).to have_http_status(:not_found)
          expect(response_hash["error"]).to eq("Business não encontrado")
        end
      end
    end
  end

  describe "#destroy" do
    let!(:ixc_contract) { create(:ixc_contract, ixc_token: business.ixc_token) }

    it "destroys the requested ixc_contract" do
      expect do
        path = [
          "/client/v2/businesses/#{business.cnpj}/ixc_contracts/#{ixc_contract.id}",
          "/client/v2/ixc_contracts/#{ixc_contract.id}"
        ].sample
        delete(path, headers:)

        expect(response).to be_no_content
      end.to change(IxcContract, :count).by(-1)
    end

    context "without client employee authorization" do
      it "renders unauthorized" do
        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/ixc_contracts/#{ixc_contract.id}",
            "/client/v2/ixc_contracts/#{ixc_contract.id}"
          ].sample
          delete path

          expect(response).to be_unauthorized
        end.not_to change(IxcContract, :count)
      end
    end

    context "with business unrelated to client employee" do
      context "when is lecupon admin" do
        let!(:client_employee) { create(:client_employee, :admin_lecupon) }

        it "destroys the requested ixc_contract" do
          expect do
            path = [
              "/client/v2/businesses/#{business.cnpj}/ixc_contracts/#{ixc_contract.id}",
              "/client/v2/ixc_contracts/#{ixc_contract.id}"
            ].sample
            delete(path, headers:)

            expect(response).to be_no_content
          end.to change(IxcContract, :count).by(-1)
        end
      end

      context "when is not lecupon admin" do
        let(:business_two) { create(:business) }

        let!(:client_employee) { create(:client_employee, businesses: [business_two]) }

        it "renders not_found" do
          expect do
            path = [
              "/client/v2/businesses/#{business.cnpj}/ixc_contracts/#{ixc_contract.id}",
              "/client/v2/ixc_contracts/#{ixc_contract.id}"
            ].sample
            delete(path, headers:)

            expect(response).to have_http_status(:not_found)
            expect(response_hash["error"]).to eq("Business não encontrado")
          end.not_to change(IxcContract, :count)
        end
      end
    end
  end
end
