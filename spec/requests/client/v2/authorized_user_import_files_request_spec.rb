require "rails_helper"
require "sidekiq/testing"

RSpec.describe Client::V2::AuthorizedUserImportFilesController, type: :request do
  let(:business) { create(:business) }
  let(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end
  let(:response_hash) { JSON.parse(response.body) }
  let(:path) do
    [
      "/client/v2/businesses/#{business.cnpj}/authorized_users/file_imports",
      "/client/v2/authorized_users/file_imports"
    ].sample
  end
  let(:serialized_keys) { %w[id total_amount import_count error_count process_count filename invalid_list_file_url] }

  describe "#create" do
    let(:auth_user_list_csv) do
      tempfile = Tempfile.new(["auth_user_test", ".csv"], "tmp")
      CSV.open(tempfile, "w", headers: true, col_sep: ",") do |csv|
        csv << ["name", "cpf", "email", "custom_field_1", "user_tags"]
        csv << [FFaker::Name.name, CPF.new(FFaker::IdentificationBR.cpf).stripped, FFaker::Internet.email, "metadata", "Tag 1;Tag 2"]
      end

      tempfile
    end

    let(:params) do
      {
        authorized_users_csv: Rack::Test::UploadedFile.new(auth_user_list_csv)
      }
    end

    context "when authorized_users_csv param is a valid csv" do
      context "when with invalid colums on csv" do
        let(:auth_user_list_csv) do
          tempfile = Tempfile.new(["auth_user_test", ".csv"], "tmp")
          CSV.open(tempfile, "w", headers: true, col_sep: ",") do |csv|
            csv << ["name", "cpf", "email", "invalid_field"]
            csv << [FFaker::Name.name, "0", FFaker::Internet.email, 0]
          end

          tempfile
        end

        let(:params) do
          {
            authorized_users_csv: Rack::Test::UploadedFile.new(auth_user_list_csv)
          }
        end

        it "returns unprocessable" do
          post(path, headers:, params:)

          expect(response).to be_unprocessable
          expect(response_hash["error"]).to include("Colunas inválidas:")
        end

        it "must not persist import file" do
          post(path, headers:, params:)
          expect(response).to be_unprocessable
          expect(ImportTempFile.count).to eq(0)
          expect(AuthorizedUserImportFile.count).to eq(0)
        end
      end

      context "when all csv columns are valid" do
        it "returns successful" do
          Sidekiq::Testing.inline! do
            post(path, headers:, params:)

            expect(response).to have_http_status(:ok)
            expect(response_hash.keys).to match_array(serialized_keys)

            imported_authorized_user = AuthorizedUser.first
            expect(imported_authorized_user.authorized_user_group.slug).to eq("tag-1")
          end
        end

        it "must call AuthorizedUserImportFile process file worker" do
          Sidekiq::Testing.inline! do
            expect(AuthorizedUserImportFile::ProcessFileWorker).to receive(:perform_async)
            post path, headers:, params:
          end
        end
      end
    end

    context "when authorized_users_csv param is not a valid csv" do
      let(:params) do
        {
          authorized_users_csv: Rack::Test::UploadedFile.new(Rails.root.join("spec/fixtures/samplefile.png"))

        }
      end

      it "must render unprocessable" do
        post(path, headers:, params:)

        expect(response).to be_unprocessable
        expect(response_hash["error"]).to include("Arquivo com formato inválido")
      end

      it "must not persist import file" do
        post(path, headers:, params:)
        expect(response).to be_unprocessable
        expect(ImportTempFile.count).to eq(0)
        expect(AuthorizedUserImportFile.count).to eq(0)
      end
    end
  end

  describe "#index" do
    let!(:auth_user_list_params) do
      (1..4).collect do |_user|
        {
          name: FFaker::Name.name,
          cpf: CPF.new(FFaker::IdentificationBR.cpf).stripped,
          email: FFaker::Internet.email,
          custom_field_1: "metadata"
        }
      end
    end

    let(:auth_user_list_csv) do
      tempfile = Tempfile.new(["auth_user_test", ".csv"], "tmp")
      CSV.open(tempfile, "w", headers: true, col_sep: ",") do |csv|
        csv << ["name", "cpf", "email", "custom_field_1"]
        auth_user_list_params.each { |params| csv << [params[:name], params[:cpf], params[:email], params[:custom_field_1]] }
      end

      tempfile
    end
    let(:another_business) { create(:business) }

    let(:import_temp_file) { create(:import_temp_file, file: auth_user_list_csv) }
    let!(:authorized_user_import_file) { create(:authorized_user_import_file, import_temp_file:, business:) }
    let!(:another_authorized_user_import_file) { create(:authorized_user_import_file, import_temp_file:, business: another_business) }

    it "must return all business auth_user import files" do
      get(path, headers:)

      expect(response_hash.count).to eq(1)
      expect(response_hash.map(&:keys)).to all(match_array(serialized_keys))
    end
  end
  describe "#show" do
    let!(:auth_user_list_params) do
      (1..4).collect do |_user|
        {
          name: FFaker::Name.name,
          cpf: CPF.new(FFaker::IdentificationBR.cpf).stripped,
          email: FFaker::Internet.email,
          custom_field_1: "metadata"
        }
      end
    end

    let(:auth_user_list_csv) do
      tempfile = Tempfile.new(["auth_user_test", ".csv"], "tmp")
      CSV.open(tempfile, "w", headers: true, col_sep: ",") do |csv|
        csv << ["name", "cpf", "email", "custom_field_1"]
        auth_user_list_params.each { |params| csv << [params[:name], params[:cpf], params[:email], params[:custom_field_1]] }
      end

      tempfile
    end
    let(:another_business) { create(:business) }

    let(:import_temp_file) { create(:import_temp_file, file: auth_user_list_csv) }
    let!(:authorized_user_import_file) { create(:authorized_user_import_file, import_temp_file:, business:) }

    it "must return all business auth_user import files" do
      get(path + "/#{authorized_user_import_file.id}", headers:)

      expect(response_hash.keys).to match_array(serialized_keys)
      expected_filename = "Planilha de usuário - #{import_temp_file.created_at.strftime("%d/%m/%Y %H:%M:%S")}"
      expect(response_hash["filename"]).to include(expected_filename)
    end
  end
end
