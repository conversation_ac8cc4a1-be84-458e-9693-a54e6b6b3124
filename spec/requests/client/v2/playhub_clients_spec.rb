require "rails_helper"

RSpec.describe Client::V2::PlayhubClientsController, type: :request do
  let(:business) { create(:business) }
  let(:authorized_user_group) { create(:authorized_user_group, business:) }
  let!(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  let(:valid_attributes) do
    {
      client_id: "client_id",
      authorized_user_group_id: authorized_user_group.id,
      isp_id: "VE2"
    }
  end

  let(:invalid_attributes) {
    {
      client_id: nil,
      authorized_user_group_id: authorized_user_group.id,
      isp_id: "VE2"
    }
  }
  let(:serialized_keys) { %w[id client_id authorized_user_group isp_id created_at] }

  describe "#show" do
    it "renders unauthorized with invalid credentials" do
      path = [
        "/client/v2/businesses/#{business.cnpj}/playhub_clients",
        "/client/v2/playhub_clients"
      ].sample
      get path

      expect(response).to be_unauthorized
    end

    context "when a token is registered" do
      let!(:playhub_client) { create(:playhub_client, business:) }

      it "renders a successful response" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/playhub_clients",
          "/client/v2/playhub_clients"
        ].sample
        get path, params: {cnpj: business.cnpj}, headers:, as: :json

        response_hash = JSON.parse(response.body)
        expect(response_hash.keys).to match_array(serialized_keys)
        expect(response).to be_successful
      end
    end

    context "when there is no token" do
      it "returns not found" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/playhub_clients",
          "/client/v2/playhub_clients"
        ].sample
        get path, params: {cnpj: business.cnpj}, headers:, as: :json

        expect(response).to have_http_status(:not_found)
      end
    end
  end

  describe "#create" do
    it "creates a new token" do
      path = [
        "/client/v2/businesses/#{business.cnpj}/playhub_clients",
        "/client/v2/playhub_clients"
      ].sample
      post path, params: valid_attributes, headers:, as: :json

      json_response = JSON.parse(response.body)
      expect(response).to have_http_status(:created)
      expect(json_response.keys).to match_array(serialized_keys)
    end

    it "fails to create token with invalid data" do
      path = [
        "/client/v2/businesses/#{business.cnpj}/playhub_clients",
        "/client/v2/playhub_clients"
      ].sample
      post path, params: invalid_attributes, headers:, as: :json

      json_response = JSON.parse(response.body)
      expect(response).to have_http_status(:unprocessable_entity)
      expect(json_response["error"]).to include("não pode ficar em branco")
    end
  end

  describe "#update" do
    let!(:playhub_client) { create(:playhub_client, business:) }

    context "with valid parameters" do
      it "updates a playhub client" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/playhub_clients",
          "/client/v2/playhub_clients"
        ].sample
        patch path, params: {client_id: "new_client_id"}, headers:, as: :json

        json_response = JSON.parse(response.body)
        expect(response).to have_http_status(:success)
        expect(json_response["client_id"]).to eq("new_client_id")
      end
    end
  end

  describe "#destroy" do
    let!(:playhub_client) { create(:playhub_client, business:) }

    it "deletes the playhub client" do
      path = [
        "/client/v2/businesses/#{business.cnpj}/playhub_clients",
        "/client/v2/playhub_clients"
      ].sample
      delete path, headers:, as: :json

      expect(response).to have_http_status(:no_content)
    end
  end
end
