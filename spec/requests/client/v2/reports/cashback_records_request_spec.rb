# frozen_string_literal: true

require "rails_helper"

RSpec.describe Client::V2::Reports::CashbackRecordsController, type: :request do
  let!(:business) { create(:business, :oab, :with_cashback, cnpj: "03814381000130") }
  let!(:business_two) { create(:business, :publicar, :with_cashback, cnpj: "32839587000113") }
  let!(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end
  let(:response_hash) { JSON.parse(response.body) }

  describe "#index" do
    let!(:user_one) { create(:user, business:, name: "Ciclan<PERSON>") }
    let!(:user_two) { create(:user, business:, name: "<PERSON><PERSON><PERSON>") }
    let!(:user_three) { create(:user, business:, name: "<PERSON><PERSON>") }
    let!(:user_four) { create(:user, business: business_two, name: "<PERSON>") }

    let!(:order_one) { create(:order, :online, user: user_one) }
    let!(:cashback_one) do
      create(:cashback_record,
        order: order_one,
        user: user_one,
        transaction_status: Enums::CashbackRecordStatus.approved_statuses.sample,
        order_amount: 107.5,
        total_commission_amount: 10.75,
        business_spread_amount: 1.08,
        cashback_amount: 9.67)
    end
    let!(:order_two) { create(:order, :online, user: user_one) }
    let!(:cashback_two) do
      create(:cashback_record,
        order: order_two,
        user: user_one,
        transaction_status: Enums::CashbackRecordStatus::PENDING,
        order_amount: 127.5,
        total_commission_amount: 12.75,
        business_spread_amount: 1.28,
        cashback_amount: 11.47)
    end
    let!(:order_three) { create(:order, :online, user: user_two) }
    let!(:cashback_three) do
      create(:cashback_record,
        order: order_three,
        user: user_two,
        transaction_status: Enums::CashbackRecordStatus::AVAILABLE,
        order_amount: 100,
        total_commission_amount: 10,
        business_spread_amount: 1,
        cashback_amount: 9)
    end
    let!(:order_four) { create(:order, :online, user: user_two) }
    let!(:cashback_four) do
      create(:cashback_record,
        order: order_four,
        user: user_two,
        transaction_status: Enums::CashbackRecordStatus::CANCELED,
        order_amount: 1207.5,
        total_commission_amount: 120.75,
        business_spread_amount: 12.08,
        cashback_amount: 108.67)
    end
    let!(:order_five) { create(:order, :online, user: user_three) }
    let!(:cashback_five) do
      create(:cashback_record,
        order: order_five,
        user: user_three,
        transaction_status: ["approved", "available_lecupon", "in_transfer_lecupon"].sample,
        order_amount: 50,
        total_commission_amount: 5,
        business_spread_amount: 0.5,
        cashback_amount: 4.5)
    end
    let!(:order_six) { create(:order, :online, user: user_four) }
    let!(:cashback_six) do
      create(:cashback_record,
        order: order_six,
        user: user_four,
        transaction_status: Enums::CashbackRecordStatus::AVAILABLE,
        order_amount: 500,
        total_commission_amount: 50,
        business_spread_amount: 5,
        cashback_amount: 45)
    end

    let(:serializer_keys) do
      %w[user_id user_name pending_amount approved_amount available_amount in_transfer_amount transferred_amount]
    end

    before do
      create(:payout, :in_transfer, user: user_three, user_business: business, total_amount: 900)
      create(:payout, :transferred, user: user_three, user_business: business, total_amount: 180)
      create(:wallet, :cashback, user: user_one, balance: 0)
      create(:wallet, :cashback, user: user_two, balance: 900)
      create(:wallet, :cashback, user: user_three, balance: 0)
      create(:wallet, :cashback, user: user_four, balance: 0)
    end

    it "returns the cashback records grouped by the users of the business, page 1" do
      path = [
        "/client/v2/businesses/#{business.cnpj}/reports/cashback_records",
        "/client/v2/reports/cashback_records"
      ].sample
      get path, headers:, params: {page: 1}

      expect(response).to have_http_status(:ok)
      expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
      expect(response_hash).to eq([
        {
          "user_id" => user_three.id,
          "user_name" => "Beltrano",
          "pending_amount" => 0.0,
          "approved_amount" => 4.5,
          "available_amount" => 0.0,
          "in_transfer_amount" => 900.0,
          "transferred_amount" => 180.0
        },
        {
          "user_id" => user_one.id,
          "user_name" => "Ciclano",
          "pending_amount" => 11.47,
          "approved_amount" => 9.67,
          "available_amount" => 0.0,
          "in_transfer_amount" => 0.0,
          "transferred_amount" => 0.0
        },
        {
          "user_id" => user_two.id,
          "user_name" => "Fulano",
          "pending_amount" => 0.0,
          "approved_amount" => 0.0,
          "available_amount" => 9.0,
          "in_transfer_amount" => 0.0,
          "transferred_amount" => 0.0
        }
      ])
    end

    it "returns cashback records grouped by users of business, except those with business cashback disabled" do
      user_three.cashback_records.update_all(computable: false)

      path = [
        "/client/v2/businesses/#{business.cnpj}/reports/cashback_records",
        "/client/v2/reports/cashback_records"
      ].sample
      get(path, headers:)

      expect(response).to be_ok
      expect(response_hash.map(&:keys)).to all match_array serializer_keys
      expect(response_hash).to eq([
        {
          "user_id" => user_one.id,
          "user_name" => "Ciclano",
          "pending_amount" => 11.47,
          "approved_amount" => 9.67,
          "available_amount" => 0.0,
          "in_transfer_amount" => 0.0,
          "transferred_amount" => 0.0
        },
        {
          "user_id" => user_two.id,
          "user_name" => "Fulano",
          "pending_amount" => 0.0,
          "approved_amount" => 0.0,
          "available_amount" => 9.0,
          "in_transfer_amount" => 0.0,
          "transferred_amount" => 0.0
        }
      ])
    end

    it "returns the cashback records grouped by the users of the business, page 2" do
      path = [
        "/client/v2/businesses/#{business.cnpj}/reports/cashback_records",
        "/client/v2/reports/cashback_records"
      ].sample
      get path, headers:, params: {page: 2}

      expect(response).to have_http_status(:ok)
      expect(response_hash).to be_empty
    end

    context "when cpf param is present" do
      let(:params) { {cpf: user_one.cpf} }

      it "returns only cashback records filtered by user cpf" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/reports/cashback_records",
          "/client/v2/reports/cashback_records"
        ].sample
        get(path, headers:, params:)

        expect(response).to have_http_status(:ok)
        expect(response_hash).to eq([
          {
            "user_id" => user_one.id,
            "user_name" => "Ciclano",
            "pending_amount" => 11.47,
            "approved_amount" => 9.67,
            "available_amount" => 0.0,
            "in_transfer_amount" => 0.0,
            "transferred_amount" => 0.0
          }
        ])
      end
    end

    context "when cnpj is invalid" do
      let(:cnpj) { "000" }
      let(:headers) do
        {
          "X-ClientEmployee-Email": client_employee.email,
          "X-ClientEmployee-Token": client_employee.authentication_token,
          "Tenant-id": cnpj
        }
      end

      it "returns not_found" do
        path = [
          "/client/v2/businesses/#{cnpj}/reports/cashback_records",
          "/client/v2/reports/cashback_records"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:not_found)
        expect(response_hash["error"]).to eq("Business não encontrado")
      end
    end

    context "when business unrelated to the current user" do
      let(:headers) do
        {
          "X-ClientEmployee-Email": client_employee.email,
          "X-ClientEmployee-Token": client_employee.authentication_token,
          "Tenant-id": business_two.cnpj
        }
      end

      it "returns not_found" do
        path = [
          "/client/v2/businesses/#{business_two.cnpj}/reports/cashback_records",
          "/client/v2/reports/cashback_records"
        ].sample
        get path, headers:, params: {page: 1}

        expect(response).to have_http_status(:not_found)
        expect(response_hash["error"]).to eq("Business não encontrado")
      end
    end

    context "when the client employee is admin lecupon" do
      let!(:client_employee) { create(:client_employee, :admin_lecupon) }

      it "returns the cashback records grouped by the users of the business, for any business" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/reports/cashback_records",
          "/client/v2/reports/cashback_records"
        ].sample
        get path, headers:, params: {page: 1}

        expect(response).to have_http_status(:ok)
        expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
        expect(response_hash).to eq([
          {
            "user_id" => user_three.id,
            "user_name" => "Beltrano",
            "pending_amount" => 0.0,
            "approved_amount" => 4.5,
            "available_amount" => 0.0,
            "in_transfer_amount" => 900.0,
            "transferred_amount" => 180.0
          },
          {
            "user_id" => user_one.id,
            "user_name" => "Ciclano",
            "pending_amount" => 11.47,
            "approved_amount" => 9.67,
            "available_amount" => 0.0,
            "in_transfer_amount" => 0.0,
            "transferred_amount" => 0.0
          },
          {
            "user_id" => user_two.id,
            "user_name" => "Fulano",
            "pending_amount" => 0.0,
            "approved_amount" => 0.0,
            "available_amount" => 9.0,
            "in_transfer_amount" => 0.0,
            "transferred_amount" => 0.0
          }
        ])
      end
    end
  end
end
