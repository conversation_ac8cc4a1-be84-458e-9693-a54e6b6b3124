# frozen_string_literal: true

require "rails_helper"

RSpec.describe Client::V2::Reports::UsersController, type: :request do
  let!(:business) do
    create(:business,
      :oab,
      :with_cashback,
      cnpj: "03814381000130",
      user_count: 6,
      authorized_user_count: 7,
      users_with_redeem_count: 3,
      order_count: 2,
      users_with_redeem_last_month_count: 2,
      users_with_redeem_last_week_count: 1,
      amount_saved_total: 60,
      amount_saved_last_month: 30,
      amount_saved_last_week: 10)
  end
  let!(:users_list) { create_list(:authorized_user, 2, :with_user, business:, active: true, telemedicine: true) }
  let!(:authorized_users_list) { create_list(:authorized_user, 3, business:) }
  let(:response_hash) { JSON.parse(response.body) }

  describe "#overall_info" do
    before do
      business.telemedicine_config.update(contracted_beneficiaries: 1000)
    end

    context "when client employee is authorized for current business" do
      let!(:client_employee) { create(:client_employee, :admin_client, businesses: [business]) }
      let(:headers) do
        {
          "X-ClientEmployee-Email": client_employee.email,
          "X-ClientEmployee-Token": client_employee.authentication_token,
          "Tenant-id": business.cnpj
        }
      end

      it "must return info related to all businesses" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/reports/users/overall_info",
          "/client/v2/reports/users/overall_info"
        ].sample
        get(path, headers:)

        expect(response_hash["user_count"]).to eq(6)
        expect(response_hash["authorized_user_count"]).to eq(7)
        expect(response_hash["users_with_redeem_count"]).to eq(3)
        expect(response_hash["order_count"]).to eq(2)
        expect(response_hash["users_with_redeem_percent"]).to eq(50)
        expect(response_hash["users_with_redeem_last_month_percent"]).to eq(34)
        expect(response_hash["users_with_redeem_last_week_percent"]).to eq(17)
        expect(response_hash["amount_saved_total"]).to eq(60)
        expect(response_hash["amount_saved_last_month"]).to eq(30)
        expect(response_hash["amount_saved_last_week"]).to eq(10)
        expect(response_hash["users_elegible_to_telemedicine"]).to eq(2)
        expect(response_hash["telemedicine_contracted_beneficiaries"]).to eq(1000)
        expect(response_hash["telemedicine_plan"]).to eq("Plano Integral")
      end
    end

    context "when business unrelated to the current user" do
      let!(:client_employee) { create(:client_employee, :admin_client) }
      let(:headers) do
        {
          "X-ClientEmployee-Email": client_employee.email,
          "X-ClientEmployee-Token": client_employee.authentication_token,
          "Tenant-id": business.cnpj
        }
      end

      it "returns not found" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/reports/users/overall_info",
          "/client/v2/reports/users/overall_info"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:not_found)
        expect(response_hash["error"]).to eq("Business não encontrado")
      end
    end
  end
end
