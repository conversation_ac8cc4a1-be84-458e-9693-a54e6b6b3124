require "rails_helper"

RSpec.describe Client::V2::<PERSON><PERSON>oftTokensController, type: :request do
  let(:business) { create(:business) }
  let(:authorized_user_group) { create(:authorized_user_group, business:) }
  let!(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  let(:valid_attributes) do
    {
      base_url: "https://business.com",
      authorized_user_group_id: authorized_user_group.id,
      username: "<EMAIL>",
      password: "93S6G$lFb5@M",
      client_id: "5",
      client_secret: "HmiosCqbkQHIaSgaPTsjDizposZxHoUHBroanC1G",
      bundle_code: "55"
    }
  end

  let(:invalid_attributes) {
    {
      base_url: nil,
      username: "<EMAIL>",
      password: "93S6G$lFb5@M",
      client_id: "5",
      client_secret: "HmiosCqbkQHIaSgaPTsjDizposZxHoUHBroanC1G",
      bundle_code: "55"
    }
  }
  let(:serialized_keys) { %w[id base_url username password client_id client_secret bundle_code authorized_user_group use_only_cpf created_at] }

  describe "#show" do
    it "renders unauthorized with invalid credentials" do
      path = [
        "/client/v2/businesses/#{business.cnpj}/hubsoft_tokens",
        "/client/v2/hubsoft_tokens"
      ].sample
      get path, as: :json

      expect(response).to be_unauthorized
    end

    context "when a token is registered" do
      let!(:hubsoft_token) { create(:hubsoft_token, business:) }

      it "renders a successful response" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/hubsoft_tokens",
          "/client/v2/hubsoft_tokens"
        ].sample
        get path, params: {cnpj: business.cnpj}, headers:, as: :json

        response_hash = JSON.parse(response.body)
        expect(response_hash.keys).to match_array(serialized_keys)
        expect(response).to be_successful
      end
    end

    context "when there is no token" do
      it "returns not found" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/hubsoft_tokens",
          "/client/v2/hubsoft_tokens"
        ].sample
        get path, params: {cnpj: business.cnpj}, headers:, as: :json

        expect(response).to have_http_status(:not_found)
      end
    end
  end

  describe "#create" do
    it "creates a new token" do
      path = [
        "/client/v2/businesses/#{business.cnpj}/hubsoft_tokens",
        "/client/v2/hubsoft_tokens"
      ].sample
      post path, params: valid_attributes, headers:, as: :json

      json_response = JSON.parse(response.body)
      expect(response).to have_http_status(:created)
      expect(json_response.keys).to match_array(serialized_keys)
    end

    it "fails to create token with invalid data" do
      path = [
        "/client/v2/businesses/#{business.cnpj}/hubsoft_tokens",
        "/client/v2/hubsoft_tokens"
      ].sample
      post path, params: invalid_attributes, headers:, as: :json

      json_response = JSON.parse(response.body)
      expect(response).to have_http_status(:unprocessable_entity)
      expect(json_response["error"]).to include("não pode ficar em branco")
    end
  end

  describe "#update" do
    let!(:hubsoft_token) { create(:hubsoft_token, business:) }

    context "with valid parameters" do
      it "updates a hubsoft token" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/hubsoft_tokens",
          "/client/v2/hubsoft_tokens"
        ].sample
        patch path, params: {base_url: "https://any_other_url.com/"}, headers:, as: :json

        json_response = JSON.parse(response.body)
        expect(response).to have_http_status(:success)
        expect(json_response["base_url"]).to eq("https://any_other_url.com/")
      end
    end
  end

  describe "#destroy" do
    let!(:hubsoft_token) { create(:hubsoft_token, business:) }

    it "deletes the hubsoft token" do
      path = [
        "/client/v2/businesses/#{business.cnpj}/hubsoft_tokens",
        "/client/v2/hubsoft_tokens"
      ].sample
      delete path, headers:, as: :json

      expect(response).to have_http_status(:no_content)
    end
  end
end
