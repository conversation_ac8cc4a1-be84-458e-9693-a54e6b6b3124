# frozen_string_literal: true

require "rails_helper"
require "action_policy/rspec"

RSpec.describe Client::V2::MailerConfigsController, type: :request do
  describe "#show" do
    let(:business) { create(:business) }
    let(:headers) do
      Devise::JWT::TestHelpers.auth_headers({"Tenant-id": business.cnpj}, admin)
    end
    let(:serialized_keys) { %w[domain_configured dns_configured dns_records email_sender logo] }
    let(:response_hash) { JSON.parse(response.body) }

    context "when admin is authenticated" do
      context "when admin has permission on business" do
        let(:admin) { create(:client_employee, :admin_lecupon) }

        it "must return project_config fields related to dkim" do
          path = [
            "/client/v2/businesses/#{business.id}/mailer_configs",
            "/client/v2/mailer_configs"
          ].sample
          get(path, headers:)

          expect(response).to be_ok
          expect(response_hash.keys).to match_array(serialized_keys)
        end
      end

      context "when admin does not have permission on business" do
        let(:admin) { create(:client_employee, :admin_client) }

        it "returns not found" do
          path = [
            "/client/v2/businesses/#{business.id}/mailer_configs",
            "/client/v2/mailer_configs"
          ].sample
          get(path, headers:)

          expect(response).to have_http_status(:not_found)
          expect(response_hash["error"]).to eq("Business não encontrado")
        end
      end
    end

    context "when admin is not authenticated" do
      it "must render unauthorized" do
        path = [
          "/client/v2/businesses/#{business.id}/mailer_configs",
          "/client/v2/mailer_configs"
        ].sample
        get path

        expect(response).to be_unauthorized
      end
    end
  end
end
