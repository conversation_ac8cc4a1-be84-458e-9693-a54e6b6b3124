require "rails_helper"

RSpec.describe Client::V2::Organizations::Admins::InvitationsController, type: :request do
  let!(:business) { create(:business, name: "<PERSON><PERSON><PERSON><PERSON>") }
  let!(:organization) { create(:organization, name: "<PERSON><PERSON><PERSON>") }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end
  let(:response_hash) { JSON.parse(response.body) }
  let!(:organization_profile) { create(:organization_profile, organization:, business:) }

  describe "#create" do
    let!(:client_employee) { create(:client_employee, :admin_lecupon) }

    context "when inviter have access to business" do
      context "when inviting a new email" do
        let(:params) do
          {client_employee: {email: "<EMAIL>", name: "any name"}}
        end

        it "invite new client employee and renders a successful response" do
          expect do
            path = [
              "/client/v2/businesses/#{business.cnpj}/organizations/#{organization.id}/admins/invitations",
              "/client/v2/organizations/#{organization.id}/admins/invitations"
            ].sample
            post(path, headers:, params:)

            expect(response).to be_successful
          end.to change(ClientEmployee, :count).by(1)
            .and change(AdminOrganization, :count).by(1)

          invited_client_employee = ClientEmployee.last
          new_admin_organization = AdminOrganization.last
          expect(invited_client_employee.email).to eq("<EMAIL>")
          expect(invited_client_employee.name).to eq("any name")
          expect(invited_client_employee.role).to eq("shopkeeper")
          expect(new_admin_organization.client_employee).to eq(invited_client_employee)
          expect(new_admin_organization.organization).to eq(organization)
          expect(new_admin_organization.business).to eq(business)

          expect(AdminOrganizationMailer.deliveries.count).to eq(1)
          mail = AdminOrganizationMailer.deliveries.last
          expect(mail.to).to eq(["<EMAIL>"])
          expect(mail.from).to eq(["<EMAIL>"])
          expect(CGI.unescapeHTML(mail.html_part.body.raw_source))
            .to include(Rails.application.credentials.accept_invite_url)
            .and include(organization.name)
            .and include(business.name)
        end
      end

      context "when inviting an existing email" do
        context "when existing client employee is admin lecupon" do
          let!(:existing_client_employee) { create(:client_employee, role: "admin_lecupon") }
          let(:params) do
            {client_employee: {email: existing_client_employee.email.upcase, name: "any name"}}
          end

          it "must render error" do
            expect do
              path = [
                "/client/v2/businesses/#{business.cnpj}/organizations/#{organization.id}/admins/invitations",
                "/client/v2/organizations/#{organization.id}/admins/invitations"
              ].sample
              post path, headers:, params:
            end.not_to change(ClientEmployee, :count)
            expect(response).to be_unprocessable
            expect(response_hash["error"]).to eq("Este e-mail já possui permissões de admnistrador")
            expect(existing_client_employee.admin_organizations.count).to eq(0)
            expect(AdminOrganizationMailer.deliveries.count).to eq(0)
          end
        end

        context "when existing client employee is not admin lecupon" do
          let!(:existing_client_employee) { create(:client_employee, role: "admin_client") }
          let(:params) do
            {client_employee: {email: existing_client_employee.email.upcase, name: "any name"}}
          end

          it "must not admin_organization and renders unprocessable" do
            expect do
              path = [
                "/client/v2/businesses/#{business.cnpj}/organizations/#{organization.id}/admins/invitations",
                "/client/v2/organizations/#{organization.id}/admins/invitations"
              ].sample
              post path, headers:, params:
            end.not_to change(ClientEmployee, :count)

            expect(response).to be_unprocessable
            expect(response_hash["error"]).to eq("Este e-mail já possui permissões de admnistrador")
            expect(existing_client_employee.admin_organizations.count).to eq(0)
            expect(AdminOrganizationMailer.deliveries.count).to eq(0)
          end
        end
      end

      context "when business has no profile for organization" do
        before { organization_profile.delete }

        let(:params) { {client_employee: {email: "<EMAIL>", name: "any name"}} }

        it "must render error" do
          expect do
            path = [
              "/client/v2/businesses/#{business.cnpj}/organizations/#{organization.id}/admins/invitations",
              "/client/v2/organizations/#{organization.id}/admins/invitations"
            ].sample
            post path, headers:, params:
          end.not_to change(ClientEmployee, :count)

          expect(response).to be_not_found
          expect(AdminOrganizationMailer.deliveries.count).to eq(0)
        end
      end
    end

    context "when business unrelated to the current user" do
      let(:client_employee) { create(:client_employee, :admin_client) }

      it "must be unauthorized" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/organizations/#{organization.id}/admins/invitations",
          "/client/v2/organizations/#{organization.id}/admins/invitations"
        ].sample
        post(path, headers:)

        expect(response).to have_http_status(:not_found)
        expect(response_hash["error"]).to eq("Business não encontrado")
      end
    end
  end

  describe "#update" do
    let(:inviter) { create(:client_employee, :admin_lecupon) }
    let!(:invited_client_employee) { ClientEmployee.invite!({email: "<EMAIL>", role: "shopkeeper", skip_invitation: true}, inviter) }

    let(:params) { {client_employee: {email: "<EMAIL>"}} }

    let(:headers) do
      {
        "X-ClientEmployee-Email": inviter.email,
        "X-ClientEmployee-Token": inviter.authentication_token,
        "Tenant-id": business.cnpj
      }
    end

    context "when client_employee was invited to the organization and business" do
      let!(:admin_organization) { create(:admin_organization, client_employee: invited_client_employee, business:, organization:) }

      context "when it has already accepted an invite" do
        before do
          invited_client_employee.update!(invitation_accepted_at: 5.minutes.ago)
        end

        it "must render error" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/organizations/#{organization.id}/admins/invitations",
            "/client/v2/organizations/#{organization.id}/admins/invitations"
          ].sample
          put(path, headers:, params:)

          expect(response).to be_unprocessable
          expect(response_hash["error"]).to eq("Este convite já foi aceito")
        end
      end

      context "when it does not have accepted an invite yet" do
        it "must send invite mail and render successfull" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/organizations/#{organization.id}/admins/invitations",
            "/client/v2/organizations/#{organization.id}/admins/invitations"
          ].sample
          put(path, headers:, params:)

          expect(response).to be_successful
          expect(AdminOrganizationMailer.deliveries.count).to eq(1)
          mail = AdminOrganizationMailer.deliveries.last
          expect(mail.to).to eq([invited_client_employee.email])
          expect(mail.from).to eq(["<EMAIL>"])
          expect(CGI.unescapeHTML(mail.html_part.body.raw_source))
            .to include(organization.name)
            .and include(business.name)
        end
      end
    end

    context "when client employee was not invited to the organization and business" do
      it "must render error" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/organizations/#{organization.id}/admins/invitations",
          "/client/v2/organizations/#{organization.id}/admins/invitations"
        ].sample
        put(path, headers:, params:)

        expect(response).to be_unprocessable
        expect(response_hash["error"]).to eq("Este e-mail ainda não foi convidado")
      end
    end
  end
end
