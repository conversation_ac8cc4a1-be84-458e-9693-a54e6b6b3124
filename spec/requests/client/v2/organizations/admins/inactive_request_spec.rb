require "rails_helper"

RSpec.describe Client::V2::Organizations::Admins::InactiveController, type: :request do
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  describe "#update" do
    let(:admin_organization) { create(:admin_organization, active: true) }
    let(:organization) { admin_organization.organization }
    let(:business) { admin_organization.business }
    let(:client_employee) { admin_organization.client_employee }

    context "when business has profile for organization" do
      let!(:organization_profile) { create(:organization_profile, organization:, business:) }

      context "when exists an admin_organization for this business, organization and admin" do
        it "must be activated" do
          path = [
            "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/admins/#{client_employee.id}/inactive",
            "/client/v2/organizations/#{organization.id}/admins/#{client_employee.id}/inactive"
          ].sample
          patch(path, headers:)

          expect(response).to be_successful
          expect(admin_organization.reload).not_to be_active
        end
      end

      context "when does not exist an admin_organization for this business, organization and admin" do
        let(:another_organization) { create(:organization) }

        it "renders not found" do
          path = [
            "/client/v2/businesses/#{business.id}/organizations/#{another_organization.id}/admins/#{client_employee.id}/inactive",
            "/client/v2/organizations/#{another_organization.id}/admins/#{client_employee.id}/inactive"
          ].sample
          patch(path, headers:)

          expect(response).to be_not_found
        end
      end
    end

    context "when business has no profile for organization" do
      it "renders not found" do
        patch(
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/admins/#{client_employee.id}/inactive",
          headers:
        )

        expect(response).to be_not_found
      end
    end
  end
end
