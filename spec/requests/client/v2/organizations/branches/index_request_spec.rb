require "rails_helper"

RSpec.describe Client::V2::Organizations::BranchesController, type: :request do
  let!(:business) { create(:business) }
  let!(:client_employee) { create(:client_employee, :admin_client, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  describe "#index" do
    let(:serializer_keys) do
      %w[id organization_id city_id name cnpj description telephone cep address number complement
        neighborhood created_at updated_at lat lng opening_hour closing_hour timezone active
        contact_name contact_email contact_telephone branch_type full_address editable uf qrcode_string]
    end
    let!(:organization) { create(:organization) }

    it "returns branches ordered by name" do
      create(:organization_profile, organization:, business:)
      branch_alphabetically_first = create(:branch, :physical, organization:, name: "Filial BH")
      branch_alphabetically_second = create(:branch, :online, organization:, name: "Online", business:)

      path = [
        "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/branches",
        "/client/v2/organizations/#{organization.id}/branches"
      ].sample
      get(path, headers:)

      expect(response).to be_ok
      expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
      expect(response_hash.pluck("id", "editable", "uf")).to eq([
        [branch_alphabetically_first.id, false, branch_alphabetically_first.federation_unit.initials],
        [branch_alphabetically_second.id, true, nil]
      ])
    end

    it "returns empty when organization does not have branches" do
      create(:organization_profile, organization:, business:)

      path = [
        "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/branches",
        "/client/v2/organizations/#{organization.id}/branches"
      ].sample
      get(path, headers:)

      expect(response).to be_ok
      expect(response_hash).to be_empty
    end

    it "returns branches when does not have profile" do
      branch = create(:branch, organization:)

      params = {only_exclusive: false}
      path = [
        "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/branches",
        "/client/v2/organizations/#{organization.id}/branches"
      ].sample
      get(path, headers:, params:)

      expect(response).to be_ok
      expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
      expect(response_hash.pluck("id")).to eq([branch.id])
    end

    it "returns branches when does not have profile when request is json" do
      branch = create(:branch, organization:)

      params = {only_exclusive: false}
      path = [
        "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/branches",
        "/client/v2/organizations/#{organization.id}/branches"
      ].sample
      get(path, headers:, params:, as: :json)

      expect(response).to be_ok
      expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
      expect(response_hash.pluck("id")).to eq([branch.id])
    end

    it "returns empty when organization is blocklisted by business" do
      create(:organization_profile, organization:, business:)
      create(:organization_blocklist, business:, organization:)

      path = [
        "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/branches",
        "/client/v2/organizations/#{organization.id}/branches"
      ].sample
      get(path, headers:)

      expect(response).to be_ok
      expect(response_hash).to be_empty
    end

    it "returns empty when business has no profile for organization" do
      path = [
        "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/branches",
        "/client/v2/organizations/#{organization.id}/branches"
      ].sample
      get(path, headers:)

      expect(response).to be_ok
      expect(response_hash).to be_empty
    end

    it "does not return branches of another organization" do
      create(:organization_profile, organization:, business:)
      branch = create(:branch, organization:)
      another_organization = create(:organization)
      branch_another_organization = create(:branch, organization: another_organization)

      path = [
        "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/branches",
        "/client/v2/organizations/#{organization.id}/branches"
      ].sample
      get(path, headers:)

      expect(response).to be_ok
      expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
      expect(response_hash.pluck("id"))
        .to include(branch.id)
        .and not_include(branch_another_organization.id)
    end

    it "filters branches by name" do
      create(:organization_profile, organization:, business:)
      branch = create(:branch, organization:, name: "Filial SP")
      branch_not_filtered = create(:branch, organization:, name: "Filial BH")
      params = {name: " sp  "}

      path = [
        "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/branches",
        "/client/v2/organizations/#{organization.id}/branches"
      ].sample
      get(path, params:, headers:)

      expect(response).to be_ok
      expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
      expect(response_hash.pluck("id"))
        .to include(branch.id)
        .and not_include(branch_not_filtered.id)
    end

    it "filters branches by cnpj" do
      create(:organization_profile, organization:, business:)
      branch = create(:branch, organization:)
      branch_not_filtered = create(:branch, organization:)
      params = {cnpj: CNPJ.new(branch.cnpj).formatted}

      path = [
        "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/branches",
        "/client/v2/organizations/#{organization.id}/branches"
      ].sample
      get(path, params:, headers:)

      expect(response).to be_ok
      expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
      expect(response_hash.pluck("id"))
        .to include(branch.id)
        .and not_include(branch_not_filtered.id)
    end

    it "filters branches by active" do
      create(:organization_profile, organization:, business:)
      branch = create(:branch, organization:, active: true)
      branch_not_filtered = create(:branch, organization:, active: false)
      params = {active: true}

      path = [
        "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/branches",
        "/client/v2/organizations/#{organization.id}/branches"
      ].sample
      get(path, params:, headers:)

      expect(response).to be_ok
      expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
      expect(response_hash.pluck("id"))
        .to include(branch.id)
        .and not_include(branch_not_filtered.id)
    end

    it "must not paginate when sending 'skip_pagination' param" do
      create(:organization_profile, organization:, business:)
      create_list(:branch, WillPaginate.per_page + 1, :physical, organization:, name: "Filial BH")

      path = [
        "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/branches",
        "/client/v2/organizations/#{organization.id}/branches"
      ].sample
      get(path, headers:, params: {skip_pagination: true})

      expect(response).to be_ok
      expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
      expect(response_hash.count).to eq(WillPaginate.per_page + 1)
    end

    context "when business unrelated to the current user" do
      let(:unrelated_business) { create(:business) }
      let(:headers) do
        {
          "X-ClientEmployee-Email": client_employee.email,
          "X-ClientEmployee-Token": client_employee.authentication_token,
          "Tenant-id": unrelated_business.cnpj
        }
      end

      it "renders not_found" do
        create(:organization_profile, organization:, business:)

        path = [
          "/client/v2/businesses/#{unrelated_business.id}/organizations/#{organization.id}/branches",
          "/client/v2/organizations/#{organization.id}/branches"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:not_found)
        expect(response_hash["error"]).to eq("Business não encontrado")
      end
    end

    describe "branch_type filter" do
      let!(:physical_branch) { create(:branch, :physical, organization:, name: "Filial BH") }
      let!(:online_branch) { create(:branch, :online, organization:, name: "Online", business:) }

      it "must filter online branch when branch type filter is 'online'" do
        create(:organization_profile, organization:, business:)

        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/branches",
          "/client/v2/organizations/#{organization.id}/branches"
        ].sample
        get(path, headers:, params: {branch_type: Enums::Branch::Type::ONLINE})

        expect(response).to be_ok
        expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
        expect(response_hash.count).to eq(1)
        expect(response_hash.first["id"]).to eq(online_branch.id)
      end

      it "must filter physical branch when branch type filter is 'physical'" do
        create(:organization_profile, organization:, business:)

        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/branches",
          "/client/v2/organizations/#{organization.id}/branches"
        ].sample
        get(path, headers:, params: {branch_type: Enums::Branch::Type::PHYSICAL})

        expect(response).to be_ok
        expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
        expect(response_hash.count).to eq(1)
        expect(response_hash.first["id"]).to eq(physical_branch.id)
      end
    end
  end

  def response_hash
    JSON.parse(response.body)
  end
end
