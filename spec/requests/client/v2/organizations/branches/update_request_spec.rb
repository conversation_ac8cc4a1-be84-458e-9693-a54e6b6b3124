require "rails_helper"

RSpec.describe Client::V2::Organizations::BranchesController, type: :request do
  let!(:business) { create(:business) }
  let!(:client_employee) { create(:client_employee, :admin_client, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  describe "#update" do
    let(:serializer_keys) do
      %w[id organization_id city_id name cnpj description telephone cep address number complement
        neighborhood created_at updated_at lat lng opening_hour closing_hour timezone active
        contact_name contact_email contact_telephone branch_type full_address editable uf qrcode_string]
    end
    let!(:organization) { create(:organization) }

    it "updates branch" do
      create(:organization_profile, organization:, business:)
      branch = create(:branch, business:, organization:)
      params = {name: FFaker::NameBR.name}

      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/branches/#{branch.id}",
          "/client/v2/organizations/#{organization.id}/branches/#{branch.id}"
        ].sample
        patch path, headers:, params:
      end.to change { branch.reload.name }.to(params[:name])

      expect(response).to be_ok
      expect(response_hash.keys).to match_array(serializer_keys)
    end

    context "when business unrelated to the current user" do
      let(:unrelated_business) { create(:business) }
      let(:headers) do
        {
          "X-ClientEmployee-Email": client_employee.email,
          "X-ClientEmployee-Token": client_employee.authentication_token,
          "Tenant-id": unrelated_business.cnpj
        }
      end

      it "renders not_found" do
        create(:organization_profile, organization:, business:)
        branch = create(:branch, business:, organization:)

        expect do
          path = [
            "/client/v2/businesses/#{unrelated_business.id}/organizations/#{organization.id}/branches/#{branch.id}",
            "/client/v2/organizations/#{organization.id}/branches/#{branch.id}"
          ].sample
          patch path, headers:
        end.to not_change { branch.reload.attributes }

        expect(response).to have_http_status(:not_found)
        expect(response_hash["error"]).to eq("Business não encontrado")
      end
    end

    it "renders ok when organization is not allowed to business" do
      create(:organization_profile, organization:, business:)
      branch = create(:branch, business:, organization:)
      create(:organization_blocklist, business:, organization:)

      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/branches/#{branch.id}",
          "/client/v2/organizations/#{organization.id}/branches/#{branch.id}"
        ].sample
        patch path, headers:
      end.to not_change { branch.reload.attributes }

      expect(response).to be_ok
      expect(response_hash.keys).to match_array(serializer_keys)
    end

    it "renders not found when business has no profile for organization" do
      branch = create(:branch, business:, organization:)

      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/branches/#{branch.id}",
          "/client/v2/organizations/#{organization.id}/branches/#{branch.id}"
        ].sample
        patch path, headers:
      end.to not_change { branch.reload.attributes }

      expect(response).to be_not_found
    end

    it "renders not found when branch was not created by client employee from same business" do
      create(:organization_profile, organization:, business:)
      branch = create(:branch, business: nil, organization:)

      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/branches/#{branch.id}",
          "/client/v2/organizations/#{organization.id}/branches/#{branch.id}"
        ].sample
        patch path, headers:
      end.to not_change { branch.reload.attributes }

      expect(response).to be_not_found
    end

    it "renders not found when branch does not exist" do
      create(:organization_profile, organization:, business:)

      path = [
        "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/branches/0",
        "/client/v2/organizations/#{organization.id}/branches/0"
      ].sample
      patch(path, headers:)

      expect(response).to be_not_found
    end
  end

  def response_hash
    JSON.parse(response.body)
  end
end
