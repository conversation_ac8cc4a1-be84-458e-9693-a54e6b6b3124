require "rails_helper"

RSpec.describe Client::V2::Organizations::BranchesController, type: :request do
  let!(:business) { create(:business) }
  let!(:client_employee) { create(:client_employee, :admin_client, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  describe "#create" do
    let(:serializer_keys) do
      %w[id organization_id city_id name cnpj description telephone cep address number complement
        neighborhood created_at updated_at lat lng opening_hour closing_hour timezone active
        contact_name contact_email contact_telephone branch_type full_address editable uf qrcode_string]
    end
    let!(:organization) { create(:organization) }

    it "creates branch" do
      create(:organization_profile, organization:, business:)
      branch_type = ["physical", "online"].sample
      city = create(:city)
      params = attributes_for(:branch, branch_type).merge!(city_id: city.id)

      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/branches",
          "/client/v2/organizations/#{organization.id}/branches"
        ].sample
        post(path, headers:, params:)
      end.to change(organization.branches, :count).by(1)

      expect(response).to be_created
      expect(response_hash.keys).to match_array(serializer_keys)
    end

    it "renders error when required attribute is not sent" do
      create(:organization_profile, organization:, business:)
      branch_type = ["physical", "online"].sample
      required_attributes =
        if branch_type == "physical"
          %i[name cnpj opening_hour closing_hour cep address number neighborhood lat lng
            contact_name contact_email contact_telephone telephone]
        else
          %i[name cnpj contact_name contact_email contact_telephone telephone]
        end
      city = create(:city)
      params =
        attributes_for(:branch, branch_type)
          .merge!(city_id: city.id)
          .except(required_attributes.sample)

      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/branches",
          "/client/v2/organizations/#{organization.id}/branches"
        ].sample
        post(path, headers:, params:)
      end.to not_change(Branch, :count)

      expect(response).to be_precondition_failed
    end

    context "when business unrelated to the current user" do
      let(:unrelated_business) { create(:business) }
      let(:headers) do
        {
          "X-ClientEmployee-Email": client_employee.email,
          "X-ClientEmployee-Token": client_employee.authentication_token,
          "Tenant-id": unrelated_business.cnpj
        }
      end

      it "render not_found" do
        create(:organization_profile, organization:, business:)

        expect do
          path = [
            "/client/v2/businesses/#{unrelated_business.id}/organizations/#{organization.id}/branches",
            "/client/v2/organizations/#{organization.id}/branches"
          ].sample
          post path, headers:
        end.to not_change(Branch, :count)

        expect(response).to have_http_status(:not_found)
        expect(response_hash["error"]).to eq("Business não encontrado")
      end
    end

    it "renders error when business has no profile for organization" do
      branch_type = ["physical", "online"].sample
      city = create(:city)
      params = attributes_for(:branch, branch_type).merge!(city_id: city.id)
      create(:organization_blocklist, business:, organization:)

      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/branches",
          "/client/v2/organizations/#{organization.id}/branches"
        ].sample
        post path, headers:, params:
      end.not_to change(Branch, :count)

      expect(response).to be_not_found
    end

    it "renders error when organization already have online branch" do
      create(:organization_profile, organization:, business:)
      create(:branch, :online, organization:)
      city = create(:city)
      params = attributes_for(:branch, "online")
        .merge!(city_id: city.id)

      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/branches",
          "/client/v2/organizations/#{organization.id}/branches"
        ].sample
        post path, headers:, params:
      end.to not_change(Branch, :count)
      expect(response).to have_http_status(:unprocessable_entity)
      expect(response_hash["error"]).to eq("Tipo de filial já existe uma online para essa marca")
    end
  end

  def response_hash
    JSON.parse(response.body)
  end
end
