require "rails_helper"

RSpec.describe Client::V2::Organizations::BranchesController, type: :request do
  let!(:business) { create(:business) }
  let!(:client_employee) { create(:client_employee, :admin_client, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  describe "#show" do
    let!(:organization) { create(:organization) }

    it "returns branch created by business" do
      create(:organization_profile, organization:, business:)
      branch = create(:branch, :physical, organization:, business:)
      create(:branch, :online, organization:, business:)

      path = [
        "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/branches/#{branch.id}",
        "/client/v2/organizations/#{organization.id}/branches/#{branch.id}"
      ].sample
      get(path, headers:)

      expect(response).to be_ok
      expect(response_hash.keys).to match_array(serializer_keys)
      expect(response_hash["city"].keys).to match_array(city_serializer_keys)
      expect(response_hash["id"]).to eq(branch.id)
      expect(response_hash["editable"]).to eq(true)
      expect(response_hash["uf"]).to eq(branch.federation_unit.initials)
    end

    it "returns branch not created by business" do
      create(:organization_profile, organization:, business:)
      branch = create(:branch, :physical, organization:)
      create(:branch, :online, organization:, business:)

      path = [
        "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/branches/#{branch.id}",
        "/client/v2/organizations/#{organization.id}/branches/#{branch.id}"
      ].sample
      get(path, headers:)

      expect(response).to be_ok
      expect(response_hash.keys).to match_array(serializer_keys)
      expect(response_hash["city"].keys).to match_array(city_serializer_keys)
      expect(response_hash["id"]).to eq(branch.id)
      expect(response_hash["editable"]).to eq(false)
      expect(response_hash["uf"]).to eq(branch.federation_unit.initials)
    end

    it "returns not found when branch belongs to another organization" do
      create(:organization_profile, organization:, business:)
      unrelated_organization = create(:organization)
      branch = create(:branch, :physical, organization: unrelated_organization, business:)

      path = [
        "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/branches/#{branch.id}",
        "/client/v2/organizations/#{organization.id}/branches/#{branch.id}"
      ].sample
      get(path, headers:)

      expect(response).to be_not_found
    end

    it "returns ok when organization is blocklisted by business" do
      create(:organization_profile, organization:, business:)
      branch = create(:branch, :physical, organization:, business:)
      create(:organization_blocklist, business:, organization:)

      path = [
        "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/branches/#{branch.id}",
        "/client/v2/organizations/#{organization.id}/branches/#{branch.id}"
      ].sample
      get(path, headers:)

      expect(response).to be_ok
      expect(response_hash.keys).to match_array(serializer_keys)
    end

    it "renders not found when business has no profile for organization" do
      branch = create(:branch, :physical, organization:, business:)

      path = [
        "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/branches/#{branch.id}",
        "/client/v2/organizations/#{organization.id}/branches/#{branch.id}"
      ].sample
      get(path, headers:)

      expect(response).to be_not_found
    end

    context "when business unrelated to the current user" do
      let(:unrelated_business) { create(:business) }
      let(:headers) do
        {
          "X-ClientEmployee-Email": client_employee.email,
          "X-ClientEmployee-Token": client_employee.authentication_token,
          "Tenant-id": unrelated_business.cnpj
        }
      end

      it "renders not_found" do
        create(:organization_profile, organization:, business:)
        branch = create(:branch, :physical, organization:, business: unrelated_business)

        path = [
          "/client/v2/businesses/#{unrelated_business.id}/organizations/#{organization.id}/branches/#{branch.id}",
          "/client/v2/organizations/#{organization.id}/branches/#{branch.id}"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:not_found)
        expect(response_hash["error"]).to eq("Business não encontrado")
      end
    end
  end

  def response_hash
    JSON.parse(response.body)
  end

  def serializer_keys
    %w[id
      active
      address
      branch_type
      cep
      city
      city_id
      closing_hour
      cnpj
      complement
      contact_email
      contact_name
      contact_telephone
      created_at
      description
      editable
      full_address
      lat
      lng
      name
      neighborhood
      number
      opening_hour
      organization_id
      telephone
      timezone
      uf
      updated_at]
  end

  def city_serializer_keys
    %w[id name]
  end
end
