require "rails_helper"

RSpec.describe Client::V2::Organizations::Branches::ActiveController, type: :request do
  let!(:business) { create(:business) }
  let!(:client_employee) { create(:client_employee, :admin_client, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end
  let(:json_parse_response_body) { JSON.parse(response.body) }

  describe "#update" do
    let!(:organization) { create(:organization) }
    let!(:branch) { create(:branch, organization:, business:, active: false) }

    context "when business has organization profile" do
      before { business.organizations << organization }

      it "activates branch" do
        expect do
          path = [
            "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/branches/#{branch.id}/active",
            "/client/v2/organizations/#{organization.id}/branches/#{branch.id}/active"
          ].sample
          patch path, headers:
        end.to change(organization.branches.active, :count).by(1)

        expect(response).to be_ok
      end

      context "when business unrelated to the current user" do
        let(:unrelated_business) { create(:business) }
        let(:headers) do
          {
            "X-ClientEmployee-Email": client_employee.email,
            "X-ClientEmployee-Token": client_employee.authentication_token,
            "Tenant-id": unrelated_business.cnpj
          }
        end

        it "renders not_found" do
          expect do
            path = [
              "/client/v2/businesses/#{unrelated_business.id}/organizations/#{unrelated_business.id}/branches/#{branch.id}/active",
              "/client/v2/organizations/#{unrelated_business.id}/branches/#{branch.id}/active"
            ].sample
            patch path, headers:
          end.to not_change(Branch.where(active: true), :count)

          expect(response).to have_http_status(:not_found)
          expect(json_parse_response_body["error"]).to eq("Business não encontrado")
        end
      end
    end

    context "when business does not have organization profile" do
      it "renders not found" do
        expect do
          path = [
            "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/branches/#{branch.id}/active",
            "/client/v2/organizations/#{organization.id}/branches/#{branch.id}/active"
          ].sample
          patch path, headers:
        end.to not_change(Branch.where(active: true), :count)

        expect(response).to be_not_found
      end
    end
  end
end
