require "rails_helper"

RSpec.describe Client::V2::Organizations::PromotionsController, type: :request do
  let(:business) { create(:business) }
  let!(:client_employee) { create(:client_employee, :admin_lecupon) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end
  let(:serializer_keys) do
    %w[
      id
      discount_value
      discount_type
      description
      end_date
      end_hour
      provider_type
      start_date
      start_hour
      status
      title
      web_postos
    ]
  end
  let(:response_hash) { JSON.parse(response.body) }

  describe "GET index" do
    let!(:business) { create(:business, :oab) }
    let!(:available_organization) { create(:organization) }
    let!(:blocklisted_organization) { create(:organization) }
    let!(:organization_blocklist) do
      create(:organization_blocklist, business:, organization: blocklisted_organization)
    end
    let!(:available_promotion) { create(:promotion, :available, :percent_discount, organization: available_organization, discount_value: 15, business:) }
    let!(:available_branch) { create(:branch, organization: available_organization) }
    let!(:available_cupon) { create(:cupon, :cpf, branch: available_branch, promotion: available_promotion, organization: available_organization) }
    let!(:blocklisted_organization_coupon) { create(:promotion, organization: blocklisted_organization) }

    before do
      business.organizations << available_organization
    end

    it "returns all available promotions to business" do
      path = [
        "/client/v2/businesses/#{business.cnpj}/organizations/#{available_organization.id}/promotions",
        "/client/v2/organizations/#{available_organization.id}/promotions"
      ].sample
      get(path, headers:, as: :json)

      expect(response).to be_ok
      expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
      expect(response_hash.pluck("discount_value")).to match_array([15.0])
      expect(response_hash.pluck("discount_type")).to match_array(["percent"])
      expect(response_hash.count).to eq(1)
      expect(response_hash.first["id"]).to eq(available_promotion.id)
    end

    context "when filtering by description" do
      let!(:promotion_one) { create(:promotion, description: "foo", organization: available_organization, business:) }
      let!(:promotion_two) { create(:promotion, description: "bar", organization: available_organization, business:) }
      let(:params) { {term: "foo"} }

      it "returns promotion filtered by description" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/organizations/#{available_organization.id}/promotions",
          "/client/v2/organizations/#{available_organization.id}/promotions"
        ].sample
        get(path, headers:, params:, as: :json)

        expect(response).to be_ok
        expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
        expect(response_hash.count).to eq(1)
        expect(response_hash.first["id"]).to eq(promotion_one.id)
      end
    end
  end
end
