require "rails_helper"

RSpec.describe Client::V2::Organizations::Promotions::BranchesController, type: :request do
  let(:business) { create(:business) }
  let!(:client_employee) { create(:client_employee, :admin_lecupon) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  let(:json_parse_response_body) { JSON.parse(response.body) }

  describe "#update" do
    context "given a active organization" do
      let(:active_organization) do
        organization = create(:organization, active: true)
        create(:organization_profile, organization:, business:)

        organization
      end

      context "when the promotion is from the current business" do
        let(:branch) { create(:branch, organization: active_organization) }
        let(:create_promotion) do
          available_promotion = create(:promotion, organization: active_organization, business:)
          create(:cupon, branch:, promotion: available_promotion, organization: active_organization)

          available_promotion
        end

        let(:create_promotion_without_provider) do
          available_promotion = create(:promotion, organization: active_organization, business:, provider: nil, provider_type: nil)
          create_list(:cupon, 3, :with_branch, promotion: available_promotion, organization: active_organization)

          available_promotion
        end
        let(:create_promotion_with_many_branches) do
          available_promotion = create(:promotion, organization: active_organization, business:)
          create_list(:cupon, 3, :with_branch, promotion: available_promotion, organization: active_organization)

          available_promotion
        end
        it "is expect change branch" do
          promotion = create_promotion
          new_branch = create(:branch, :online, organization: active_organization)
          branch_params = {branch_ids: [new_branch.id]}

          path = [
            "/client/v2/businesses/#{business.cnpj}/organizations/#{active_organization.id}/promotions/#{promotion.id}/branches",
            "/client/v2/organizations/#{active_organization.id}/promotions/#{promotion.id}/branches"
          ].sample
          patch path, headers:, params: branch_params, as: :json

          expect(response).to have_http_status(:ok)
          promotion.reload
          expect(promotion.branches.count).to eq(1)
          expect(promotion.branches.first.id).to eq(new_branch.id)
          expect(promotion.cupons.active.count).to eq(1)
          expect(promotion.cupons.inactive.count).to eq(1)
        end

        it "is expected response change quantity of branches" do
          promotion = create_promotion_with_many_branches
          new_branch = create(:branch, :online, organization: active_organization)
          branch_params = {branch_ids: [new_branch.id]}

          path = [
            "/client/v2/businesses/#{business.cnpj}/organizations/#{active_organization.id}/promotions/#{promotion.id}/branches",
            "/client/v2/organizations/#{active_organization.id}/promotions/#{promotion.id}/branches"
          ].sample
          patch path, headers:, params: branch_params, as: :json

          promotion.reload
          expect(response).to have_http_status(:ok)
          promotion.reload
          expect(promotion.branches.count).to eq(1)
          expect(promotion.branches.first.id).to eq(new_branch.id)
          expect(promotion.cupons.active.count).to eq(1)
          expect(promotion.cupons.inactive.count).to eq(3)
        end

        specify "expect success when branch dose not have provider" do
          promotion = create_promotion_without_provider
          new_branch = create(:branch, :online, organization: active_organization)
          branch_params = {branch_ids: [new_branch.id]}

          path = [
            "/client/v2/businesses/#{business.cnpj}/organizations/#{active_organization.id}/promotions/#{promotion.id}/branches",
            "/client/v2/organizations/#{active_organization.id}/promotions/#{promotion.id}/branches"
          ].sample
          patch path, headers:, params: branch_params, as: :json

          expect(response).to have_http_status(:ok)
        end

        context "given a invalid params" do
          it "is expected response with error" do
            promotion = create_promotion
            branch_params = {branch_ids: ""}

            path = [
              "/client/v2/businesses/#{business.cnpj}/organizations/#{active_organization.id}/promotions/#{promotion.id}/branches",
              "/client/v2/organizations/#{active_organization.id}/promotions/#{promotion.id}/branches"
            ].sample
            patch path, headers:, params: branch_params, as: :json

            expect(response).to have_http_status(:precondition_failed)
            expect(json_parse_response_body["error"])
              .to eq("A requisição falhou devido a ausência de parâmetro: branch_ids")
          end
        end

        context "when business is inactve" do
          let(:create_promotion) { Promotion.create!(provider_type: "online", source: "business", organization: active_organization, business:) }

          specify do
            business.update!(status: Business::Status::SUSPENDED_BY_OVERDUE)
            promotion = create_promotion
            new_branch = create(:branch, :online, organization: active_organization)
            branch_params = {branch_ids: [new_branch.id]}

            path = [
              "/client/v2/businesses/#{business.cnpj}/organizations/#{active_organization.id}/promotions/#{promotion.id}/branches",
              "/client/v2/organizations/#{active_organization.id}/promotions/#{promotion.id}/branches"
            ].sample
            patch path, headers:, params: branch_params, as: :json

            expect(response).to have_http_status(:not_found)
          end
        end
      end

      context "given promotion from an another business" do
        let(:another_business) { create(:business) }
        let(:create_promotion) { Promotion.create!(source: "business", organization: active_organization, business: another_business) }

        specify do
          promotion = create_promotion
          new_branch = create(:branch, :online, organization: active_organization)
          branch_params = {branch_ids: [new_branch.id]}

          path = [
            "/client/v2/businesses/#{business.cnpj}/organizations/#{active_organization.id}/promotions/#{promotion.id}/branches",
            "/client/v2/organizations/#{active_organization.id}/promotions/#{promotion.id}/branches"
          ].sample
          patch path, headers:, params: branch_params, as: :json

          expect(response).to have_http_status(:not_found)
        end
      end
    end

    context "given a blocklisted organization" do
      let(:blocklisted_organization) do
        organization = create(:organization, active: true)
        create(:organization_profile, organization:, business:)
        create(:organization_blocklist, business:, organization:)

        organization
      end
      let(:create_promotion) do
        available_promotion = create(:promotion, organization: blocklisted_organization, business:)
        branch = create(:branch, organization: blocklisted_organization)
        create(:cupon, branch:, promotion: available_promotion, organization: blocklisted_organization)

        available_promotion
      end

      it "is expected response to be ok" do
        promotion = create_promotion
        new_branch = create(:branch, :online, organization: blocklisted_organization)
        branch_params = {branch_ids: [new_branch.id]}

        path = [
          "/client/v2/businesses/#{business.cnpj}/organizations/#{blocklisted_organization.id}/promotions/#{promotion.id}/branches",
          "/client/v2/organizations/#{blocklisted_organization.id}/promotions/#{promotion.id}/branches"
        ].sample
        patch path, headers:, params: branch_params, as: :json

        expect(response).to have_http_status(:ok)
      end
    end

    context "given a inactive organization" do
      let(:inactive_organization) do
        organization = create(:organization, active: false)
        create(:organization_profile, organization:, business:)
        organization
      end
      let(:create_promotion) do
        available_promotion = create(:promotion, organization: inactive_organization, business:)
        branch = create(:branch, organization: inactive_organization)
        create(:cupon, branch:, promotion: available_promotion, organization: inactive_organization)

        available_promotion
      end

      specify do
        promotion = create_promotion
        new_branch = create(:branch, :online, organization: inactive_organization)
        branch_params = {branch_ids: [new_branch.id]}

        path = [
          "/client/v2/businesses/#{business.cnpj}/organizations/#{inactive_organization.id}/promotions/#{promotion.id}/branches",
          "/client/v2/organizations/#{inactive_organization.id}/promotions/#{promotion.id}/branches"
        ].sample
        patch path, headers:, params: branch_params, as: :json

        expect(response).to have_http_status(:not_found)
      end
    end

    context "when organization does not have profile" do
      let(:active_organization_without_profile) { create(:organization, active: true) }
      let(:create_promotion) { Promotion.create!(source: "business", organization: active_organization_without_profile, business:) }

      specify do
        promotion = create_promotion
        descriptions_params = {
          title: FFaker::LoremBR.phrase,
          description: FFaker::LoremBR.paragraph,
          redeem_limited: "false"
        }

        path = [
          "/client/v2/businesses/#{business.cnpj}/organizations/#{active_organization_without_profile.id}/promotions/#{promotion.id}/branches",
          "/client/v2/organizations/#{active_organization_without_profile.id}/promotions/#{promotion.id}/branches"
        ].sample
        patch path, headers:, params: descriptions_params, as: :json

        expect(response).to have_http_status(:not_found)
      end
    end

    context "when promotion is inactive" do
      let!(:organization) { create(:organization) }
      let!(:organization_profile) { create(:organization_profile, organization:, business:) }
      let!(:branch) { create(:branch, organization:) }
      let!(:promotion) { create(:promotion, :inactive, organization:, business:) }
      let!(:coupon) { create(:cupon, branch:, promotion:, organization:) }
      let(:params) { {branch_ids: [branch.id]} }

      it "renders error" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/organizations/#{organization.id}/promotions/#{promotion.id}/branches",
          "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/branches"
        ].sample
        patch path, headers:, params:, as: :json

        expect(response).to be_not_found
      end
    end
  end
end
