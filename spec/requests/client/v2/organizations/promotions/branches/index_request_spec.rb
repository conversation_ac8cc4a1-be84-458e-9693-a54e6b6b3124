require "rails_helper"

RSpec.describe Client::V2::Organizations::Promotions::BranchesController, type: :request do
  describe "#index" do
    let(:business) { create(:business) }
    let!(:client_employee) { create(:client_employee, :admin_lecupon) }
    let(:headers) do
      {
        "X-ClientEmployee-Email": client_employee.email,
        "X-ClientEmployee-Token": client_employee.authentication_token,
        "Tenant-id": business.cnpj
      }
    end

    it "renders successful" do
      organization = create(:organization)
      create(:organization_profile, organization:, business:)
      promotion = create(:promotion, organization:, business:, provider_type: "code")
      branch_1 = create(:branch, organization:, name: "Branch B")
      create(:cupon, promotion:, branch: branch_1)
      branch_2 = create(:branch, organization:, name: "Branch A")
      create(:cupon, promotion:, branch: branch_2)

      unrelated_promotion = create(:promotion)
      unrelated_branch = create(:branch, organization:)
      create(:cupon, promotion: unrelated_promotion, branch: unrelated_branch)

      path = [
        "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/branches",
        "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/branches"
      ].sample
      get(path, headers:)

      expect(response).to be_ok
      expect(response_hash).to match_array([
        {
          "id" => branch_2.id,
          "name" => "Branch A"
        },
        {
          "id" => branch_1.id,
          "name" => "Branch B"
        }
      ])
    end

    it "renders error when promotion is not exclusive" do
      organization = create(:organization)
      create(:organization_profile, organization:, business:)
      promotion = create(:promotion, organization:, provider_type: "code")
      branch_1 = create(:branch, organization:, name: "Branch B")
      create(:cupon, promotion:, branch: branch_1)
      branch_2 = create(:branch, organization:, name: "Branch A")
      create(:cupon, promotion:, branch: branch_2)

      unrelated_promotion = create(:promotion)
      unrelated_branch = create(:branch, organization:)
      create(:cupon, promotion: unrelated_promotion, branch: unrelated_branch)

      path = [
        "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/branches",
        "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/branches"
      ].sample
      get(path, headers:)

      expect(response).to be_not_found
    end

    it "renders error when business does not have organization profile" do
      organization = create(:organization)
      promotion = create(:promotion, organization:, provider_type: "code")
      branch_1 = create(:branch, organization:, name: "Branch B")
      create(:cupon, promotion:, branch: branch_1)
      branch_2 = create(:branch, organization:, name: "Branch A")
      create(:cupon, promotion:, branch: branch_2)

      unrelated_promotion = create(:promotion)
      unrelated_branch = create(:branch, organization:)
      create(:cupon, promotion: unrelated_promotion, branch: unrelated_branch)

      path = [
        "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/branches",
        "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/branches"
      ].sample
      get(path, headers:)

      expect(response).to be_not_found
    end

    it "renders error when organization is not active" do
      organization = create(:organization, active: false)
      create(:organization_profile, organization:, business:)
      promotion = create(:promotion, organization:, provider_type: "code")
      branch_1 = create(:branch, organization:, name: "Branch B")
      create(:cupon, promotion:, branch: branch_1)
      branch_2 = create(:branch, organization:, name: "Branch A")
      create(:cupon, promotion:, branch: branch_2)

      unrelated_promotion = create(:promotion)
      unrelated_branch = create(:branch, organization:)
      create(:cupon, promotion: unrelated_promotion, branch: unrelated_branch)

      path = [
        "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/branches",
        "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/branches"
      ].sample
      get(path, headers:)

      expect(response).to be_not_found
    end

    it "renders error when promotion is inactive" do
      organization = create(:organization)
      create(:organization_profile, organization:, business:)
      promotion = create(:promotion, :inactive, organization:, business:, provider_type: :code)
      branch = create(:branch, organization:)
      create(:cupon, promotion:, branch:)

      path = [
        "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/branches",
        "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/branches"
      ].sample
      get(path, headers:)

      expect(response).to have_http_status(:not_found)
    end
  end

  def response_hash
    JSON.parse(response.body)
  end
end
