require "rails_helper"

RSpec.describe Client::V2::Organizations::Promotions::VoucherTypeController, type: :request do
  describe "#update" do
    let(:business) { create(:business) }
    let!(:client_employee) { create(:client_employee, :admin_lecupon) }
    let(:headers) do
      {
        "X-ClientEmployee-Email": client_employee.email,
        "X-ClientEmployee-Token": client_employee.authentication_token,
        "Tenant-id": business.cnpj
      }
    end

    describe "voucher_type none" do
      let!(:organization) do
        organization = create(:organization)
        create(:organization_profile, organization:, business:)

        organization
      end
      let!(:branch) { create(:branch, organization:) }

      context "when existing promotion has bucket" do
        it "renders successful and remove reference from bucket" do
          bucket = create(:voucher_bucket)
          promotion = create(
            :promotion,
            organization:,
            business:,
            dynamic_voucher: true,
            code: "ABC123",
            voucher_type: nil,
            provider: nil,
            provider_type: "regionalized",
            status: "no_voucher",
            voucher_bucket: bucket
          )
          Cupon.create!(promotion:, branch:, active: true)
          create(:voucher, bucket:)

          params = {voucher_type: "none"}
          expect do
            path = [
              "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/voucher_type",
              "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/voucher_type"
            ].sample
            put path, params:, headers:, as: :json

            expect(response).to be_ok
          end.to change { promotion.reload.voucher_type }.from(nil).to("none")
            .and change(promotion, :status).from("no_voucher").to("available")
            .and change(promotion, :provider).from(nil).to("regionalized")
            .and change(promotion, :dynamic_voucher).from(true).to(false)
            .and change(promotion, :code).from("ABC123").to(nil)
            .and change { promotion.cupons.pluck(:c_type) }.to(all(eq("ONLINE")))
            .and change { promotion.vouchers.count }.to(0)
            .and change(VoucherBucket, :count).by(0)
          expect(VoucherBucket.where(promotions: promotion).count).to eq(0)
        end
      end

      context "when existing promotion has no bucket" do
        it "renders successful" do
          promotion = create(
            :promotion,
            organization:,
            business:,
            dynamic_voucher: true,
            code: "ABC123",
            voucher_type: nil,
            provider: nil,
            provider_type: "regionalized",
            status: "no_voucher"
          )
          Cupon.create!(promotion:, branch:, active: true)

          params = {voucher_type: "none"}
          expect do
            path = [
              "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/voucher_type",
              "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/voucher_type"
            ].sample
            put path, params:, headers:, as: :json

            expect(response).to be_ok
          end.to change { promotion.reload.voucher_type }.from(nil).to("none")
            .and change(promotion, :status).from("no_voucher").to("available")
            .and change(promotion, :provider).from(nil).to("regionalized")
            .and change(promotion, :dynamic_voucher).from(true).to(false)
            .and change(promotion, :code).from("ABC123").to(nil)
            .and change { promotion.cupons.pluck(:c_type) }.to(all(eq("ONLINE")))
            .and not_change { promotion.vouchers.count }
            .and change(VoucherBucket, :count).by(0)
          expect(VoucherBucket.where(promotions: promotion).count).to eq(0)
        end
      end
    end

    describe "voucher_type dynamic" do
      let!(:organization) { create(:organization) }
      let!(:organization_profile) { create(:organization_profile, organization:, business:) }
      let!(:branch) { create(:branch, organization:) }

      context "when existing promotion has bucket" do
        it "renders successful" do
          bucket = create(:voucher_bucket)
          promotion = create(
            :promotion,
            organization:,
            business:,
            dynamic_voucher: false,
            code: "ABC123",
            voucher_type: nil,
            provider: nil,
            provider_type: "code",
            status: "no_voucher",
            voucher_bucket: bucket
          )
          Cupon.create!(promotion:, branch:, active: true)

          params = {voucher_type: "dynamic", web_postos: true}
          expect do
            path = [
              "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/voucher_type",
              "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/voucher_type"
            ].sample
            put path, params:, headers:, as: :json

            expect(response).to be_ok
          end.to change { promotion.reload.voucher_type }.from(nil).to("dynamic")
            .and change(promotion, :status).from("no_voucher").to("available")
            .and change(promotion, :provider).from(nil).to("dynamic")
            .and change(promotion, :dynamic_voucher).from(false).to(true)
            .and change(promotion, :code).from("ABC123").to(nil)
            .and change(promotion, :web_postos).from(false).to(true)
            .and change { promotion.cupons.pluck(:c_type) }.to(all(eq("DYNAMIC")))
            .and change(VoucherBucket, :count).by(0)
          expect(VoucherBucket.where(promotions: promotion).count).to eq(1)
        end
      end

      context "when existing promotion has no bucket" do
        it "renders successful and use existing bucket" do
          another_bucket_promotion = create(:voucher_bucket)
          _another_promotion = create(:promotion, voucher_bucket: another_bucket_promotion, web_postos: true)
          promotion = create(
            :promotion,
            organization:,
            business:,
            dynamic_voucher: false,
            code: "ABC123",
            voucher_type: nil,
            provider: nil,
            provider_type: "code",
            status: "no_voucher"
          )
          Cupon.create!(promotion:, branch:, active: true)

          params = {voucher_type: "dynamic", web_postos: true}
          expect do
            put "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/voucher_type", params:, headers:, as: :json

            expect(response).to be_ok
          end.to change { promotion.reload.voucher_type }.from(nil).to("dynamic")
            .and change(promotion, :status).from("no_voucher").to("available")
            .and change(promotion, :provider).from(nil).to("dynamic")
            .and change(promotion, :dynamic_voucher).from(false).to(true)
            .and change(promotion, :code).from("ABC123").to(nil)
            .and change(promotion, :web_postos).from(false).to(true)
            .and change { promotion.cupons.pluck(:c_type) }.to(all(eq("DYNAMIC")))
            .and change { promotion.voucher_bucket }.from(nil).to(another_bucket_promotion)
            .and not_change(VoucherBucket, :count)
          expect(VoucherBucket.where(promotions: promotion).count).to eq(1)
        end

        it "renders successful and create new bucket" do
          promotion = create(
            :promotion,
            organization:,
            business:,
            dynamic_voucher: false,
            code: "ABC123",
            voucher_type: nil,
            provider: nil,
            provider_type: "code",
            status: "no_voucher"
          )
          Cupon.create!(promotion:, branch:, active: true)

          params = {voucher_type: "dynamic", web_postos: true}
          expect do
            put "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/voucher_type", params:, headers:, as: :json

            expect(response).to be_ok
          end.to change { promotion.reload.voucher_type }.from(nil).to("dynamic")
            .and change(promotion, :status).from("no_voucher").to("available")
            .and change(promotion, :provider).from(nil).to("dynamic")
            .and change(promotion, :dynamic_voucher).from(false).to(true)
            .and change(promotion, :code).from("ABC123").to(nil)
            .and change(promotion, :web_postos).from(false).to(true)
            .and change { promotion.cupons.pluck(:c_type) }.to(all(eq("DYNAMIC")))
            .and change(VoucherBucket, :count).by(1)
          expect(VoucherBucket.where(promotions: promotion).count).to eq(1)
        end
      end
    end

    describe "voucher_type fixed" do
      let!(:organization) { create(:organization) }
      let!(:organization_profile) { create(:organization_profile, organization:, business:) }
      let!(:branch) { create(:branch, organization:) }

      context "when existing promotion has bucket" do
        it "renders successful" do
          bucket = create(:voucher_bucket)
          promotion = create(
            :promotion,
            organization:,
            business:,
            dynamic_voucher: true,
            code: nil,
            voucher_type: nil,
            provider: nil,
            provider_type: "code",
            status: "no_voucher",
            voucher_bucket: bucket
          )
          Cupon.create!(promotion:, branch:, active: true)
          create(:voucher, bucket:)

          params = {voucher_type: "fixed", fixed_code: "ABC123", web_postos: true}
          expect do
            path = [
              "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/voucher_type",
              "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/voucher_type"
            ].sample
            put path, params:, headers:, as: :json

            expect(response).to be_ok
          end.to change { promotion.reload.voucher_type }.from(nil).to("fixed")
            .and change(promotion, :status).from("no_voucher").to("available")
            .and change(promotion, :provider).from(nil).to("fixed_code")
            .and change(promotion, :dynamic_voucher).from(true).to(false)
            .and change(promotion, :code).from(nil).to("ABC123")
            .and not_change(promotion, :web_postos)
            .and change { promotion.cupons.pluck(:c_type) }.to(all(eq("FIXED_CODE")))
            .and change(VoucherBucket, :count).by(0)
          expect(VoucherBucket.where(promotions: promotion).count).to eq(0)
        end
      end

      context "when existing promotion has no bucket" do
        it "renders successful and no use existing bucket" do
          another_bucket_promotion = create(:voucher_bucket)
          _another_promotion = create(:promotion, voucher_bucket: another_bucket_promotion, web_postos: true)
          promotion = create(
            :promotion,
            organization:,
            business:,
            dynamic_voucher: true,
            code: nil,
            voucher_type: nil,
            provider: nil,
            provider_type: "code",
            status: "no_voucher"
          )
          Cupon.create!(promotion:, branch:, active: true)

          params = {voucher_type: "fixed", fixed_code: "ABC123", web_postos: true}
          expect do
            put "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/voucher_type", params:, headers:, as: :json

            expect(response).to be_ok
          end.to change { promotion.reload.voucher_type }.from(nil).to("fixed")
            .and change(promotion, :status).from("no_voucher").to("available")
            .and change(promotion, :provider).from(nil).to("fixed_code")
            .and change(promotion, :dynamic_voucher).from(true).to(false)
            .and change(promotion, :code).from(nil).to("ABC123")
            .and not_change(promotion, :web_postos)
            .and change { promotion.cupons.pluck(:c_type) }.to(all(eq("FIXED_CODE")))
            .and not_change { promotion.voucher_bucket }
            .and change(VoucherBucket, :count).by(0)
          expect(VoucherBucket.where(promotions: promotion).count).to eq(0)
        end
      end

      it "renders error when setting voucher_type fixed and fixed_code is not sent" do
        organization = create(:organization)
        create(:organization_profile, organization:, business:)
        branch = create(:branch, organization:)
        bucket = create(:voucher_bucket)
        promotion = create(
          :promotion,
          organization:,
          business:,
          dynamic_voucher: true,
          code: nil,
          voucher_type: nil,
          provider: nil,
          provider_type: "code",
          status: "no_voucher",
          voucher_bucket: bucket
        )
        Cupon.create!(promotion:, branch:, active: true)
        create(:voucher, bucket: promotion.voucher_bucket)

        params = {voucher_type: "fixed"}
        expect do
          path = [
            "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/voucher_type",
            "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/voucher_type"
          ].sample
          put path, params:, headers:, as: :json

          expect(response).to be_precondition_failed
        end.to not_change { promotion.reload.voucher_type }
          .and not_change(promotion, :status)
          .and not_change(promotion, :provider)
          .and not_change(promotion, :dynamic_voucher)
          .and not_change(promotion, :code)
          .and not_change { promotion.cupons.pluck(:c_type) }
          .and not_change(promotion.voucher_bucket.vouchers, :count)
          .and change(VoucherBucket, :count).by(0)
      end
    end

    describe "voucher_type list" do
      let!(:organization) { create(:organization) }
      let!(:organization_profile) { create(:organization_profile, organization:, business:) }
      let!(:branch) { create(:branch, organization:) }

      context "when existing promotion has bucket" do
        it "renders successful" do
          bucket = create(:voucher_bucket)
          promotion = create(
            :promotion,
            organization:,
            business:,
            dynamic_voucher: true,
            code: "ABC123",
            voucher_type: nil,
            provider: nil,
            provider_type: "code",
            status: "no_voucher",
            voucher_bucket: bucket
          )
          Cupon.create!(promotion:, branch:, active: true)

          params = {voucher_type: "list", web_postos: false}
          expect do
            path = [
              "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/voucher_type",
              "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/voucher_type"
            ].sample
            put path, params:, headers:, as: :json

            expect(response).to be_ok
          end.to change { promotion.reload.voucher_type }.from(nil).to("list")
            .and not_change(promotion, :status)
            .and change(promotion, :provider).from(nil).to("coupon_code")
            .and change(promotion, :dynamic_voucher).from(true).to(false)
            .and change(promotion, :code).from("ABC123").to(nil)
            .and not_change(promotion, :web_postos)
            .and change { promotion.cupons.pluck(:c_type) }.to(all(eq("FIXED")))
            .and change(VoucherBucket, :count).by(0)
          expect(VoucherBucket.where(promotions: promotion).count).to eq(1)
        end
      end

      context "when existing promotion has no bucket" do
        it "renders successful and use existing bucket" do
          another_bucket_promotion = create(:voucher_bucket)
          _another_promotion = create(:promotion, voucher_bucket: another_bucket_promotion, web_postos: true)
          promotion = create(
            :promotion,
            organization:,
            business:,
            dynamic_voucher: true,
            code: "ABC123",
            voucher_type: nil,
            provider: nil,
            provider_type: "code",
            status: "no_voucher"
          )
          Cupon.create!(promotion:, branch:, active: true)

          params = {voucher_type: "list", web_postos: true}
          expect do
            path = [
              "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/voucher_type",
              "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/voucher_type"
            ].sample
            put path, params:, headers:, as: :json

            expect(response).to be_ok
          end.to change { promotion.reload.voucher_type }.from(nil).to("list")
            .and not_change(promotion, :status)
            .and change(promotion, :provider).from(nil).to("coupon_code")
            .and change(promotion, :dynamic_voucher).from(true).to(false)
            .and change(promotion, :code).from("ABC123").to(nil)
            .and change(promotion, :web_postos).from(false).to(true)
            .and change { promotion.cupons.pluck(:c_type) }.to(all(eq("FIXED")))
            .and change { promotion.voucher_bucket }.from(nil).to(another_bucket_promotion)
            .and not_change(VoucherBucket, :count)
          expect(VoucherBucket.where(promotions: promotion).count).to eq(1)
        end

        it "renders successful and create a bucket" do
          promotion = create(
            :promotion,
            organization:,
            business:,
            dynamic_voucher: true,
            code: "ABC123",
            voucher_type: nil,
            provider: nil,
            provider_type: "code",
            status: "no_voucher"
          )
          Cupon.create!(promotion:, branch:, active: true)

          params = {voucher_type: "list", web_postos: true}
          expect do
            path = [
              "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/voucher_type",
              "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/voucher_type"
            ].sample
            put path, params:, headers:, as: :json

            expect(response).to be_ok
          end.to change { promotion.reload.voucher_type }.from(nil).to("list")
            .and not_change(promotion, :status)
            .and change(promotion, :provider).from(nil).to("coupon_code")
            .and change(promotion, :dynamic_voucher).from(true).to(false)
            .and change(promotion, :code).from("ABC123").to(nil)
            .and change(promotion, :web_postos).from(false).to(true)
            .and change { promotion.cupons.pluck(:c_type) }.to(all(eq("FIXED")))
            .and change(VoucherBucket, :count).by(1)
          expect(VoucherBucket.where(promotions: promotion).count).to eq(1)
        end
      end
    end

    it "renders error when promotion is not exclusive" do
      organization = create(:organization)
      create(:organization_profile, organization:, business:)
      branch = create(:branch, organization:)
      promotion = create(
        :promotion,
        organization:,
        dynamic_voucher: true,
        code: "ABC123",
        voucher_type: nil,
        provider: nil,
        provider_type: "regionalized",
        status: "no_voucher"
      )
      Cupon.create!(promotion:, branch:, active: true)

      params = {voucher_type: "none"}
      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/voucher_type",
          "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/voucher_type"
        ].sample
        put path, params:, headers:, as: :json
      end.to not_change { promotion.reload.voucher_type }
        .and not_change(promotion, :status)
        .and not_change(promotion, :provider)
        .and not_change(promotion, :dynamic_voucher)
        .and not_change(promotion, :code)
        .and not_change { promotion.cupons.pluck(:c_type) }
        .and change(VoucherBucket, :count).by(0)
      expect(response).to be_not_found
      expect(promotion.vouchers.count).to eq(0)
    end

    it "renders error when business does not have organization profile" do
      organization = create(:organization)
      branch = create(:branch, organization:)
      voucher_bucket = create(:voucher_bucket)
      promotion = create(
        :promotion,
        organization:,
        business:,
        dynamic_voucher: true,
        code: "ABC123",
        voucher_type: nil,
        provider: nil,
        provider_type: "regionalized",
        status: "no_voucher",
        voucher_bucket:
      )
      Cupon.create!(promotion:, branch:, active: true)

      params = {voucher_type: "none"}
      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/voucher_type",
          "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/voucher_type"
        ].sample
        put path, params:, headers:, as: :json

        expect(response).to be_not_found
      end.to not_change { promotion.reload.voucher_type }
        .and not_change(promotion, :status)
        .and not_change(promotion, :provider)
        .and not_change(promotion, :dynamic_voucher)
        .and not_change(promotion, :code)
        .and not_change { promotion.cupons.pluck(:c_type) }
        .and change(VoucherBucket, :count).by(0)
      expect(promotion.vouchers.count).to eq(0)
    end

    it "renders error when promotion provider_type is not code or regionalized" do
      organization = create(:organization)
      create(:organization_profile, organization:, business:)
      branch = create(:branch, organization:)
      promotion = create(
        :promotion,
        organization:,
        business:,
        dynamic_voucher: true,
        code: "ABC123",
        voucher_type: nil,
        provider: nil,
        provider_type: (Promotion::ProviderType.all - Promotion::ProviderType.voucherable).sample,
        status: "no_voucher"
      )
      Cupon.create!(promotion:, branch:, active: true)

      params = {voucher_type: "none"}
      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/voucher_type",
          "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/voucher_type"
        ].sample
        put path, params:, headers:, as: :json

        expect(response).to be_not_found
      end.to not_change { promotion.reload.voucher_type }
        .and not_change(promotion, :status)
        .and not_change(promotion, :provider)
        .and not_change(promotion, :dynamic_voucher)
        .and not_change(promotion, :code)
        .and not_change { promotion.cupons.pluck(:c_type) }
        .and change(VoucherBucket, :count).by(0)
      expect(promotion.vouchers.count).to eq(0)
    end

    it "renders error when organization is not active" do
      organization = create(:organization, active: false)
      create(:organization_profile, organization:, business:)
      branch = create(:branch, organization:)
      bucket = create(:voucher_bucket)
      promotion = create(
        :promotion,
        organization:,
        business:,
        dynamic_voucher: true,
        code: "ABC123",
        voucher_type: nil,
        provider: nil,
        provider_type: "regionalized",
        status: "no_voucher",
        voucher_bucket: bucket
      )
      Cupon.create!(promotion:, branch:, active: true)

      params = {voucher_type: "none"}
      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/voucher_type",
          "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/voucher_type"
        ].sample
        put path, params:, headers:, as: :json

        expect(response).to be_not_found
      end.to not_change { promotion.reload.voucher_type }
        .and not_change(promotion, :status)
        .and not_change(promotion, :provider)
        .and not_change(promotion, :dynamic_voucher)
        .and not_change(promotion, :code)
        .and not_change { promotion.cupons.pluck(:c_type) }
        .and change(VoucherBucket, :count).by(0)
      expect(promotion.vouchers.count).to eq(0)
    end

    it "renders error when promotion is inactive" do
      organization = create(:organization)
      create(:organization_profile, organization:, business:)
      branch = create(:branch, organization:)
      bucket = create(:voucher_bucket)
      promotion = create(
        :promotion,
        organization:,
        business:,
        dynamic_voucher: true,
        code: "ABC123",
        voucher_type: nil,
        provider: nil,
        provider_type: "regionalized",
        status: "no_voucher",
        voucher_bucket: bucket
      )
      Cupon.create!(promotion:, branch:, active: true)

      promotion.status_inactive!

      params = {voucher_type: "none"}
      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/voucher_type",
          "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/voucher_type"
        ].sample
        put path, params:, headers:, as: :json

        expect(response).to be_not_found
        promotion.reload
      end.to not_change(promotion, :voucher_type)
        .and not_change(promotion, :status)
        .and not_change(promotion, :provider)
        .and not_change(promotion, :dynamic_voucher)
        .and not_change(promotion, :code)
        .and change(VoucherBucket, :count).by(0)
      expect(promotion.vouchers.count).to eq(0)
    end
  end
end
