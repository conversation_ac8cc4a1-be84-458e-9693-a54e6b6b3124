require "rails_helper"

RSpec.describe Client::V2::Organizations::Promotions::WorkScheduleController, type: :request do
  describe "PUT /client/v2/businesses/:business_id/organizations/:organization_id/promotions/:promotion_id/work_schedule" do
    let(:business) { create(:business) }
    let!(:client_employee) { create(:client_employee, :admin_lecupon) }
    let(:headers) do
      {
        "X-ClientEmployee-Email": client_employee.email,
        "X-ClientEmployee-Token": client_employee.authentication_token,
        "Tenant-id": business.cnpj
      }
    end

    it "renders successful" do
      organization = create(:organization)
      create(:organization_profile, organization:, business:)
      branch = create(:branch, organization:)
      bucket = create(:voucher_bucket)
      promotion = create(
        :promotion,
        organization:,
        business:,
        start_date: 1.year.ago,
        end_date: 1.week.ago,
        provider: nil,
        provider_type: "code",
        code: "ABC123",
        status: "expired",
        monday: nil,
        tuesday: nil,
        wednesday: nil,
        thursday: nil,
        friday: nil,
        saturday: nil,
        sunday: nil,
        voucher_bucket: bucket
      )
      coupon = create(
        :cupon,
        refresh_promotion_availability: false,
        promotion:,
        branch:,
        business:,
        start_date: 1.year.ago,
        end_date: 1.week.ago,
        active: true,
        monday: nil,
        tuesday: nil,
        wednesday: nil,
        thursday: nil,
        friday: nil,
        saturday: nil,
        sunday: nil
      )

      params = {
        weekdays: ["monday", "wednesday", "friday"],
        start_date: 1.day.ago.to_date,
        end_date: 1.year.from_now.to_date,
        start_hour: "08:00",
        end_hour: "18:00"
      }
      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/work_schedule",
          "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/work_schedule"
        ].sample
        put path, params:, headers:, as: :json
      end.to change { promotion.reload.start_date.to_date }.to(params[:start_date])
        .and change { promotion.end_date.to_date }.to(params[:end_date])
        .and change(promotion, :monday).to(true)
        .and change(promotion, :tuesday).to(false)
        .and change(promotion, :wednesday).to(true)
        .and change(promotion, :thursday).to(false)
        .and change(promotion, :friday).to(true)
        .and change(promotion, :saturday).to(false)
        .and change(promotion, :sunday).to(false)
        .and change(promotion, :status).from("expired").to("available")
        .and change(promotion, :provider).from(nil).to("coupon_code")
        .and change { coupon.reload.start_date.to_date }.to(params[:start_date])
        .and change { coupon.end_date.to_date }.to(params[:end_date])
        .and change(coupon, :monday).to(true)
        .and change(coupon, :tuesday).to(false)
        .and change(coupon, :wednesday).to(true)
        .and change(coupon, :thursday).to(false)
        .and change(coupon, :friday).to(true)
        .and change(coupon, :saturday).to(false)
        .and change(coupon, :sunday).to(false)
        .and change(coupon, :c_type).to("FIXED")

      expect(response).to be_ok
    end

    it "renders successful" do
      organization = create(:organization)
      create(:organization_profile, organization:, business:)
      branch = create(:branch, organization:)
      bucket = create(:voucher_bucket)
      promotion = create(
        :promotion,
        organization:,
        business:,
        start_date: 1.year.ago,
        end_date: 1.week.ago,
        provider: nil,
        provider_type: "code",
        code: "ABC123",
        status: "expired",
        monday: nil,
        tuesday: nil,
        wednesday: nil,
        thursday: nil,
        friday: nil,
        saturday: nil,
        sunday: nil,
        voucher_bucket: bucket
      )
      coupon = create(
        :cupon,
        promotion:,
        branch:,
        business:,
        start_date: 1.year.ago,
        end_date: 1.week.ago,
        active: true,
        monday: nil,
        tuesday: nil,
        wednesday: nil,
        thursday: nil,
        friday: nil,
        saturday: nil,
        sunday: nil
      )

      param_not_sent = [:weekdays, :start_date, :end_date, :start_hour, :end_hour].sample
      params = {
        weekdays: ["monday", "wednesday", "friday"],
        start_date: 1.day.ago.to_date,
        end_date: 1.year.from_now.to_date,
        start_hour: "08:00",
        end_hour: "18:00"
      }.except(param_not_sent)

      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/work_schedule",
          "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/work_schedule"
        ].sample
        put path, params:, headers:, as: :json
      end.to not_change { promotion.reload.start_date.to_date }
        .and not_change { promotion.end_date.to_date }
        .and not_change(promotion, :monday)
        .and not_change(promotion, :tuesday)
        .and not_change(promotion, :wednesday)
        .and not_change(promotion, :thursday)
        .and not_change(promotion, :friday)
        .and not_change(promotion, :saturday)
        .and not_change(promotion, :sunday)
        .and not_change(promotion, :status)
        .and not_change(promotion, :provider)
        .and not_change { coupon.reload.start_date.to_date }
        .and not_change { coupon.end_date.to_date }
        .and not_change(coupon, :monday)
        .and not_change(coupon, :tuesday)
        .and not_change(coupon, :wednesday)
        .and not_change(coupon, :thursday)
        .and not_change(coupon, :friday)
        .and not_change(coupon, :saturday)
        .and not_change(coupon, :sunday)
        .and not_change(coupon, :c_type)

      expect(response).to be_precondition_failed
      expect(response_hash["error"]).to eq("A requisição falhou devido a ausência de parâmetro: #{param_not_sent}")
    end

    it "renders error when promotion is not exclusive" do
      organization = create(:organization)
      create(:organization_profile, organization:, business:)
      branch = create(:branch, organization:)
      promotion = create(
        :promotion,
        organization:,
        start_date: 1.year.ago,
        end_date: 1.week.ago,
        provider: nil,
        provider_type: "code",
        code: "ABC123",
        status: "expired",
        monday: nil,
        tuesday: nil,
        wednesday: nil,
        thursday: nil,
        friday: nil,
        saturday: nil,
        sunday: nil
      )
      coupon = create(
        :cupon,
        promotion:,
        branch:,
        start_date: 1.year.ago,
        end_date: 1.week.ago,
        active: true,
        monday: nil,
        tuesday: nil,
        wednesday: nil,
        thursday: nil,
        friday: nil,
        saturday: nil,
        sunday: nil
      )

      params = {
        weekdays: ["monday", "wednesday", "friday"],
        start_date: 1.day.ago.to_date,
        end_date: 1.year.from_now.to_date,
        start_hour: "08:00",
        end_hour: "18:00"
      }
      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/work_schedule",
          "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/work_schedule"
        ].sample
        put path, params:, headers:, as: :json
      end.to not_change { promotion.reload.start_date.to_date }
        .and not_change { promotion.end_date.to_date }
        .and not_change(promotion, :monday)
        .and not_change(promotion, :tuesday)
        .and not_change(promotion, :wednesday)
        .and not_change(promotion, :thursday)
        .and not_change(promotion, :friday)
        .and not_change(promotion, :saturday)
        .and not_change(promotion, :sunday)
        .and not_change(promotion, :status)
        .and not_change(promotion, :provider)
        .and not_change { coupon.reload.start_date.to_date }
        .and not_change { coupon.end_date.to_date }
        .and not_change(coupon, :monday)
        .and not_change(coupon, :tuesday)
        .and not_change(coupon, :wednesday)
        .and not_change(coupon, :thursday)
        .and not_change(coupon, :friday)
        .and not_change(coupon, :saturday)
        .and not_change(coupon, :sunday)
        .and not_change(coupon, :c_type)

      expect(response).to be_not_found
    end

    it "renders error when business does not have organization profile" do
      organization = create(:organization)
      branch = create(:branch, organization:)
      promotion = create(
        :promotion,
        organization:,
        business:,
        start_date: 1.year.ago,
        end_date: 1.week.ago,
        provider: nil,
        provider_type: "code",
        code: "ABC123",
        status: "expired",
        monday: nil,
        tuesday: nil,
        wednesday: nil,
        thursday: nil,
        friday: nil,
        saturday: nil,
        sunday: nil
      )
      coupon = create(
        :cupon,
        promotion:,
        business:,
        branch:,
        start_date: 1.year.ago,
        end_date: 1.week.ago,
        active: true,
        monday: nil,
        tuesday: nil,
        wednesday: nil,
        thursday: nil,
        friday: nil,
        saturday: nil,
        sunday: nil
      )

      params = {
        weekdays: ["monday", "wednesday", "friday"],
        start_date: 1.day.ago.to_date,
        end_date: 1.year.from_now.to_date,
        start_hour: "08:00",
        end_hour: "18:00"
      }
      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/work_schedule",
          "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/work_schedule"
        ].sample
        put path, params:, headers:, as: :json
      end.to not_change { promotion.reload.start_date.to_date }
        .and not_change { promotion.end_date.to_date }
        .and not_change(promotion, :monday)
        .and not_change(promotion, :tuesday)
        .and not_change(promotion, :wednesday)
        .and not_change(promotion, :thursday)
        .and not_change(promotion, :friday)
        .and not_change(promotion, :saturday)
        .and not_change(promotion, :sunday)
        .and not_change(promotion, :status)
        .and not_change(promotion, :provider)
        .and not_change { coupon.reload.start_date.to_date }
        .and not_change { coupon.end_date.to_date }
        .and not_change(coupon, :monday)
        .and not_change(coupon, :tuesday)
        .and not_change(coupon, :wednesday)
        .and not_change(coupon, :thursday)
        .and not_change(coupon, :friday)
        .and not_change(coupon, :saturday)
        .and not_change(coupon, :sunday)
        .and not_change(coupon, :c_type)

      expect(response).to be_not_found
    end

    it "renders error when organization is not active" do
      organization = create(:organization, active: false)
      create(:organization_profile, organization:, business:)
      branch = create(:branch, organization:)
      promotion = create(
        :promotion,
        organization:,
        business:,
        start_date: 1.year.ago,
        end_date: 1.week.ago,
        provider: nil,
        provider_type: "code",
        code: "ABC123",
        status: "expired",
        monday: nil,
        tuesday: nil,
        wednesday: nil,
        thursday: nil,
        friday: nil,
        saturday: nil,
        sunday: nil
      )
      coupon = create(
        :cupon,
        promotion:,
        business:,
        branch:,
        start_date: 1.year.ago,
        end_date: 1.week.ago,
        active: true,
        monday: nil,
        tuesday: nil,
        wednesday: nil,
        thursday: nil,
        friday: nil,
        saturday: nil,
        sunday: nil
      )

      params = {
        weekdays: ["monday", "wednesday", "friday"],
        start_date: 1.day.ago.to_date,
        end_date: 1.year.from_now.to_date,
        start_hour: "08:00",
        end_hour: "18:00"
      }
      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/work_schedule",
          "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/work_schedule"
        ].sample
        put path, params:, headers:, as: :json
      end.to not_change { promotion.reload.start_date.to_date }
        .and not_change { promotion.end_date.to_date }
        .and not_change(promotion, :monday)
        .and not_change(promotion, :tuesday)
        .and not_change(promotion, :wednesday)
        .and not_change(promotion, :thursday)
        .and not_change(promotion, :friday)
        .and not_change(promotion, :saturday)
        .and not_change(promotion, :sunday)
        .and not_change(promotion, :status)
        .and not_change(promotion, :provider)
        .and not_change { coupon.reload.start_date.to_date }
        .and not_change { coupon.end_date.to_date }
        .and not_change(coupon, :monday)
        .and not_change(coupon, :tuesday)
        .and not_change(coupon, :wednesday)
        .and not_change(coupon, :thursday)
        .and not_change(coupon, :friday)
        .and not_change(coupon, :saturday)
        .and not_change(coupon, :sunday)
        .and not_change(coupon, :c_type)

      expect(response).to be_not_found
    end

    it "renders error when promotion is inactive" do
      organization = create(:organization)
      create(:organization_profile, organization:, business:)
      branch = create(:branch, organization:)
      promotion = create(
        :promotion,
        organization:,
        business:,
        start_date: 1.year.ago,
        end_date: 1.week.ago,
        provider: nil,
        provider_type: "code",
        code: "ABC123",
        status: "expired",
        monday: nil,
        tuesday: nil,
        wednesday: nil,
        thursday: nil,
        friday: nil,
        saturday: nil,
        sunday: nil
      )
      coupon = create(
        :cupon,
        refresh_promotion_availability: false,
        promotion:,
        branch:,
        business:,
        start_date: 1.year.ago,
        end_date: 1.week.ago,
        active: true,
        monday: nil,
        tuesday: nil,
        wednesday: nil,
        thursday: nil,
        friday: nil,
        saturday: nil,
        sunday: nil
      )

      promotion.status_inactive!

      params = {
        weekdays: ["monday", "wednesday", "friday"],
        start_date: 1.day.ago.to_date,
        end_date: 1.year.from_now.to_date,
        start_hour: "08:00",
        end_hour: "18:00"
      }
      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/work_schedule",
          "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/work_schedule"
        ].sample
        put path, params:, headers:, as: :json
        promotion.reload
        coupon.reload
      end.to not_change { promotion.start_date.to_date }
        .and not_change { promotion.end_date.to_date }
        .and not_change(promotion, :monday)
        .and not_change(promotion, :tuesday)
        .and not_change(promotion, :wednesday)
        .and not_change(promotion, :thursday)
        .and not_change(promotion, :friday)
        .and not_change(promotion, :saturday)
        .and not_change(promotion, :sunday)
        .and not_change(promotion, :status)
        .and not_change(promotion, :provider)
        .and not_change { coupon.start_date.to_date }
        .and not_change { coupon.end_date.to_date }
        .and not_change(coupon, :monday)
        .and not_change(coupon, :tuesday)
        .and not_change(coupon, :wednesday)
        .and not_change(coupon, :thursday)
        .and not_change(coupon, :friday)
        .and not_change(coupon, :saturday)
        .and not_change(coupon, :sunday)
        .and not_change(coupon, :c_type)

      expect(response).to be_not_found
    end
  end

  def response_hash
    JSON.parse(response.body)
  end
end
