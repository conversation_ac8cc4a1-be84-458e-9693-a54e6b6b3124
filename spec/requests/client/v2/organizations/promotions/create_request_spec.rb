require "rails_helper"

RSpec.describe Client::V2::Organizations::PromotionsController, type: :request do
  describe "#create" do
    let(:business) { create(:business) }
    let!(:client_employee) { create(:client_employee, :admin_lecupon) }
    let(:headers) do
      {
        "X-ClientEmployee-Email": client_employee.email,
        "X-ClientEmployee-Token": client_employee.authentication_token,
        "Tenant-id": business.cnpj
      }
    end
    let(:serializer_keys) do
      %w[
        id
        description
        discount_value
        discount_type
        end_date
        end_hour
        provider_type
        start_date
        start_hour
        status
        title
        web_postos
      ]
    end

    it "renders created and creates exclusive promotion for branches" do
      organization = create(:organization)
      create(:organization_profile, organization:, business:)
      branches = create_list(:branch, 3, :physical, organization:)
      branch_ids = branches.map(&:id)
      params = {branch_ids:}

      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions",
          "/client/v2/organizations/#{organization.id}/promotions"
        ].sample
        post path, params:, headers:, as: :json
      end.to change(Promotion, :count).by(1)
        .and change(Cupon, :count).by(branches.size)

      expect(response).to be_created
      expect(response_hash.keys).to match_array(serializer_keys)
      promotion = Promotion.last
      expect(promotion.business).to eq(business)
      expect(promotion.organization).to eq(organization)
      expect(promotion.source).to eq("business")
      expect(promotion.cupons.pluck(:business_id, :organization_id)).to all(eq([business.id, organization.id]))
      expect(promotion.cupons.pluck(:branch_id)).to match_array(branch_ids)
    end

    it "renders error when branch_ids is not passed" do
      organization = create(:organization)
      create(:organization_profile, organization:, business:)
      params = {branch_ids: nil}

      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions",
          "/client/v2/organizations/#{organization.id}/promotions"
        ].sample
        post path, params:, headers:, as: :json
      end.to change(Promotion, :count).by(0)
        .and change(Cupon, :count).by(0)

      expect(response).to be_precondition_failed
    end

    it "renders not found when business does not have organization profile" do
      organization = create(:organization)
      branches = create_list(:branch, 3, organization:)

      params = {branch_ids: branches.map(&:id)}

      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions",
          "/client/v2/organizations/#{organization.id}/promotions"
        ].sample
        post path, params:, headers:, as: :json
      end.to change(Promotion, :count).by(0)
        .and change(Branch, :count).by(0)
        .and change(Cupon, :count).by(0)

      expect(response).to be_not_found
    end
  end

  def response_hash
    JSON.parse(response.body)
  end
end
