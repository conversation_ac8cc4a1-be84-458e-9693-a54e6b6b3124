require "rails_helper"

RSpec.describe Client::V2::Organizations::PromotionsController, type: :request do
  let(:business) { create(:business) }
  let!(:client_employee) { create(:client_employee, :admin_lecupon) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  let(:json_parse_response_body) { JSON.parse(response.body) }

  describe "#destroy" do
    let(:available_promotion) { create(:promotion, organization: active_organization) }
    let(:available_branch) { create(:branch, organization: active_organization) }
    let(:available_cupon) { create(:cupon, :cpf, branch: available_branch, promotion: available_promotion, organization: active_organization) }
    let(:blocklisted_organization_coupon) { create(:promotion, organization: blocklisted_organization) }

    context "given an active organization" do
      let(:active_organization) do
        organization = create(:organization, active: true)
        create(:organization_profile, organization:, business:)

        organization
      end

      context "when the promotion is from the current business" do
        let(:create_promotion) { Promotion.create!(provider_type: "online", source: "business", organization: active_organization, business:) }

        it "is expected to be inactivated" do
          promotion = create_promotion

          expect do
            path = [
              "/client/v2/businesses/#{business.cnpj}/organizations/#{active_organization.id}/promotions/#{promotion.id}",
              "/client/v2/organizations/#{active_organization.id}/promotions/#{promotion.id}"
            ].sample
            delete path, headers:, as: :json

            promotion.reload
          end.to change(promotion, :status).from("pending").to("inactive")
          expect(response).to have_http_status(:ok)
        end

        it "is expected update data by refreshers" do
          organization = create(:organization, :percent_highest_discount, active: true, highest_discount_value: 10)
          organization_profile = create(:organization_profile, :percent_highest_discount, organization:, business:)
          _promotion_one = Promotion.create!(
            provider_type: "online",
            source: "business",
            organization:,
            discount_type: Promotion::DiscountType::PERCENT,
            discount_value: 5,
            status: "available"
          )
          promotion_two = Promotion.create!(
            provider_type: "online",
            source: "business",
            organization:,
            business:,
            discount_type: Promotion::DiscountType::PERCENT,
            discount_value: 10,
            status: "available"
          )

          expect do
            path = [
              "/client/v2/businesses/#{business.cnpj}/organizations/#{organization.id}/promotions/#{promotion_two.id}",
              "/client/v2/organizations/#{organization.id}/promotions/#{promotion_two.id}"
            ].sample
            delete path, headers:, as: :json

            [promotion_two, organization, organization_profile].map(&:reload)
          end.to change(promotion_two, :status).from("available").to("inactive")
            .and change(organization, :highest_discount_value).from(10.0).to(5.0)
            .and change(organization_profile, :highest_discount_value).from(30.0).to(nil)
          expect(response).to have_http_status(:ok)
        end

        context "when business is inactive" do
          let(:create_promotion) { Promotion.create!(provider_type: "online", source: "business", organization: active_organization, business:) }

          specify do
            business.update!(status: Business::Status::SUSPENDED_BY_OVERDUE)
            promotion = create_promotion

            path = [
              "/client/v2/businesses/#{business.cnpj}/organizations/#{active_organization.id}/promotions/#{promotion.id}",
              "/client/v2/organizations/#{active_organization.id}/promotions/#{promotion.id}"
            ].sample
            delete path, headers:, as: :json

            expect(response).to have_http_status(:not_found)
          end
        end
      end

      context "given promotion from an another business" do
        let(:another_business) { create(:business) }
        let(:active_organization) do
          organization = create(:organization, active: true)
          create(:organization_profile, organization:, business:)

          organization
        end
        let(:create_promotion) { Promotion.create!(source: "business", organization: active_organization, business: another_business) }

        specify do
          promotion = create_promotion

          path = [
            "/client/v2/businesses/#{business.cnpj}/organizations/#{active_organization.id}/promotions/#{promotion.id}",
            "/client/v2/organizations/#{active_organization.id}/promotions/#{promotion.id}"
          ].sample
          delete path, headers:, as: :json

          expect(response).to have_http_status(:not_found)
        end
      end
    end

    context "given a blocklisted organization" do
      let(:blocklisted_organization) do
        organization = create(:organization, active: true)
        create(:organization_profile, organization:, business:)
        create(:organization_blocklist, business:, organization:)

        organization
      end
      let(:create_promotion) { Promotion.create!(source: "business", organization: blocklisted_organization, business:) }

      it "is expected to be inactivated" do
        promotion = create_promotion

        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/organizations/#{blocklisted_organization.id}/promotions/#{promotion.id}",
            "/client/v2/organizations/#{blocklisted_organization.id}/promotions/#{promotion.id}"
          ].sample
          delete path, headers:, as: :json

          promotion.reload
        end.to change(promotion, :status).from("pending").to("inactive")
        expect(response).to have_http_status(:ok)
      end
    end

    context "given a inactive organization" do
      let(:inactive_organization) do
        organization = create(:organization, active: false)
        create(:organization_profile, organization:, business:)

        organization
      end
      let(:create_promotion) { Promotion.create!(source: "business", organization: inactive_organization, business:) }

      specify do
        promotion = create_promotion

        path = [
          "/client/v2/businesses/#{business.cnpj}/organizations/#{inactive_organization.id}/promotions/#{promotion.id}",
          "/client/v2/organizations/#{inactive_organization.id}/promotions/#{promotion.id}"
        ].sample
        delete path, headers:, as: :json
        delete("/client/v2/businesses/#{business.cnpj}/organizations/#{inactive_organization.id}/promotions/#{promotion.id}",
          as: :json,
          headers:)

        expect(response).to have_http_status(:not_found)
      end
    end

    context "when organization does not have profile" do
      let(:active_organization_without_profile) { create(:organization, active: true) }
      let(:create_promotion) { Promotion.create!(source: "business", organization: active_organization_without_profile, business:) }

      specify do
        promotion = create_promotion

        path = [
          "/client/v2/businesses/#{business.cnpj}/organizations/#{active_organization_without_profile.id}/promotions/#{promotion.id}",
          "/client/v2/organizations/#{active_organization_without_profile.id}/promotions/#{promotion.id}"
        ].sample
        delete path, headers:, as: :json

        expect(response).to have_http_status(:not_found)
      end
    end
  end
end
