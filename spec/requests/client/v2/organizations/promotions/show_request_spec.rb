require "rails_helper"

RSpec.describe Client::V2::Organizations::PromotionsController, type: :request do
  let!(:business) { create(:business) }
  let!(:client_employee) { create(:client_employee, :admin_lecupon) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  let(:json_parse_response_body) { JSON.parse(response.body) }

  describe "#show" do
    let(:promotion_serializer_keys) {
      %w[
        id
        average_ticket
        cashback_rules
        cashback_type
        cashback_value
        closed_interval
        cumulative
        discount_type
        discount_value
        description
        end_date
        end_hour
        fixed_code
        frequency_in_days
        picture
        provider_type
        quantity
        redeem_limited
        redeemable_hours
        redeems_per_cpf
        rules
        segment
        start_date
        start_hour
        status
        tags
        title
        url
        usage_instruction
        info
        voucher_type
        web_postos
      ]
    }
    let(:redeemable_hours_serializer_keys) { %w[end_hour start_hour week_day] }
    let(:picture_serializer_keys) { %w[large small] }
    let(:info_serializer_keys) { %w[closed_interval cumulative frequency_of_redemptions number_of_redemptions] }

    context "given a active organization" do
      let(:active_organization) do
        organization = create(:organization, active: true)
        create(:organization_profile, organization:, business:)

        organization
      end

      context "when promotion is available" do
        let(:create_promotion) do
          available_promotion = create(:promotion, :percent_discount, organization: active_organization, business:, closed_interval: true, tags: ["Tag 1"])
          available_branch = create(:branch, :online, organization: active_organization)
          create(:cupon, :cpf, branch: available_branch, promotion: available_promotion, organization: active_organization)

          available_promotion
        end

        it "is expected response to be ok" do
          promotion = create_promotion

          path = [
            "/client/v2/businesses/#{business.cnpj}/organizations/#{active_organization.id}/promotions/#{promotion.id}",
            "/client/v2/organizations/#{active_organization.id}/promotions/#{promotion.id}"
          ].sample
          get path, headers:, as: :json

          expect(response).to have_http_status(:ok)
          expect(json_parse_response_body.keys).to match_array(promotion_serializer_keys)
          expect(json_parse_response_body["discount_value"]).to eq(30)
          expect(json_parse_response_body["redeemable_hours"].first.keys).to match_array(redeemable_hours_serializer_keys)
          expect(json_parse_response_body["picture"].keys).to match_array(picture_serializer_keys)
          expect(json_parse_response_body["info"].keys).to match_array(info_serializer_keys)
          expect(json_parse_response_body["tags"]).to match_array(["Tag 1"])
        end
      end

      context "when promotion has only provider_type" do
        let(:create_promotion) { Promotion.create!(provider_type: "code", source: "business", organization: active_organization, business:) }

        it "is expected response to be ok" do
          promotion = create_promotion

          path = [
            "/client/v2/businesses/#{business.cnpj}/organizations/#{active_organization.id}/promotions/#{promotion.id}",
            "/client/v2/organizations/#{active_organization.id}/promotions/#{promotion.id}"
          ].sample
          get path, headers:, as: :json

          expect(response).to have_http_status(:ok)
          expect(json_parse_response_body.keys).to match_array(promotion_serializer_keys)
          expect(json_parse_response_body["redeemable_hours"]).to be_empty
        end
      end

      context "when business is inactive" do
        let(:create_promotion) { Promotion.create!(provider_type: "online", source: "business", organization: active_organization, business:) }

        specify do
          business.update!(status: Business::Status::SUSPENDED_BY_OVERDUE)
          promotion = create_promotion

          path = [
            "/client/v2/businesses/#{business.cnpj}/organizations/#{active_organization.id}/promotions/#{promotion.id}",
            "/client/v2/organizations/#{active_organization.id}/promotions/#{promotion.id}"
          ].sample
          get path, headers:, as: :json

          expect(response).to have_http_status(:not_found)
        end
      end
    end

    context "given a blocklisted organization" do
      let(:blocklisted_organization) do
        organization = create(:organization)
        create(:organization_profile, organization:, business:)
        create(:organization_blocklist, business:, organization:)

        organization
      end
      let(:create_promotion) { Promotion.create!(provider_type: "online", source: "business", organization: blocklisted_organization, business:) }

      it "is expected response to be ok" do
        promotion = create_promotion

        path = [
          "/client/v2/businesses/#{business.cnpj}/organizations/#{blocklisted_organization.id}/promotions/#{promotion.id}",
          "/client/v2/organizations/#{blocklisted_organization.id}/promotions/#{promotion.id}"
        ].sample
        get path, headers:, as: :json

        expect(response).to have_http_status(:ok)
      end
    end

    context "given a inactive organization" do
      let(:inactive_organization) do
        organization = create(:organization, active: false)
        create(:organization_profile, organization:, business:)

        organization
      end
      let(:create_promotion) { Promotion.create!(provider_type: "online", source: "business", organization: inactive_organization, business:) }

      specify do
        promotion = create_promotion

        path = [
          "/client/v2/businesses/#{business.cnpj}/organizations/#{inactive_organization.id}/promotions/#{promotion.id}",
          "/client/v2/organizations/#{inactive_organization.id}/promotions/#{promotion.id}"
        ].sample
        get path, headers:, as: :json

        expect(response).to have_http_status(:not_found)
      end
    end

    context "given promotion from an another business" do
      let(:another_business) { create(:business) }
      let(:active_organization) { create(:organization, active: true) }
      let(:create_promotion) { Promotion.create!(provider_type: "online", source: "business", organization: active_organization, business: another_business) }

      specify do
        promotion = create_promotion

        path = [
          "/client/v2/businesses/#{business.cnpj}/organizations/#{active_organization.id}/promotions/#{promotion.id}",
          "/client/v2/organizations/#{active_organization.id}/promotions/#{promotion.id}"
        ].sample
        get path, headers:, as: :json

        expect(response).to have_http_status(:not_found)
      end
    end

    context "when promotion is non exclusive" do
      let!(:organization) { create(:organization) }
      let!(:organization_profile) { create(:organization_profile, organization:, business:) }
      let!(:promotion) { create(:promotion, provider_type: :online, source: :business, business:, organization:) }

      it "renders ok" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/organizations/#{organization.id}/promotions/#{promotion.id}",
          "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}"
        ].sample
        get path, headers:, as: :json

        expect(response).to be_ok
        expect(json_parse_response_body.keys).to match_array(promotion_serializer_keys)
        expect(json_parse_response_body["id"]).to eq(promotion.id)
      end
    end

    context "when organization does not have profile" do
      let(:active_organization_without_profile) { create(:organization, active: true) }
      let(:create_promotion) { Promotion.create!(source: "business", organization: active_organization_without_profile, business:) }

      specify do
        promotion = create_promotion
        params = {provider_type: "cpf"}

        path = [
          "/client/v2/businesses/#{business.cnpj}/organizations/#{active_organization_without_profile.id}/promotions/#{promotion.id}",
          "/client/v2/organizations/#{active_organization_without_profile.id}/promotions/#{promotion.id}"
        ].sample
        get path, headers:, params:, as: :json

        expect(response).to have_http_status(:not_found)
      end
    end
  end
end
