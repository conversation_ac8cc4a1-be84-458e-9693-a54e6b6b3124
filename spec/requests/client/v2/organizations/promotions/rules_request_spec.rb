require "rails_helper"

RSpec.describe Client::V2::Organizations::Promotions::RulesController, type: :request do
  describe "PUT /client/v2/businesses/:business_id/organizations/:organization_id/promotions/:promotion_id/rules" do
    let(:business) { create(:business) }
    let!(:client_employee) { create(:client_employee, :admin_lecupon) }
    let(:headers) do
      {
        "X-ClientEmployee-Email": client_employee.email,
        "X-ClientEmployee-Token": client_employee.authentication_token,
        "Tenant-id": business.cnpj
      }
    end

    it "renders successful" do
      organization = create(:organization)
      create(:organization_profile, organization:, business:)
      branch = create(:branch, organization:)
      promotion = create(
        :promotion,
        organization:,
        business:,
        provider: nil,
        provider_type: "code",
        code: "ABC123",
        status: "pending",
        not_cumulative: nil,
        redeems_per_cpf: nil,
        frequency_in_days: 0,
        closed_interval: true,
        rules: nil,
        cashback_rules: nil,
        tags: ["Old Tag"]
      )
      coupon = create(
        :cupon,
        promotion:,
        branch:,
        business:,
        active: true,
        not_cumulative: nil,
        redeems_per_cpf: nil,
        frequency_in_days: 0,
        rules: nil,
        cashback_rules: nil
      )

      params = {
        cumulative: true,
        redeems_per_cpf: 999,
        frequency_in_days: 1,
        closed_interval: false,
        rules: FFaker::LoremBR.phrase,
        cashback_rules: FFaker::LoremBR.phrase,
        tags: ["New Tag 1", "New Tag 2"]
      }
      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/rules",
          "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/rules"
        ].sample
        put path, params:, headers:, as: :json
      end.to change { promotion.reload.not_cumulative }.to(false)
        .and change(promotion, :redeems_per_cpf).to(params[:redeems_per_cpf])
        .and change(promotion, :frequency_in_days).to(params[:frequency_in_days])
        .and change(promotion, :closed_interval).to(params[:closed_interval])
        .and change(promotion, :rules).to(params[:rules])
        .and change(promotion, :cashback_rules).to(params[:cashback_rules])
        .and change(promotion, :status).from("pending").to("available")
        .and change(promotion, :provider).from(nil).to("fixed_code")
        .and change(promotion, :tags).from(["Old Tag"]).to(["New Tag 1", "New Tag 2"])
        .and change(Tag, :count).to(3)
        .and change(Tagging, :count).from(1).to(2)
        .and change { coupon.reload.not_cumulative }.to(false)
        .and change(coupon, :redeems_per_cpf).to(params[:redeems_per_cpf])
        .and change(coupon, :frequency_in_days).to(params[:frequency_in_days])
        .and change(coupon, :rules).to(params[:rules])
        .and change(coupon, :cashback_rules).to(params[:cashback_rules])
        .and change(coupon, :c_type).to("FIXED_CODE")

      expect(response).to be_ok
    end

    it "renders error when required param is not sent" do
      organization = create(:organization)
      create(:organization_profile, organization:, business:)
      branch = create(:branch, organization:)
      promotion = create(
        :promotion,
        organization:,
        business:,
        provider: nil,
        provider_type: "code",
        code: "ABC123",
        status: "pending",
        not_cumulative: nil,
        redeems_per_cpf: nil,
        frequency_in_days: 0,
        closed_interval: true,
        rules: nil,
        cashback_rules: nil
      )
      coupon = create(
        :cupon,
        promotion:,
        branch:,
        business:,
        active: true,
        not_cumulative: nil,
        redeems_per_cpf: nil,
        frequency_in_days: 0,
        rules: nil,
        cashback_rules: nil
      )

      param_not_sent = [:cumulative, :redeems_per_cpf, :frequency_in_days, :closed_interval].sample
      params = {
        cumulative: true,
        redeems_per_cpf: 999,
        frequency_in_days: 1,
        closed_interval: false,
        rules: FFaker::LoremBR.phrase,
        cashback_rules: FFaker::LoremBR.phrase
      }.except(param_not_sent)

      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/rules",
          "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/rules"
        ].sample
        put path, params:, headers:, as: :json
      end.to not_change { promotion.reload.not_cumulative }
        .and not_change(promotion, :redeems_per_cpf)
        .and not_change(promotion, :frequency_in_days)
        .and not_change(promotion, :closed_interval)
        .and not_change(promotion, :rules)
        .and not_change(promotion, :cashback_rules)
        .and not_change(promotion, :status).from("pending")
        .and not_change(promotion, :provider).from(nil)
        .and not_change { coupon.reload.not_cumulative }
        .and not_change(coupon, :redeems_per_cpf)
        .and not_change(coupon, :frequency_in_days)
        .and not_change(coupon, :rules)
        .and not_change(coupon, :cashback_rules)
        .and not_change(coupon, :c_type)

      expect(response).to be_precondition_failed
      expect(response_hash["error"]).to eq("A requisição falhou devido a ausência de parâmetro: #{param_not_sent}")
    end

    it "renders error when promotion is not exclusive" do
      organization = create(:organization)
      create(:organization_profile, organization:, business:)
      branch = create(:branch, organization:)
      promotion = create(
        :promotion,
        organization:,
        provider: nil,
        provider_type: "code",
        code: "ABC123",
        status: "pending",
        not_cumulative: nil,
        redeems_per_cpf: nil,
        frequency_in_days: 0,
        closed_interval: true,
        rules: nil,
        cashback_rules: nil
      )
      coupon = create(
        :cupon,
        promotion:,
        branch:,
        active: true,
        not_cumulative: nil,
        redeems_per_cpf: nil,
        frequency_in_days: 0,
        rules: nil,
        cashback_rules: nil
      )

      params = {
        cumulative: true,
        redeems_per_cpf: 999,
        frequency_in_days: 1,
        closed_interval: false,
        rules: FFaker::LoremBR.phrase,
        cashback_rules: FFaker::LoremBR.phrase
      }
      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/rules",
          "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/rules"
        ].sample
        put path, params:, headers:, as: :json
      end.to not_change { promotion.reload.not_cumulative }
        .and not_change(promotion, :redeems_per_cpf)
        .and not_change(promotion, :frequency_in_days)
        .and not_change(promotion, :closed_interval)
        .and not_change(promotion, :rules)
        .and not_change(promotion, :cashback_rules)
        .and not_change(promotion, :status).from("pending")
        .and not_change(promotion, :provider).from(nil)
        .and not_change { coupon.reload.not_cumulative }
        .and not_change(coupon, :redeems_per_cpf)
        .and not_change(coupon, :frequency_in_days)
        .and not_change(coupon, :rules)
        .and not_change(coupon, :cashback_rules)
        .and not_change(coupon, :c_type)

      expect(response).to be_not_found
    end

    it "renders error when business does not have organization profile" do
      organization = create(:organization)
      branch = create(:branch, organization:)
      promotion = create(
        :promotion,
        organization:,
        business:,
        provider: nil,
        provider_type: "code",
        code: "ABC123",
        status: "pending",
        not_cumulative: nil,
        redeems_per_cpf: nil,
        frequency_in_days: 0,
        closed_interval: true,
        rules: nil,
        cashback_rules: nil
      )
      coupon = create(
        :cupon,
        promotion:,
        branch:,
        business:,
        active: true,
        not_cumulative: nil,
        redeems_per_cpf: nil,
        frequency_in_days: 0,
        rules: nil,
        cashback_rules: nil
      )

      params = {
        cumulative: true,
        redeems_per_cpf: 999,
        frequency_in_days: 1,
        closed_interval: false,
        rules: FFaker::LoremBR.phrase,
        cashback_rules: FFaker::LoremBR.phrase
      }
      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/rules",
          "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/rules"
        ].sample
        put path, params:, headers:, as: :json
      end.to not_change { promotion.reload.not_cumulative }
        .and not_change(promotion, :redeems_per_cpf)
        .and not_change(promotion, :frequency_in_days)
        .and not_change(promotion, :closed_interval)
        .and not_change(promotion, :rules)
        .and not_change(promotion, :cashback_rules)
        .and not_change(promotion, :status).from("pending")
        .and not_change(promotion, :provider).from(nil)
        .and not_change { coupon.reload.not_cumulative }
        .and not_change(coupon, :redeems_per_cpf)
        .and not_change(coupon, :frequency_in_days)
        .and not_change(coupon, :rules)
        .and not_change(coupon, :cashback_rules)
        .and not_change(coupon, :c_type)

      expect(response).to be_not_found
    end

    it "renders error when organization is not active" do
      organization = create(:organization, active: false)
      create(:organization_profile, organization:, business:)
      branch = create(:branch, organization:)
      promotion = create(
        :promotion,
        organization:,
        business:,
        provider: nil,
        provider_type: "code",
        code: "ABC123",
        status: "pending",
        not_cumulative: nil,
        redeems_per_cpf: nil,
        frequency_in_days: 0,
        closed_interval: true,
        rules: nil,
        cashback_rules: nil
      )
      coupon = create(
        :cupon,
        promotion:,
        branch:,
        business:,
        active: true,
        not_cumulative: nil,
        redeems_per_cpf: nil,
        frequency_in_days: 0,
        rules: nil,
        cashback_rules: nil
      )

      params = {
        cumulative: true,
        redeems_per_cpf: 999,
        frequency_in_days: 1,
        closed_interval: false,
        rules: FFaker::LoremBR.phrase,
        cashback_rules: FFaker::LoremBR.phrase
      }
      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/rules",
          "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/rules"
        ].sample
        put path, params:, headers:, as: :json
      end.to not_change { promotion.reload.not_cumulative }
        .and not_change(promotion, :redeems_per_cpf)
        .and not_change(promotion, :frequency_in_days)
        .and not_change(promotion, :closed_interval)
        .and not_change(promotion, :rules)
        .and not_change(promotion, :cashback_rules)
        .and not_change(promotion, :status).from("pending")
        .and not_change(promotion, :provider).from(nil)
        .and not_change { coupon.reload.not_cumulative }
        .and not_change(coupon, :redeems_per_cpf)
        .and not_change(coupon, :frequency_in_days)
        .and not_change(coupon, :rules)
        .and not_change(coupon, :cashback_rules)
        .and not_change(coupon, :c_type)

      expect(response).to be_not_found
    end

    it "renders error when promotion is inactive" do
      organization = create(:organization)
      create(:organization_profile, organization:, business:)
      branch = create(:branch, organization:)
      promotion = create(
        :promotion,
        organization:,
        business:,
        provider: nil,
        provider_type: "code",
        code: "ABC123",
        status: "pending",
        not_cumulative: nil,
        redeems_per_cpf: nil,
        frequency_in_days: 0,
        closed_interval: true,
        rules: nil,
        cashback_rules: nil
      )
      coupon = create(
        :cupon,
        promotion:,
        branch:,
        business:,
        active: true,
        not_cumulative: nil,
        redeems_per_cpf: nil,
        frequency_in_days: 0,
        rules: nil,
        cashback_rules: nil
      )

      promotion.status_inactive!

      params = {
        cumulative: true,
        redeems_per_cpf: 999,
        frequency_in_days: 1,
        closed_interval: false,
        rules: FFaker::LoremBR.phrase,
        cashback_rules: FFaker::LoremBR.phrase
      }
      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/rules",
          "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/rules"
        ].sample
        put path, params:, headers:, as: :json
        promotion.reload
        coupon.reload
      end.to not_change(promotion, :not_cumulative)
        .and not_change(promotion, :redeems_per_cpf)
        .and not_change(promotion, :frequency_in_days)
        .and not_change(promotion, :closed_interval)
        .and not_change(promotion, :rules)
        .and not_change(promotion, :cashback_rules)
        .and not_change(promotion, :status)
        .and not_change(promotion, :provider)
        .and not_change(coupon, :not_cumulative)
        .and not_change(coupon, :redeems_per_cpf)
        .and not_change(coupon, :frequency_in_days)
        .and not_change(coupon, :rules)
        .and not_change(coupon, :cashback_rules)
        .and not_change(coupon, :c_type)

      expect(response).to be_not_found
    end
  end

  def response_hash
    JSON.parse(response.body)
  end
end
