require "rails_helper"

RSpec.describe Client::V2::Organizations::Promotions::DescriptionsController, type: :request do
  let(:business) { create(:business) }
  let!(:client_employee) { create(:client_employee, :admin_lecupon) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  let(:json_parse_response_body) { JSON.parse(response.body) }

  describe "#update" do
    context "given a active organization" do
      let(:active_organization) do
        organization = create(:organization, active: true)
        create(:organization_profile, organization:, business:)

        organization
      end

      context "when the promotion is from the current business" do
        let(:picture) do
          tempfile = Tempfile.new(["picture_image", ".png"], "tmp")

          Rack::Test::UploadedFile.new(tempfile)
        end
        let(:create_promotion) do
          Promotion.create!(
            source: "business",
            organization: active_organization,
            business:
          )
        end

        it "is expected response to be ok" do
          promotion = create_promotion
          descriptions_params = {
            title: FFaker::LoremBR.phrase,
            description: FFaker::LoremBR.paragraph,
            picture:,
            discount_value: 10,
            discount_type: "percent",
            redeem_limited: true,
            quantity: 800,
            average_ticket: 1.0,
            url: FFaker::Internet.http_url,
            cashback_type: Cashback::Type::PERCENT,
            cashback_value: 10,
            usage_instruction: "Any usage instructions"
          }

          expect do
            path = [
              "/client/v2/businesses/#{business.cnpj}/organizations/#{active_organization.id}/promotions/#{promotion.id}/descriptions",
              "/client/v2/organizations/#{active_organization.id}/promotions/#{promotion.id}/descriptions"
            ].sample
            put path, headers:, params: descriptions_params, as: :json
            promotion.reload
          end.to change(promotion, :title)
            .and change(promotion, :description)
            .and change(promotion, :discount_value)
            .and change(promotion, :redeem_type)
            .and change(promotion, :quantity)
            .and change(promotion, :average_ticket)
            .and change(promotion, :picture)
            .and change(promotion, :url)
            .and change(promotion, :cashback_type)
            .and change(promotion, :cashback_value)
            .and change(promotion, :usage_instruction)
            .and not_change(promotion, :infinity)
          expect(response).to have_http_status(:ok)
        end

        it "is expected response not change quantity, discount_value and cashback" do
          promotion = create_promotion
          promotion.update_columns(redeem_type: nil)
          descriptions_params = {
            title: FFaker::LoremBR.phrase,
            description: FFaker::LoremBR.paragraph,
            redeem_limited: "false",
            quantity: 800
          }

          expect do
            path = [
              "/client/v2/businesses/#{business.cnpj}/organizations/#{active_organization.id}/promotions/#{promotion.id}/descriptions",
              "/client/v2/organizations/#{active_organization.id}/promotions/#{promotion.id}/descriptions"
            ].sample
            put path, headers:, params: descriptions_params, as: :json
            promotion.reload
          end.to change(promotion, :infinity)
            .and change(promotion, :redeem_type)
            .and change(promotion, :picture)
            .and not_change(promotion, :discount_value)
            .and not_change(promotion, :quantity)
            .and not_change(promotion, :average_ticket)
            .and not_change(promotion, :url)
            .and not_change(promotion, :cashback_type)
            .and not_change(promotion, :cashback_value)
          expect(response).to have_http_status(:ok)
        end

        it "disables cashback when sending null values" do
          promotion = Promotion.create!(
            source: "business",
            organization: active_organization,
            business:,
            cashback_type: Cashback::Type::PERCENT,
            cashback_value: 10
          )
          params = {
            title: FFaker::LoremBR.phrase,
            description: FFaker::LoremBR.paragraph,
            discount_value: 10,
            discount_type: "percent",
            redeem_limited: "false",
            quantity: 800,
            cashback_type: nil,
            cashback_value: nil
          }

          expect do
            path = [
              "/client/v2/businesses/#{business.cnpj}/organizations/#{active_organization.id}/promotions/#{promotion.id}/descriptions",
              "/client/v2/organizations/#{active_organization.id}/promotions/#{promotion.id}/descriptions"
            ].sample
            put path, headers:, params:, as: :json
            promotion.reload
          end.to change(promotion, :cashback_type).to(nil)
            .and change(promotion, :cashback_value).to(nil)
          expect(response).to have_http_status(:ok)
        end

        context "given a invalid params" do
          it "is expected response with error" do
            promotion = create_promotion
            descriptions_params = {
              title: nil,
              description: nil,
              redeem_limited: nil
            }

            path = [
              "/client/v2/businesses/#{business.cnpj}/organizations/#{active_organization.id}/promotions/#{promotion.id}/descriptions",
              "/client/v2/organizations/#{active_organization.id}/promotions/#{promotion.id}/descriptions"
            ].sample
            put path, headers:, params: descriptions_params, as: :json

            expect(response).to have_http_status(:unprocessable_entity)
            expect(json_parse_response_body["error"])
              .to eq("Description não pode ficar em branco, Title não pode ficar em branco")
          end
        end

        context "when business is inactve" do
          let(:create_promotion) { Promotion.create!(provider_type: "online", source: "business", organization: active_organization, business:) }

          specify do
            business.update!(status: Business::Status::SUSPENDED_BY_OVERDUE)
            promotion = create_promotion
            descriptions_params = {
              title: FFaker::LoremBR.phrase,
              description: FFaker::LoremBR.paragraph,
              discount_value: nil,
              discount_type: nil,
              redeem_limited: "false"
            }

            path = [
              "/client/v2/businesses/#{business.cnpj}/organizations/#{active_organization.id}/promotions/#{promotion.id}/descriptions",
              "/client/v2/organizations/#{active_organization.id}/promotions/#{promotion.id}/descriptions"
            ].sample
            put path, headers:, params: descriptions_params, as: :json

            expect(response).to have_http_status(:not_found)
          end
        end
      end

      context "given promotion from an another business" do
        let(:another_business) { create(:business) }
        let(:create_promotion) { Promotion.create!(source: "business", organization: active_organization, business: another_business) }

        specify do
          promotion = create_promotion
          descriptions_params = {
            title: FFaker::LoremBR.phrase,
            description: FFaker::LoremBR.paragraph,
            redeem_limited: "false"
          }

          path = [
            "/client/v2/businesses/#{business.cnpj}/organizations/#{active_organization.id}/promotions/#{promotion.id}/descriptions",
            "/client/v2/organizations/#{active_organization.id}/promotions/#{promotion.id}/descriptions"
          ].sample
          put path, headers:, params: descriptions_params, as: :json

          expect(response).to have_http_status(:not_found)
        end
      end
    end

    context "given a blocklisted organization" do
      let(:blocklisted_organization) do
        organization = create(:organization, active: true)
        create(:organization_profile, organization:, business:)
        create(:organization_blocklist, business:, organization:)

        organization
      end
      let(:create_promotion) { Promotion.create!(source: "business", organization: blocklisted_organization, business:) }

      it "is expected response to be ok" do
        promotion = create_promotion
        descriptions_params = {
          title: FFaker::LoremBR.phrase,
          description: FFaker::LoremBR.paragraph,
          redeem_limited: "false"
        }

        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/organizations/#{blocklisted_organization.id}/promotions/#{promotion.id}/descriptions",
            "/client/v2/organizations/#{blocklisted_organization.id}/promotions/#{promotion.id}/descriptions"
          ].sample
          put path, headers:, params: descriptions_params, as: :json

          promotion.reload
        end.to change(promotion, :title)
          .and change(promotion, :description)
        expect(response).to have_http_status(:ok)
      end
    end

    context "given a inactive organization" do
      let(:inactive_organization) do
        organization = create(:organization, active: false)
        create(:organization_profile, organization:, business:)
        organization
      end
      let(:create_promotion) { Promotion.create!(source: "business", organization: inactive_organization, business:) }

      specify do
        promotion = create_promotion
        descriptions_params = {
          title: FFaker::LoremBR.phrase,
          description: FFaker::LoremBR.paragraph,
          redeem_limited: "false"
        }

        path = [
          "/client/v2/businesses/#{business.cnpj}/organizations/#{inactive_organization.id}/promotions/#{promotion.id}/descriptions",
          "/client/v2/organizations/#{inactive_organization.id}/promotions/#{promotion.id}/descriptions"
        ].sample
        put path, headers:, params: descriptions_params, as: :json

        expect(response).to have_http_status(:not_found)
      end
    end

    context "when organization does not have profile" do
      let(:active_organization_without_profile) { create(:organization, active: true) }
      let(:create_promotion) { Promotion.create!(source: "business", organization: active_organization_without_profile, business:) }

      specify do
        promotion = create_promotion
        descriptions_params = {
          title: FFaker::LoremBR.phrase,
          description: FFaker::LoremBR.paragraph,
          redeem_limited: "false"
        }

        path = [
          "/client/v2/businesses/#{business.cnpj}/organizations/#{active_organization_without_profile.id}/promotions/#{promotion.id}/descriptions",
          "/client/v2/organizations/#{active_organization_without_profile.id}/promotions/#{promotion.id}/descriptions"
        ].sample
        put path, headers:, params: descriptions_params, as: :json

        expect(response).to have_http_status(:not_found)
      end
    end

    context "when promotion is inactive" do
      let!(:organization) { create(:organization) }
      let!(:organization_profile) { create(:organization_profile, organization:, business:) }
      let!(:branch) { create(:branch, organization:) }
      let(:picture) { Rack::Test::UploadedFile.new(Tempfile.new(["picture_image", ".png"], "tmp")) }
      let!(:promotion) { Promotion.create!(source: :business, organization:, business:) }
      let(:params) do
        {
          title: FFaker::LoremBR.phrase,
          description: FFaker::LoremBR.paragraph,
          picture:,
          discount: 10,
          redeem_limited: true,
          quantity: 800,
          average_ticket: 1.0,
          url: FFaker::Internet.http_url
        }
      end

      before do
        promotion.status_inactive!
      end

      it "renders error" do
        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/organizations/#{organization.id}/promotions/#{promotion.id}/descriptions",
            "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/descriptions"
          ].sample
          put path, headers:, params:, as: :json

          promotion.reload
        end.to not_change(promotion, :description)
          .and not_change(promotion, :title)
          .and not_change(promotion, :discount_value)
          .and not_change(promotion, :infinity)
          .and not_change(promotion, :quantity)
          .and not_change(promotion, :average_ticket)
          .and not_change(promotion, :url)

        expect(response).to have_http_status(:not_found)
      end
    end
  end
end
