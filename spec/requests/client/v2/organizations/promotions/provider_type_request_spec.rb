require "rails_helper"

RSpec.describe Client::V2::Organizations::Promotions::ProviderTypeController, type: :request do
  let(:business) { create(:business) }
  let!(:client_employee) { create(:client_employee, :admin_lecupon) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  let(:json_parse_response_body) { JSON.parse(response.body) }

  describe "#update" do
    context "given a active organization" do
      let(:active_organization) do
        organization = create(:organization, active: true)
        create(:organization_profile, organization:, business:)

        organization
      end

      context "when the promotion is from the current business" do
        let(:create_promotion) do
          promotion = create(:promotion, provider_type: nil, provider: nil, organization: active_organization, business:)
          create_list(:cupon, 2, promotion:, c_type: "cpf")

          promotion
        end

        it "is expected response to be ok" do
          promotion = create_promotion
          params = {provider_type: "regionalized"}

          expect do
            path = [
              "/client/v2/businesses/#{business.cnpj}/organizations/#{active_organization.id}/promotions/#{promotion.id}/provider_type",
              "/client/v2/organizations/#{active_organization.id}/promotions/#{promotion.id}/provider_type"
            ].sample
            patch path, params:, headers:, as: :json

            promotion.reload
          end.to change(promotion, :provider_type).from(nil).to("regionalized")
            .and change(promotion, :provider).from(nil).to("regionalized")
            .and change { promotion.cupons.pluck(:c_type) }.to(all(eq("ONLINE")))
          expect(response).to have_http_status(:ok)
        end

        context "given a invalid provider_type param" do
          it "is expected response with error" do
            promotion = create_promotion
            params = {provider_type: "INVALID"}

            path = [
              "/client/v2/businesses/#{business.cnpj}/organizations/#{active_organization.id}/promotions/#{promotion.id}/provider_type",
              "/client/v2/organizations/#{active_organization.id}/promotions/#{promotion.id}/provider_type"
            ].sample
            patch path, params:, headers:, as: :json

            expect(response).to have_http_status(:unprocessable_entity)
            expect(json_parse_response_body["error"]).to eq("Provider type não está incluído na lista")
          end

          it "is expected response with error" do
            promotion = create_promotion
            params = {}

            path = [
              "/client/v2/businesses/#{business.cnpj}/organizations/#{active_organization.id}/promotions/#{promotion.id}/provider_type",
              "/client/v2/organizations/#{active_organization.id}/promotions/#{promotion.id}/provider_type"
            ].sample
            patch path, params:, headers:, as: :json

            expect(response).to have_http_status(:precondition_failed)
            expect(json_parse_response_body["error"]).to eq("A requisição falhou devido a ausência de parâmetro: provider_type")
          end
        end

        context "when business is inactve" do
          let(:create_promotion) { Promotion.create!(provider_type: "online", source: "business", organization: active_organization, business:) }

          specify do
            business.update!(status: Business::Status::SUSPENDED_BY_OVERDUE)
            promotion = create_promotion
            params = {provider_type: "regionalized"}

            path = [
              "/client/v2/businesses/#{business.cnpj}/organizations/#{active_organization.id}/promotions/#{promotion.id}/provider_type",
              "/client/v2/organizations/#{active_organization.id}/promotions/#{promotion.id}/provider_type"
            ].sample
            patch path, params:, headers:, as: :json

            expect(response).to have_http_status(:not_found)
          end
        end
      end

      context "given promotion from an another business" do
        let(:another_business) { create(:business) }
        let(:create_promotion) { Promotion.create!(source: "business", organization: active_organization, business: another_business) }

        specify do
          promotion = create_promotion
          params = {provider_type: "cpf"}

          path = [
            "/client/v2/businesses/#{business.cnpj}/organizations/#{active_organization.id}/promotions/#{promotion.id}/provider_type",
            "/client/v2/organizations/#{active_organization.id}/promotions/#{promotion.id}/provider_type"
          ].sample
          patch path, params:, headers:, as: :json

          expect(response).to have_http_status(:not_found)
        end
      end
    end

    context "given a blocklisted organization" do
      let(:blocklisted_organization) do
        organization = create(:organization, active: true)
        create(:organization_profile, organization:, business:)
        create(:organization_blocklist, business:, organization:)

        organization
      end
      let(:create_promotion) { Promotion.create!(source: "business", organization: blocklisted_organization, business:) }

      it "is expected response to be ok" do
        promotion = create_promotion
        params = {provider_type: "online"}

        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/organizations/#{blocklisted_organization.id}/promotions/#{promotion.id}/provider_type",
            "/client/v2/organizations/#{blocklisted_organization.id}/promotions/#{promotion.id}/provider_type"
          ].sample
          patch path, params:, headers:, as: :json
        end.to change { promotion.reload.provider_type }.from(nil).to("online")
        expect(response).to have_http_status(:ok)
      end

      it "is expected response to be ok" do
        promotion = create_promotion
        params = {provider_type: "code"}

        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/organizations/#{blocklisted_organization.id}/promotions/#{promotion.id}/provider_type",
            "/client/v2/organizations/#{blocklisted_organization.id}/promotions/#{promotion.id}/provider_type"
          ].sample
          patch path, params:, headers:, as: :json
        end.to change { promotion.reload.provider_type }.from(nil).to("code")
        expect(response).to have_http_status(:ok)
      end
    end

    context "given a inactive organization" do
      let(:inactive_organization) do
        organization = create(:organization, active: false)
        create(:organization_profile, organization:, business:)
        organization
      end
      let(:create_promotion) { Promotion.create!(source: "business", organization: inactive_organization, business:) }

      specify do
        promotion = create_promotion
        params = {provider_type: "cpf"}

        path = [
          "/client/v2/businesses/#{business.cnpj}/organizations/#{inactive_organization.id}/promotions/#{promotion.id}/provider_type",
          "/client/v2/organizations/#{inactive_organization.id}/promotions/#{promotion.id}/provider_type"
        ].sample
        patch path, params:, headers:, as: :json

        expect(response).to have_http_status(:not_found)
      end
    end

    context "when organization does not have profile" do
      let(:active_organization_without_profile) { create(:organization, active: true) }
      let(:create_promotion) { Promotion.create!(source: "business", organization: active_organization_without_profile, business:) }

      specify do
        promotion = create_promotion
        params = {provider_type: "cpf"}

        path = [
          "/client/v2/businesses/#{business.cnpj}/organizations/#{active_organization_without_profile.id}/promotions/#{promotion.id}/provider_type",
          "/client/v2/organizations/#{active_organization_without_profile.id}/promotions/#{promotion.id}/provider_type"
        ].sample
        patch path, params:, headers:, as: :json

        expect(response).to have_http_status(:not_found)
      end
    end

    context "when promotion is inactive" do
      let!(:organization) { create(:organization) }
      let!(:organization_profile) { create(:organization_profile, organization:, business:) }
      let!(:branch) { create(:branch, organization:) }
      let(:promotion) { create(:promotion, :inactive, provider_type: nil, provider: nil, organization:, business:) }
      let!(:coupon) { create(:cupon, branch:, promotion:, organization:) }
      let(:params) { {provider_type: "regionalized"} }

      it "renders error" do
        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/organizations/#{organization.id}/promotions/#{promotion.id}/provider_type",
            "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/provider_type"
          ].sample
          patch path, params:, headers:, as: :json

          promotion.reload
        end.to not_change(promotion, :provider_type)
          .and not_change(promotion, :provider)
          .and not_change { promotion.cupons.pluck(:c_type) }
        expect(response).to be_not_found
      end
    end
  end
end
