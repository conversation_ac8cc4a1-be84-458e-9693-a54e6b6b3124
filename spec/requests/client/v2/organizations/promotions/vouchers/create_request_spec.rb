require "rails_helper"

RSpec.describe Client::V2::Organizations::Promotions::VouchersController, type: :request do
  describe "#create" do
    let(:business) { create(:business) }
    let!(:client_employee) { create(:client_employee, :admin_lecupon) }
    let(:headers) do
      {
        "X-ClientEmployee-Email": client_employee.email,
        "X-ClientEmployee-Token": client_employee.authentication_token,
        "Tenant-id": business.cnpj
      }
    end

    it "renders successful" do
      organization = create(:organization)
      create(:organization_profile, organization:, business:)
      branch = create(:branch, organization:)
      bucket = create(:voucher_bucket)
      promotion = create(
        :promotion,
        organization:,
        business:,
        provider: nil,
        provider_type: "code",
        status: "no_voucher",
        voucher_type: "none",
        code: "ABC123",
        dynamic_voucher: true,
        voucher_bucket: bucket
      )
      Cupon.create!(promotion:, branch:, active: true)

      vouchers = [
        {token: "ABC123", began_at: "10/12/2023", expired_at: "01/01/2050", bucket_id: bucket.id},
        {token: "DEF456", began_at: "01/02/2024", expired_at: "01/01/2050", bucket_id: bucket.id}
      ]
      file = Tempfile.new(["vouchers", ".csv"], "tmp")
      CSV.open(file, "wb", headers: true, col_sep: ";") do |csv|
        csv << ["código", "início", "fim"]
        vouchers.each do |voucher|
          csv << [voucher[:token], voucher[:began_at], voucher[:expired_at]]
        end
      end
      params = {file: Rack::Test::UploadedFile.new(file)}
      file.close

      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/vouchers",
          "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/vouchers"
        ].sample
        post path, params:, headers:
      end.to change(promotion.voucher_bucket.vouchers, :count).by(vouchers.size)
        .and change { promotion.reload.status }.from("no_voucher").to("available")
        .and change(promotion, :provider).from(nil).to("dynamic")
        .and change(promotion, :voucher_type).to("list")
        .and change(promotion, :code).to(nil)
        .and change(promotion, :dynamic_voucher).to(false)
        .and change { promotion.cupons.pluck(:c_type) }.to(all(eq("DYNAMIC")))

      expect(response).to be_created
    end

    it "renders error when file has problems" do
      organization = create(:organization)
      create(:organization_profile, organization:, business:)
      branch = create(:branch, organization:)
      promotion = create(
        :promotion,
        organization:,
        business:,
        provider: nil,
        provider_type: "code",
        status: "no_voucher",
        voucher_type: "none",
        code: "ABC123",
        dynamic_voucher: true
      )
      Cupon.create!(promotion:, branch:, active: true)

      file = Tempfile.new(["vouchers", ".csv"], "tmp")
      CSV.open(file, "wb", headers: true, col_sep: ";") do |csv|
        csv << ["código", "início"]
      end
      params = {file: Rack::Test::UploadedFile.new(file)}
      file.close

      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/vouchers",
          "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/vouchers"
        ].sample
        post path, params:, headers:
      end.to change(Voucher, :count).by(0)
        .and not_change { promotion.reload.status }
        .and not_change(promotion, :provider)
        .and not_change(promotion, :voucher_type)
        .and not_change(promotion, :code)
        .and not_change(promotion, :dynamic_voucher)
        .and not_change { promotion.cupons.pluck(:c_type) }

      expect(response).to be_unprocessable
      expect(response_hash["error"]).to eq("Linha 1: O cabeçalho do CSV deve estar no seguinte formato: codigo;inicio;fim")
    end

    it "renders error when file is not sent" do
      organization = create(:organization)
      create(:organization_profile, organization:, business:)
      branch = create(:branch, organization:)
      promotion = create(
        :promotion,
        organization:,
        business:,
        provider: nil,
        provider_type: "code",
        status: "no_voucher",
        voucher_type: "none",
        code: "ABC123",
        dynamic_voucher: true
      )
      Cupon.create!(promotion:, branch:, active: true)

      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/vouchers",
          "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/vouchers"
        ].sample
        post path, headers:, as: :json
      end.to change(Voucher, :count).by(0)
        .and not_change { promotion.reload.status }
        .and not_change(promotion, :provider)
        .and not_change(promotion, :voucher_type)
        .and not_change(promotion, :code)
        .and not_change(promotion, :dynamic_voucher)
        .and not_change { promotion.cupons.pluck(:c_type) }

      expect(response).to be_precondition_failed
    end

    it "renders error when promotion is not exclusive" do
      organization = create(:organization)
      create(:organization_profile, organization:, business:)
      branch = create(:branch, organization:)
      promotion = create(
        :promotion,
        organization:,
        provider: nil,
        provider_type: "code",
        status: "no_voucher",
        voucher_type: "none",
        code: "ABC123",
        dynamic_voucher: true
      )
      Cupon.create!(promotion:, branch:, active: true)

      vouchers = [
        {token: "ABC123", began_at: "10/12/2023", expired_at: "01/01/2050"},
        {token: "DEF456", began_at: "01/02/2024", expired_at: "01/01/2050"}
      ]
      file = Tempfile.new(["vouchers", ".csv"], "tmp")
      CSV.open(file, "wb", headers: true, col_sep: ";") do |csv|
        csv << ["código", "início", "fim"]
        vouchers.each do |voucher|
          csv << [voucher[:token], voucher[:began_at], voucher[:expired_at]]
        end
      end
      params = {file: Rack::Test::UploadedFile.new(file)}
      file.close

      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/vouchers",
          "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/vouchers"
        ].sample
        post path, params:, headers:, as: :json
      end.to change(Voucher, :count).by(0)
        .and not_change { promotion.reload.status }
        .and not_change(promotion, :provider)
        .and not_change(promotion, :voucher_type)
        .and not_change(promotion, :code)
        .and not_change(promotion, :dynamic_voucher)
        .and not_change { promotion.cupons.pluck(:c_type) }

      expect(response).to be_not_found
    end

    it "renders error when business does not have organization profile" do
      organization = create(:organization)
      branch = create(:branch, organization:)
      promotion = create(
        :promotion,
        organization:,
        business:,
        provider: nil,
        provider_type: "code",
        status: "no_voucher",
        voucher_type: "none",
        code: "ABC123",
        dynamic_voucher: true
      )
      Cupon.create!(promotion:, branch:, active: true)

      vouchers = [
        {token: "ABC123", began_at: "10/12/2023", expired_at: "01/01/2050"},
        {token: "DEF456", began_at: "01/02/2024", expired_at: "01/01/2050"}
      ]
      file = Tempfile.new(["vouchers", ".csv"], "tmp")
      CSV.open(file, "wb", headers: true, col_sep: ";") do |csv|
        csv << ["código", "início", "fim"]
        vouchers.each do |voucher|
          csv << [voucher[:token], voucher[:began_at], voucher[:expired_at]]
        end
      end
      params = {file: Rack::Test::UploadedFile.new(file)}
      file.close

      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/vouchers",
          "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/vouchers"
        ].sample
        post path, params:, headers:, as: :json
      end.to change(Voucher, :count).by(0)
        .and not_change { promotion.reload.status }
        .and not_change(promotion, :provider)
        .and not_change(promotion, :voucher_type)
        .and not_change(promotion, :code)
        .and not_change(promotion, :dynamic_voucher)
        .and not_change { promotion.cupons.pluck(:c_type) }

      expect(response).to be_not_found
    end

    it "renders error when promotion provider_type is not code or regionalized" do
      organization = create(:organization)
      create(:organization_profile, organization:, business:)
      branch = create(:branch, organization:)
      promotion = create(
        :promotion,
        organization:,
        business:,
        provider: nil,
        provider_type: (Promotion::ProviderType.all - Promotion::ProviderType.voucherable).sample,
        status: "no_voucher",
        voucher_type: "none",
        code: "ABC123",
        dynamic_voucher: true
      )
      Cupon.create!(promotion:, branch:, active: true)

      vouchers = [
        {token: "ABC123", began_at: "10/12/2023", expired_at: "01/01/2050"},
        {token: "DEF456", began_at: "01/02/2024", expired_at: "01/01/2050"}
      ]
      file = Tempfile.new(["vouchers", ".csv"], "tmp")
      CSV.open(file, "wb", headers: true, col_sep: ";") do |csv|
        csv << ["código", "início", "fim"]
        vouchers.each do |voucher|
          csv << [voucher[:token], voucher[:began_at], voucher[:expired_at]]
        end
      end
      params = {file: Rack::Test::UploadedFile.new(file)}
      file.close

      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/vouchers",
          "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/vouchers"
        ].sample
        post path, params:, headers:, as: :json
      end.to change(Voucher, :count).by(0)
        .and not_change { promotion.reload.status }
        .and not_change(promotion, :provider)
        .and not_change(promotion, :voucher_type)
        .and not_change(promotion, :code)
        .and not_change(promotion, :dynamic_voucher)
        .and not_change { promotion.cupons.pluck(:c_type) }

      expect(response).to be_not_found
    end

    it "renders error when organization is not active" do
      organization = create(:organization, active: false)
      create(:organization_profile, organization:, business:)
      branch = create(:branch, organization:)
      promotion = create(
        :promotion,
        organization:,
        business:,
        provider: nil,
        provider_type: "code",
        status: "no_voucher",
        voucher_type: "none",
        code: "ABC123",
        dynamic_voucher: true
      )
      Cupon.create!(promotion:, branch:, active: true)

      vouchers = [
        {token: "ABC123", began_at: "10/12/2023", expired_at: "01/01/2050"},
        {token: "DEF456", began_at: "01/02/2024", expired_at: "01/01/2050"}
      ]
      file = Tempfile.new(["vouchers", ".csv"], "tmp")
      CSV.open(file, "wb", headers: true, col_sep: ";") do |csv|
        csv << ["código", "início", "fim"]
        vouchers.each do |voucher|
          csv << [voucher[:token], voucher[:began_at], voucher[:expired_at]]
        end
      end
      params = {file: Rack::Test::UploadedFile.new(file)}
      file.close

      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/vouchers",
          "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/vouchers"
        ].sample
        post path, params:, headers:, as: :json
      end.to change(Voucher, :count).by(0)
        .and not_change { promotion.reload.status }
        .and not_change(promotion, :provider)
        .and not_change(promotion, :voucher_type)
        .and not_change(promotion, :code)
        .and not_change(promotion, :dynamic_voucher)
        .and not_change { promotion.cupons.pluck(:c_type) }

      expect(response).to be_not_found
    end

    it "renders error when promotion is inactive" do
      organization = create(:organization)
      create(:organization_profile, organization:, business:)
      branch = create(:branch, organization:)
      bucket = create(:voucher_bucket)
      promotion = create(
        :promotion,
        organization:,
        business:,
        provider: nil,
        provider_type: "code",
        status: "no_voucher",
        voucher_type: "none",
        code: "ABC123",
        dynamic_voucher: true,
        voucher_bucket: bucket
      )
      Cupon.create!(promotion:, branch:, active: true)

      promotion.status_inactive!

      vouchers = [
        {token: "ABC123", began_at: "10/12/2023", expired_at: "01/01/2050", bucket_id: bucket.id},
        {token: "DEF456", began_at: "01/02/2024", expired_at: "01/01/2050", bucket_id: bucket.id}
      ]
      file = Tempfile.new(["vouchers", ".csv"], "tmp")
      CSV.open(file, "wb", headers: true, col_sep: ";") do |csv|
        csv << ["código", "início", "fim"]
        vouchers.each do |voucher|
          csv << [voucher[:token], voucher[:began_at], voucher[:expired_at]]
        end
      end
      params = {file: Rack::Test::UploadedFile.new(file)}
      file.close

      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/vouchers",
          "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/vouchers"
        ].sample
        post path, params:, headers:, as: :json
      end.to change(Voucher, :count).by(0)
        .and not_change { promotion.reload.status }
        .and not_change(promotion, :provider)
        .and not_change(promotion, :voucher_type)
        .and not_change(promotion, :code)
        .and not_change(promotion, :dynamic_voucher)
        .and not_change { promotion.cupons.pluck(:c_type) }

      expect(response).to be_not_found
    end
  end

  def response_hash
    JSON.parse(response.body)
  end
end
