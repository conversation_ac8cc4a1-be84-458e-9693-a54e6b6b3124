require "rails_helper"

RSpec.describe Client::V2::Organizations::Promotions::VouchersController, type: :request do
  describe "#index" do
    let(:business) { create(:business) }
    let!(:client_employee) { create(:client_employee, :admin_lecupon) }
    let(:headers) do
      {
        "X-ClientEmployee-Email": client_employee.email,
        "X-ClientEmployee-Token": client_employee.authentication_token,
        "Tenant-id": business.cnpj
      }
    end

    it "renders successful" do
      organization = create(:organization)
      create(:organization_profile, organization:, business:)
      bucket = create(:voucher_bucket)
      promotion = create(:promotion, organization:, business:, provider_type: "code", voucher_bucket: bucket)
      voucher_1 = create(:voucher, code: "ABC123", began_at: "10/12/2023", expired_at: "01/01/2050", bucket:)
      voucher_2 = create(:voucher, code: "DEF456", began_at: "01/02/2024", expired_at: "01/01/2050", bucket:)

      unrelated_bucket = create(:voucher_bucket)
      create(:promotion, voucher_bucket: unrelated_bucket)
      create(:voucher, bucket: unrelated_bucket)

      path = [
        "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/vouchers",
        "/client/v2//organizations/#{organization.id}/promotions/#{promotion.id}/vouchers"
      ].sample
      get(path, headers:)

      expect(response).to be_ok
      expect(response_hash).to match_array([
        {
          "id" => voucher_1.id,
          "token" => "ABC123",
          "began_at" => "2023-12-10T00:00:00.000-03:00",
          "expired_at" => "2050-01-01T00:00:00.000-03:00"
        },
        {
          "id" => voucher_2.id,
          "token" => "DEF456",
          "began_at" => "2024-02-01T00:00:00.000-03:00",
          "expired_at" => "2050-01-01T00:00:00.000-03:00"
        }
      ])
    end

    it "renders error when promotion is not exclusive" do
      organization = create(:organization)
      create(:organization_profile, organization:, business:)
      promotion = create(:promotion, organization:, provider_type: "code")
      bucket = create(:voucher_bucket)
      create(:voucher, code: "ABC123", began_at: "10/12/2023", expired_at: "01/01/2050", bucket:)
      create(:voucher, code: "DEF456", began_at: "01/02/2024", expired_at: "01/01/2050", bucket:)

      unrelated_bucket = create(:voucher_bucket)
      create(:promotion, voucher_bucket: unrelated_bucket)
      create(:voucher, bucket: unrelated_bucket)

      path = [
        "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/vouchers",
        "/client/v2//organizations/#{organization.id}/promotions/#{promotion.id}/vouchers"
      ].sample
      get(path, headers:)

      expect(response).to be_not_found
    end

    it "renders error when business does not have organization profile" do
      organization = create(:organization)
      promotion = create(:promotion, organization:, business:, provider_type: "code")
      bucket = create(:voucher_bucket)
      create(:voucher, code: "ABC123", began_at: "10/12/2023", expired_at: "01/01/2050", bucket:)
      create(:voucher, code: "DEF456", began_at: "01/02/2024", expired_at: "01/01/2050", bucket:)

      unrelated_bucket = create(:voucher_bucket)
      create(:promotion, voucher_bucket: unrelated_bucket)
      create(:voucher, bucket: unrelated_bucket)

      path = [
        "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/vouchers",
        "/client/v2//organizations/#{organization.id}/promotions/#{promotion.id}/vouchers"
      ].sample
      get(path, headers:)

      expect(response).to be_not_found
    end

    it "renders error when promotion provider_type is not code or regionalized" do
      organization = create(:organization)
      create(:organization_profile, organization:, business:)
      promotion = create(
        :promotion,
        organization:,
        business:,
        provider_type: (Promotion::ProviderType.all - Promotion::ProviderType.voucherable).sample
      )
      bucket = create(:voucher_bucket)
      create(:voucher, code: "ABC123", began_at: "10/12/2023", expired_at: "01/01/2050", bucket:)
      create(:voucher, code: "DEF456", began_at: "01/02/2024", expired_at: "01/01/2050", bucket:)

      unrelated_bucket = create(:voucher_bucket)
      create(:promotion, voucher_bucket: unrelated_bucket)
      create(:voucher, bucket: unrelated_bucket)

      path = [
        "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/vouchers",
        "/client/v2//organizations/#{organization.id}/promotions/#{promotion.id}/vouchers"
      ].sample
      get(path, headers:)

      expect(response).to be_not_found
    end

    it "renders error when business does not have organization profile" do
      organization = create(:organization, active: false)
      create(:organization_profile, organization:, business:)
      promotion = create(:promotion, organization:, business:, provider_type: "code")
      bucket = create(:voucher_bucket)
      create(:voucher, code: "ABC123", began_at: "10/12/2023", expired_at: "01/01/2050", bucket:)
      create(:voucher, code: "DEF456", began_at: "01/02/2024", expired_at: "01/01/2050", bucket:)

      unrelated_bucket = create(:voucher_bucket)
      create(:promotion, voucher_bucket: unrelated_bucket)
      create(:voucher, bucket: unrelated_bucket)

      path = [
        "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/vouchers",
        "/client/v2//organizations/#{organization.id}/promotions/#{promotion.id}/vouchers"
      ].sample
      get(path, headers:)

      expect(response).to be_not_found
    end
  end

  def response_hash
    JSON.parse(response.body)
  end
end
