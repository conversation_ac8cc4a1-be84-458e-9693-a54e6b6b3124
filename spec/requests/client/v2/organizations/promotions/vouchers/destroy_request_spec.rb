require "rails_helper"

RSpec.describe Client::V2::Organizations::Promotions::VouchersController, type: :request do
  describe "#destroy" do
    let(:business) { create(:business) }
    let!(:client_employee) { create(:client_employee, :admin_lecupon) }
    let(:headers) do
      {
        "X-ClientEmployee-Email": client_employee.email,
        "X-ClientEmployee-Token": client_employee.authentication_token,
        "Tenant-id": business.cnpj
      }
    end

    it "renders successful" do
      organization = create(:organization)
      create(:organization_profile, organization:, business:)
      bucket = create(:voucher_bucket)
      promotion = create(:promotion, organization:, business:, provider_type: "code", voucher_bucket: bucket)
      voucher = create(:voucher, code: "ABC123", began_at: "10/12/2023", expired_at: "01/01/2050", bucket:)

      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/vouchers/#{voucher.id}",
          "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/vouchers/#{voucher.id}"
        ].sample
        delete path, headers:
      end.to change(promotion.voucher_bucket.vouchers, :count).by(-1)

      expect(response).to be_no_content
    end

    it "renders error when voucher is not available" do
      organization = create(:organization)
      create(:organization_profile, organization:, business:)
      bucket = create(:voucher_bucket)
      promotion = create(:promotion, organization:, business:, provider_type: "code", voucher_bucket: bucket)
      voucher = create(
        :voucher,
        order: create(:order),
        code: "ABC123",
        began_at: "10/12/2023",
        expired_at: "01/01/2050",
        bucket:
      )

      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/vouchers/#{voucher.id}",
          "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/vouchers/#{voucher.id}"
        ].sample
        delete path, headers:
      end.to change(Voucher, :count).by(0)

      expect(response).to be_not_found
    end

    it "renders error when voucher is from another promotion" do
      organization = create(:organization)
      create(:organization_profile, organization:, business:)
      voucher_bucket = create(:voucher_bucket)
      promotion = create(:promotion, organization:, business:, provider_type: "code", voucher_bucket:)

      another_bucket = create(:voucher_bucket)
      create(:promotion, organization:, business:, provider_type: "code", voucher_bucket: another_bucket)
      voucher = create(
        :voucher,
        code: "ABC123",
        began_at: "10/12/2023",
        expired_at: "01/01/2050",
        bucket: another_bucket
      )

      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/vouchers/#{voucher.id}",
          "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/vouchers/#{voucher.id}"
        ].sample
        delete path, headers:
      end.to change(Voucher, :count).by(0)
      expect(response).to be_not_found
    end

    it "renders error when promotion is not exclusive" do
      organization = create(:organization)
      create(:organization_profile, organization:, business:)
      bucket = create(:voucher_bucket)
      promotion = create(:promotion, organization:, provider_type: "code", voucher_bucket: bucket)
      voucher = create(:voucher, code: "ABC123", began_at: "10/12/2023", expired_at: "01/01/2050", bucket:)

      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/vouchers/#{voucher.id}",
          "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/vouchers/#{voucher.id}"
        ].sample
        delete path, headers:
      end.to change(Voucher, :count).by(0)

      expect(response).to be_not_found
    end

    it "renders error when business does not have organization profile" do
      organization = create(:organization)
      bucket = create(:voucher_bucket)
      promotion = create(:promotion, organization:, business:, provider_type: "code", voucher_bucket: bucket)
      voucher = create(:voucher, code: "ABC123", began_at: "10/12/2023", expired_at: "01/01/2050", bucket:)

      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/vouchers/#{voucher.id}",
          "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/vouchers/#{voucher.id}"
        ].sample
        delete path, headers:
      end.to change(Voucher, :count).by(0)

      expect(response).to be_not_found
    end

    it "renders error when promotion provider_type is not code or regionalized" do
      organization = create(:organization)
      create(:organization_profile, organization:, business:)
      bucket = create(:voucher_bucket)
      promotion = create(
        :promotion,
        organization:,
        business:,
        provider_type: (Promotion::ProviderType.all - Promotion::ProviderType.voucherable).sample,
        voucher_bucket: bucket
      )
      voucher = create(:voucher, code: "ABC123", began_at: "10/12/2023", expired_at: "01/01/2050", bucket:)

      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/vouchers/#{voucher.id}",
          "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/vouchers/#{voucher.id}"
        ].sample
        delete path, headers:
      end.to change(Voucher, :count).by(0)

      expect(response).to be_not_found
    end

    it "renders error when organization is not active" do
      organization = create(:organization, active: false)
      create(:organization_profile, organization:, business:)
      bucket = create(:voucher_bucket)
      promotion = create(:promotion, organization:, business:, provider_type: "code", voucher_bucket: bucket)
      voucher = create(:voucher, code: "ABC123", began_at: "10/12/2023", expired_at: "01/01/2050", bucket:)

      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organizations/#{organization.id}/promotions/#{promotion.id}/vouchers/#{voucher.id}",
          "/client/v2/organizations/#{organization.id}/promotions/#{promotion.id}/vouchers/#{voucher.id}"
        ].sample
        delete path, headers:
      end.to change(Voucher, :count).by(0)

      expect(response).to be_not_found
    end
  end
end
