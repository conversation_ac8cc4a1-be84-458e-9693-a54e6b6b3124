require "rails_helper"

RSpec.describe Client::V2::Organizations::CouponsController, type: :request do
  let!(:business) { create(:business) }
  let!(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  describe "#index" do
    let!(:promotion_one) { create(:promotion, provider: Promotion::Provider::COUPON_CODE) }
    let!(:organization) { create(:organization) }
    let!(:branch) { create(:branch, organization:) }
    let!(:coupon_one) { create(:cupon, :classic, branch:, promotion: promotion_one) }

    it "renders a successful response" do
      path = [
        "/client/v2/businesses/#{business.cnpj}/organizations/#{organization.id}/coupons",
        "/client/v2/organizations/#{organization.id}/coupons"
      ].sample
      get(path, headers:)

      expect(response).to be_successful
      expect(JSON.parse(response.body).count).to eq(organization.cupons.count)
    end
  end
end
