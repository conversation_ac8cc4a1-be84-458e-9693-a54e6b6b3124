require "rails_helper"

RSpec.describe Client::V2::Organizations::AdminsController, type: :request do
  let!(:business) { create(:business) }
  let!(:organization) { create(:organization) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end
  let(:response_hash) { JSON.parse(response.body) }
  let(:serialized_keys) { %w[id active email invitation_accepted_at invitation_sent_at name] }

  describe "#index" do
    let!(:client_employee) { create(:client_employee, businesses: [business]) }
    let!(:admin_organization) { create(:admin_organization, business:, organization:, client_employee:) }

    let!(:client_employee_2) { create(:client_employee, businesses: [business]) }
    let!(:admin_organization_2) { create(:admin_organization, business:, organization:, client_employee: client_employee_2) }

    let!(:unrelated_client_employee) { create(:client_employee) }
    let!(:unrelated_admin_organization) { create(:admin_organization, organization:, client_employee: unrelated_client_employee) }

    let!(:unrelated_client_employee_2) { create(:client_employee) }
    let!(:unrelated_admin_organization_2) { create(:admin_organization, business:, client_employee: unrelated_client_employee_2) }

    context "when business has profile for organization" do
      let!(:organization_profile) { create(:organization_profile, organization:, business:) }

      it "renders a successful response" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/organizations/#{organization.id}/admins",
          "/client/v2/organizations/#{organization.id}/admins"
        ].sample
        get(path, headers:)

        expect(response).to be_successful
        expect(response_hash.map(&:keys)).to all(match_array(serialized_keys))
        expect(response_hash.pluck("id")).to match_array([client_employee.id, client_employee_2.id])
      end
    end

    context "when business has no profile for organization" do
      it "renders successful and returns empty" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/organizations/#{organization.id}/admins",
          "/client/v2/organizations/#{organization.id}/admins"
        ].sample
        get(path, headers:)

        expect(response).to be_successful
        expect(response_hash).to be_empty
      end
    end
  end
end
