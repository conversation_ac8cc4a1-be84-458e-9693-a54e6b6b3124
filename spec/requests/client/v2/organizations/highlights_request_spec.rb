require "rails_helper"

RSpec.describe Client::V2::Organizations::HighlightsController, type: :request do
  let(:business) { create(:business) }

  let!(:client_employee) { create(:client_employee, :admin_lecupon) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end
  let(:serializer_keys) { %w[id organization] }
  let(:response_hash) { JSON.parse(response.body) }

  describe "#index" do
    let!(:business) { create(:business, :lecupon_equipe) }
    let!(:organization_one) { create(:organization) }
    let!(:organization_two) { create(:organization) }
    let!(:organization_three) { create(:organization) }
    let!(:org_one_pinned) { create(:highlight_organization, business:, organization: organization_one) }

    it "returns all the highlighted organizations of the business" do
      path = [
        "/client/v2/businesses/#{business.cnpj}/organizations/highlights",
        "/client/v2/organizations/highlights"
      ].sample
      get path, headers:, as: :json

      expect(response).to be_ok
      expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
    end
  end

  describe "#create" do
    let!(:business) { create(:business, :lecupon_equipe) }
    let!(:organization_one) { create(:organization) }
    let!(:blocklisted_organization) { create(:organization) }
    let!(:blocklist) { create(:organization_blocklist, organization: blocklisted_organization, business:) }

    it "returns successful and highlights the organization" do
      expect do
        path = [
          "/client/v2/businesses/#{business.cnpj}/organizations/#{organization_one.id}/highlights",
          "/client/v2/organizations/#{organization_one.id}/highlights"
        ].sample
        post path, headers:, as: :json
      end.to change(business.highlight_organizations, :count).by(1)

      expect(response).to be_created
      expect(response_hash.keys).to match_array(serializer_keys)
    end

    context "with blocklisted organization" do
      it "returns error and does not highlight organization" do
        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/organizations/#{blocklisted_organization.id}/highlights",
            "/client/v2/organizations/#{blocklisted_organization.id}/highlights"
          ].sample
          post path, headers:, as: :json
        end.to change(business.highlight_organizations, :count).by(0)

        expect(response).to be_unprocessable
        expect(response_hash["error"]).to include(I18n.t("organizations.highlights.create.blocklisted"))
      end
    end
  end

  describe "#destroy" do
    let!(:business) { create(:business, :lecupon_equipe) }
    let!(:organization_one) { create(:organization) }
    let!(:organization_two) { create(:organization) }
    let!(:organization_three) { create(:organization) }
    let!(:org_one_pinned) { create(:highlight_organization, business:, organization: organization_one) }

    it "returns successful and deletes the highlight from organization" do
      expect do
        path = [
          "/client/v2/businesses/#{business.cnpj}/organizations/#{organization_one.id}/highlights",
          "/client/v2/organizations/#{organization_one.id}/highlights"
        ].sample
        delete path, headers:, as: :json
      end.to change(HighlightOrganization, :count).by(-1)

      expect(response).to be_no_content
    end

    context "when trying to delete highlight with invalid organization id" do
      it "returns successful and does not delete the highlight from any organization" do
        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/organizations/0/highlights",
            "/client/v2/organizations/0/highlights"
          ].sample
          delete path, headers:, as: :json
        end.to change(HighlightOrganization, :count).by(0)

        expect(response).to be_no_content
      end
    end
  end
end
