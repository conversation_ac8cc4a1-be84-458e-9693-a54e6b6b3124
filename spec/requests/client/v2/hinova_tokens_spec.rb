require "rails_helper"

RSpec.describe Client::V2::HinovaTokensController, type: :request do
  let(:business) { create(:business) }

  let!(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  let(:response_hash) { JSON.parse(response.body) }

  let!(:serializer_keys) do
    %w[id business_id token authorized_user_group]
  end

  let!(:authorized_user_group) do
    create(:authorized_user_group, name: "foo", business:)
  end

  describe "#index" do
    let!(:hinova_token) { create(:hinova_token, business:) }
    let!(:hinova_token_2) { create(:hinova_token, business:, token: "valid_token_2") }

    it "renders a successful response" do
      path = [
        "/client/v2/businesses/#{business.cnpj}/hinova_tokens",
        "/client/v2/hinova_tokens"
      ].sample
      get(path, params: {cnpj: business.cnpj}, headers:)

      expect(response).to be_successful
      expect(response_hash.first.keys).to match_array(serializer_keys)
      expect(JSON.parse(response.body).count).to eq(2)
    end

    context "without client employee authorization" do
      it "renders unauthorized" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/hinova_tokens",
          "/client/v2/hinova_tokens"
        ].sample
        get path

        expect(response).to be_unauthorized
      end
    end

    context "with unrelated client employee authorization" do
      let(:business_two) { create(:business) }
      let!(:client_employee) { create(:client_employee, businesses: [business_two]) }

      it "renders not_found" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/hinova_tokens",
          "/client/v2/hinova_tokens"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:not_found)
        expect(response_hash["error"]).to eq("Business não encontrado")
      end
    end
  end

  describe "#show" do
    let!(:hinova_token) { create(:hinova_token, business:) }

    it "renders a successful response" do
      path = [
        "/client/v2/businesses/#{business.cnpj}/hinova_tokens/#{hinova_token.id}",
        "/client/v2/hinova_tokens/#{hinova_token.id}"
      ].sample
      get(path, params: {cnpj: business.cnpj}, headers:)

      expect(response).to be_successful
      expect(response_hash.keys).to match_array(serializer_keys)
    end

    context "without client employee authorization" do
      it "renders unauthorized" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/hinova_tokens/#{hinova_token.id}",
          "/client/v2/hinova_tokens/#{hinova_token.id}"
        ].sample
        get path

        expect(response).to be_unauthorized
      end
    end

    context "with unrelated client employee authorization" do
      let(:business_two) { create(:business) }
      let!(:client_employee) { create(:client_employee, businesses: [business_two]) }

      it "renders not_found" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/hinova_tokens/#{hinova_token.id}",
          "/client/v2/hinova_tokens/#{hinova_token.id}"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:not_found)
        expect(response_hash["error"]).to eq("Business não encontrado")
      end
    end
  end

  describe "#create", :vcr do
    context "with valid parameters" do
      let(:params) do
        {
          token: "valid_token",
          user_tag: authorized_user_group.slug
        }
      end

      it "creates a new HinovaToken" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/hinova_tokens",
          "/client/v2/hinova_tokens"
        ].sample
        post(path, params:, headers:)

        expect(response).to be_created
        expect(response_hash.keys).to match_array(serializer_keys)
      end
    end

    context "with invalid parameters" do
      let(:params) do
        {
          user_tag: nil,
          token: nil
        }
      end

      it "does not create a new HinovaToken" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/hinova_tokens",
          "/client/v2/hinova_tokens"
        ].sample
        post(path, params:, headers:)

        expect(response).to be_precondition_failed
      end
    end

    context "without client employee authorization" do
      let(:params) do
        {
          token: "valid_token",
          user_tag: authorized_user_group.slug
        }
      end

      it "renders unauthorized" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/hinova_tokens",
          "/client/v2/hinova_tokens"
        ].sample
        post path, params: {hinova_token: params}

        expect(response).to be_unauthorized
      end
    end

    context "with business unrelated to client employee" do
      context "when is lecupon admin" do
        let(:params) do
          {
            token: "valid_token",
            user_tag: authorized_user_group.slug
          }
        end

        let!(:client_employee) { create(:client_employee, :admin_lecupon) }

        it "creates a new HinovaToken" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/hinova_tokens",
            "/client/v2/hinova_tokens"
          ].sample
          post(path, params:, headers:)

          expect(response).to be_created
          expect(response_hash.keys).to match_array(serializer_keys)
        end
      end

      context "when is not lecupon admin" do
        let(:params) do
          {
            token: "valid_token",
            user_tag: authorized_user_group.slug
          }
        end

        let(:business_two) { create(:business) }
        let!(:client_employee) { create(:client_employee, businesses: [business_two]) }

        it "renders not_found" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/hinova_tokens",
            "/client/v2/hinova_tokens"
          ].sample
          post(path, params: {hinova_token: params}, headers:)

          expect(response).to have_http_status(:not_found)
          expect(response_hash["error"]).to eq("Business não encontrado")
        end
      end
    end
  end

  describe "#update", :vcr do
    let!(:hinova_token) { create(:hinova_token, business:) }

    context "with valid parameters" do
      let(:params) {
        {
          token: "other_token",
          user_tag: "valid_token"
        }
      }

      it "updates the requested hinova_token" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/hinova_tokens/#{hinova_token.id}",
          "/client/v2/hinova_tokens/#{hinova_token.id}"
        ].sample
        patch(path, params:, headers:)

        expect(response).to be_not_found
      end

      context "when with invalid token" do
        let(:validator_token_mock) { double(:validator_token_mock, errors: nil) }
        let(:params) do
          {
            token: "invalid_token",
            user_tag: authorized_user_group.slug
          }
        end

        before do
          allow(Hinova::Token::Validator).to receive(:new)
            .and_return(validator_token_mock)
          allow(validator_token_mock).to receive(:valid?)
            .and_return(false)
        end

        it "must return error" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/hinova_tokens/#{hinova_token.id}",
            "/client/v2/hinova_tokens/#{hinova_token.id}"
          ].sample
          patch(path, params:, headers:)

          expect(response).to be_unprocessable
          expect(JSON.parse(response.body).dig("error")).to eq("Token inválido")
        end
      end
    end

    context "with invalid parameters" do
      let(:params) do
        {
          user_tag: nil,
          token: nil
        }
      end

      it "renders error" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/hinova_tokens/#{hinova_token.id}",
          "/client/v2/hinova_tokens/#{hinova_token.id}"
        ].sample
        patch(path, params:, headers:)

        expect(response).to be_precondition_failed
      end
    end

    context "without client employee authorization" do
      let(:params) {
        {
          token: "other_token",
          user_tag: "valid_token"
        }
      }

      it "renders unauthorized" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/hinova_tokens/#{hinova_token.id}",
          "/client/v2/hinova_tokens/#{hinova_token.id}"
        ].sample
        patch(path, params:)

        expect(response).to be_unauthorized
      end
    end

    context "with business_id unrelated to client employee" do
      let(:params) {
        {
          token: "other_token",
          user_tag: "foo"
        }
      }

      context "when is lecupon admin" do
        let!(:client_employee) { create(:client_employee, :admin_lecupon) }

        it "updates the requested hinova_token" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/hinova_tokens/#{hinova_token.id}",
            "/client/v2/hinova_tokens/#{hinova_token.id}"
          ].sample
          patch(path, params:, headers:)

          hinova_token.reload
          expect(response_hash.keys).to match_array(serializer_keys)
          expect(hinova_token.token).to eq("other_token")
        end
      end

      context "when is not lecupon admin" do
        let(:business_two) { create(:business) }

        let!(:client_employee) { create(:client_employee, businesses: [business_two]) }

        it "renders not_found" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/hinova_tokens/#{hinova_token.id}",
            "/client/v2/hinova_tokens/#{hinova_token.id}"
          ].sample
          patch(path, params:, headers:)

          expect(response).to have_http_status(:not_found)
          expect(response_hash["error"]).to eq("Business não encontrado")
        end
      end
    end
  end

  describe "#destroy" do
    let!(:hinova_token) { create(:hinova_token, business:) }

    it "destroys the requested hinova_token" do
      path = [
        "/client/v2/businesses/#{business.cnpj}/hinova_tokens/#{hinova_token.id}",
        "/client/v2/hinova_tokens/#{hinova_token.id}"
      ].sample
      delete(path, headers:)

      expect(response).to be_no_content
    end

    context "without client employee authorization" do
      it "renders unauthorized" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/hinova_tokens/#{hinova_token.id}",
          "/client/v2/hinova_tokens/#{hinova_token.id}"
        ].sample
        delete path

        expect(response).to be_unauthorized
      end
    end

    context "with business unrelated to client employee" do
      context "when is lecupon admin" do
        let!(:client_employee) { create(:client_employee, :admin_lecupon) }

        it "destroys the requested hinova_token" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/hinova_tokens/#{hinova_token.id}",
            "/client/v2/hinova_tokens/#{hinova_token.id}"
          ].sample
          delete(path, headers:)

          expect(response).to be_successful
        end
      end

      context "when is not lecupon admin" do
        let(:business_two) { create(:business) }
        let!(:client_employee) { create(:client_employee, businesses: [business_two]) }

        it "renders not_found" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/hinova_tokens/#{hinova_token.id}",
            "/client/v2/hinova_tokens/#{hinova_token.id}"
          ].sample
          delete(path, headers:)

          expect(response).to have_http_status(:not_found)
          expect(response_hash["error"]).to eq("Business não encontrado")
        end
      end
    end
  end
end
