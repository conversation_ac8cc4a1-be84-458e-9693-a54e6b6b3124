# frozen_string_literal: true

require "rails_helper"

RSpec.describe Client::V2::Orders::CashbacksController, type: :request do
  let(:response_body) { JSON.parse(response.body) }
  let(:business) { create(:business, cashback: true, spread_percent: 0) }
  let(:user) { create(:user, business:) }
  let(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  describe "#index" do
    let(:promotion) { create(:promotion, cashback_type: :percent, cashback_value: 10) }
    let(:coupon) { create(:cupon, promotion:) }
    let(:order) { create(:order, user:, cupon: coupon, external_id: SecureRandom.uuid) }
    let!(:cashback) { create(:cashback_record, :approved, user:, order:, order_amount: 120.38) }
    let(:order_not_created_by_business) { create(:order, user:, external_id: nil) }
    let!(:unrelated_cashback) { create(:cashback_record, order: order_not_created_by_business, user:) }

    it "renders ok" do
      path = [
        "/client/v2/businesses/#{business.id}/orders/#{order.id}/cashbacks",
        "/client/v2/orders/#{order.id}/cashbacks"
      ].sample
      get(path, headers:)

      expect(response).to be_ok
      expect(response_body).to eq([
        "id" => cashback.id,
        "order_id" => order.id,
        "order_amount" => 120.38,
        "cashback_amount" => 12.04,
        "status" => "approved"
      ])
    end
  end
end
