require "rails_helper"
require "sidekiq/testing"

RSpec.describe Client::V2::AuthorizedUserImportFiles::ErrorsController, type: :request do
  let(:business) { create(:business) }
  let(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end
  let(:response_hash) { JSON.parse(response.body) }
  let(:path) do
    [
      "/client/v2/businesses/#{business.cnpj}/authorized_users/file_imports/#{authorized_user_import_file.id}/errors",
      "/client/v2/authorized_users/file_imports/#{authorized_user_import_file.id}/errors"
    ].sample
  end
  let(:serialized_keys) { %w[id line error_details] }

  let(:auth_user_list_csv) do
    tempfile = Tempfile.new(["auth_user_test", ".csv"], "tmp")
    CSV.open(tempfile, "w", headers: true, col_sep: ",") do |csv|
      csv << ["name", "cpf", "email", "custom_field_1"]
      (1..4).collect do |_user|
        csv << [FFaker::Name.name, CPF.new(FFaker::IdentificationBR.cpf).stripped, FFaker::Internet.email, "metadata"]
      end
    end

    tempfile
  end

  let(:import_temp_file) { create(:import_temp_file, file: auth_user_list_csv) }
  let(:authorized_user_import_file) { create(:authorized_user_import_file, import_temp_file:, business:) }
  let!(:authorized_user_import_errors) do
    create_list(:authorized_user_import_error, 5,
      authorized_user_import_file:,
      line: 1,
      error_details: "any error")
  end

  describe "#index" do
    it "must return all auth_user import file errors" do
      get(path, headers:)

      expect(response_hash.count).to eq(5)
      expect(response_hash.map(&:keys)).to all(match_array(serialized_keys))
    end
  end
end
