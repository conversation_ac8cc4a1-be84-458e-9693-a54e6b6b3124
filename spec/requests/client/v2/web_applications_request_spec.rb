# frozen_string_literal: true

require "rails_helper"
require "action_policy/rspec"

RSpec.describe Client::V2::WebApplicationsController, type: :request do
  describe "#show" do
    let!(:business) { create(:business) }
    let(:headers) do
      Devise::JWT::TestHelpers.auth_headers({"Tenant-id": business.cnpj}, admin)
    end
    let(:serialized_keys) { %w[cloudfront_distribution_url certified_cnames valid_certificate certificate_attached_to_distribution setup] }
    let(:response_hash) { JSON.parse(response.body) }

    context "when admin is authenticated" do
      context "when admin has permission on business" do
        let(:admin) { create(:client_employee, :admin_lecupon) }

        it "must render web application" do
          path = [
            "/client/v2/businesses/#{business.id}/web_applications",
            "/client/v2/web_applications"
          ].sample
          get(path, headers:)

          expect(response).to have_http_status(:ok)
          expect(response_hash.keys).to match_array(serialized_keys)
        end
      end

      context "when admin does not have permission on business" do
        let(:admin) { create(:client_employee, :admin_client) }

        it "must be forbidden" do
          path = [
            "/client/v2/businesses/#{business.id}/web_applications",
            "/client/v2/web_applications"
          ].sample
          get(path, headers:)

          expect(response).to be_not_found
          expect(response_hash["error"]).to eq("Business não encontrado")
        end
      end
    end

    context "when admin is not authenticated" do
      it "must render unauthorized" do
        path = [
          "/client/v2/businesses/#{business.id}/web_applications",
          "/client/v2/web_applications"
        ].sample
        get(path)

        expect(response).to be_unauthorized
      end
    end
  end
end
