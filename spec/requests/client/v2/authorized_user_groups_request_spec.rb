require "rails_helper"

RSpec.describe Client::V2::AuthorizedUserGroupsController, type: :request do
  let!(:business) { create(:business) }
  let!(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end
  let(:serialized_keys) { %w[name slug] }

  let(:response_hash) { JSON.parse(response.body) }

  describe "#index" do
    before do
      @business_authorized_user_groups = create_list(:authorized_user_group, 2, business:)
      create_list(:authorized_user_group, 2)
    end

    it "returns all authorized_user_groups of the business" do
      path = [
        "/client/v2/businesses/#{business.cnpj}/user_tags",
        "/client/v2/user_tags"
      ].sample
      get(path, headers:)

      expect(response).to be_ok
      expect(response_hash.pluck("slug")).to match_array(@business_authorized_user_groups.pluck(:slug))
      expect(response_hash.map(&:keys)).to all(match_array(serialized_keys))
    end

    context "filter by node_id" do
      let!(:authorized_user_group) { create(:authorized_user_group, node_id: "12345", business:) }
      let(:filter_params) do
        {
          node_id: "12345"
        }
      end
      context "when node id is present" do
        it "returns authorized_user of the business" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/user_tags",
            "/client/v2/user_tags"
          ].sample
          get(path, params: filter_params, headers:)

          expect(response).to be_ok
          expect(response_hash.pluck("slug")).to match_array([authorized_user_group.slug])
          expect(response_hash.map(&:keys)).to all(match_array(serialized_keys))
        end
      end
    end
  end

  describe "#create" do
    let(:params) {
      {
        name: "New name"
      }
    }

    context "when succesfully sync group with node" do
      it "creates authorized_user_group of the business" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/user_tags",
          "/client/v2/user_tags"
        ].sample
        post(path, params:, headers:)

        expect(response).to be_created
        expect(response_hash.keys).to match_array(serialized_keys)
        expect(response_hash["name"]).to eq(params[:name])
        expect(response_hash["slug"]).to eq(params[:name].parameterize)
      end
    end
  end
end
