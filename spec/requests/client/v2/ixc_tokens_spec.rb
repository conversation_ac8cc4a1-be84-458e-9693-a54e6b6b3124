require "rails_helper"

RSpec.describe Client::V2::IxcTokensController, type: :request do
  let(:business) { create(:business) }
  let(:authorized_user_group) { create(:authorized_user_group, business:) }

  let(:valid_attributes) {
    {
      base_url: "https://website.com.br",
      token: "116:19483117791b8d318ae00670352672cafc845d010ea21a366cb856c521c20c84",
      user_tag: authorized_user_group.slug,
      business_id: business.id
    }
  }

  let(:invalid_attributes) {
    {
      base_url: nil,
      token: "19483117791b8d318ae00670352672cafc845d010ea21a366cb856c521c20c84",
      user_tag: authorized_user_group.slug,
      business_id: business.id
    }
  }

  let(:response_hash) { JSON.parse(response.body) }
  let(:serialized_keys) { %w[id token base_url authorized_user_group product_id] }

  let!(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  before do
    allow(Slack::Api).to receive(:send_message)
  end

  describe "#show" do
    let!(:ixc_token) { create(:ixc_token, business:) }

    it "renders a successful response" do
      path = [
        "/client/v2/businesses/#{business.cnpj}/ixc_tokens",
        "/client/v2/ixc_tokens"
      ].sample
      get(path, params: {cnpj: business.cnpj}, headers:)

      expect(response_hash.keys).to match_array(serialized_keys)
      expect(response).to be_successful
    end

    context "without client employee authorization" do
      it "renders unauthorized" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/ixc_tokens",
          "/client/v2/ixc_tokens"
        ].sample
        get path

        expect(response).to be_unauthorized
      end
    end

    context "with unrelated client employee authorization" do
      let(:business_two) { create(:business) }
      let!(:client_employee) { create(:client_employee, businesses: [business_two]) }

      it "renders not_found" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/ixc_tokens",
          "/client/v2/ixc_tokens"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:not_found)
        expect(response_hash["error"]).to eq("Business não encontrado")
      end
    end

    context "without ixc token" do
      before { ixc_token.destroy! }

      it "returns error" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/ixc_tokens",
          "/client/v2/ixc_tokens"
        ].sample
        get(path, params: {cnpj: business.cnpj}, headers:)

        expect(response).to have_http_status(:not_found)
      end
    end
  end

  describe "POST /create" do
    let(:business) { create(:business) }

    let(:mock_token_validator) do
      double(:mock_token_validator, valid?: credentials_validate, errors: nil)
    end

    context "with valid parameters" do
      let(:credentials_validate) { true }

      before do
        allow(Ixc::Token::Validator).to receive(:new).and_return(mock_token_validator)
      end

      it "creates a new IxcToken" do
        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/ixc_tokens",
            "/client/v2/ixc_tokens"
          ].sample
          post(path, params: valid_attributes, headers:)

          expect(response_hash.keys).to match_array(serialized_keys)
        end.to change(IxcToken, :count).by(1)

        ixc_token = IxcToken.find(response_hash["id"])
        expect(ixc_token.authorized_user_group.id).to eq(ixc_token.authorized_user_group_id)
      end
    end

    context "with invalid parameters" do
      let(:credentials_validate) { false }

      before do
        allow(Ixc::Token::Validator).to receive(:new).and_return(mock_token_validator)
      end

      it "does not create a new IxcToken" do
        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/ixc_tokens",
            "/client/v2/ixc_tokens"
          ].sample
          post path, params: invalid_attributes, headers:
        end.to change(IxcToken, :count).by(0)
      end

      it "renders a error" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/ixc_tokens",
          "/client/v2/ixc_tokens"
        ].sample
        post(path, params: invalid_attributes, headers:)

        expect(response).to be_unprocessable
      end
    end

    context "without client employee authorization" do
      it "renders unauthorized" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/ixc_tokens",
          "/client/v2/ixc_tokens"
        ].sample
        post path, params: valid_attributes

        expect(response).to be_unauthorized
      end
    end

    context "with business unrelated to client employee" do
      context "when is lecupon admin" do
        let(:credentials_validate) { true }
        let!(:client_employee) { create(:client_employee, :admin_lecupon) }

        before do
          allow(Ixc::Token::Validator).to receive(:new).and_return(mock_token_validator)
        end

        it "creates a new IxcToken" do
          expect do
            path = [
              "/client/v2/businesses/#{business.cnpj}/ixc_tokens",
              "/client/v2/ixc_tokens"
            ].sample
            post path, params: valid_attributes, headers:
          end.to change(IxcToken, :count).by(1)
        end
      end

      context "when is not lecupon admin" do
        let(:business_two) { create(:business) }

        let!(:client_employee) { create(:client_employee, businesses: [business_two]) }

        it "renders not_found" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/ixc_tokens",
            "/client/v2/ixc_tokens"
          ].sample
          post(path, params: valid_attributes, headers:)

          expect(response).to have_http_status(:not_found)
          expect(response_hash["error"]).to eq("Business não encontrado")
        end
      end
    end
  end

  describe "#update" do
    let!(:ixc_token) { create(:ixc_token, business:) }

    let(:new_attributes) {
      {base_url: "https://any_other_url.com/"}
    }

    context "with valid parameters" do
      it "updates the requested ixc_token" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/ixc_tokens",
          "/client/v2/ixc_tokens"
        ].sample
        patch(path, params: new_attributes, headers:)

        expect(response_hash.keys).to match_array(serialized_keys)
        ixc_token.reload
        expect(ixc_token.base_url).to eq("https://any_other_url.com/")
      end
    end

    context "with invalid parameters" do
      it "renders error" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/ixc_tokens",
          "/client/v2/ixc_tokens"
        ].sample
        patch(path, params: invalid_attributes, headers:)

        expect(response).to be_unprocessable
      end
    end

    context "without client employee authorization" do
      it "renders unauthorized" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/ixc_tokens",
          "/client/v2/ixc_tokens"
        ].sample
        patch path, params: new_attributes

        expect(response).to be_unauthorized
      end
    end

    context "with business_id unrelated to client employee" do
      context "when is lecupon admin" do
        let!(:client_employee) { create(:client_employee, :admin_lecupon) }

        it "updates the requested ixc_token" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/ixc_tokens",
            "/client/v2/ixc_tokens"
          ].sample
          patch(path, params: new_attributes, headers:)

          ixc_token.reload
          expect(ixc_token.base_url).to eq("https://any_other_url.com/")
        end
      end

      context "when is not lecupon admin" do
        let(:business_two) { create(:business) }
        let!(:client_employee) { create(:client_employee, businesses: [business_two]) }

        it "renders not_found" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/ixc_tokens",
            "/client/v2/ixc_tokens"
          ].sample
          patch(path, params: new_attributes, headers:)

          expect(response).to have_http_status(:not_found)
          expect(response_hash["error"]).to eq("Business não encontrado")
        end
      end
    end

    context "without ixc token" do
      before { ixc_token.destroy! }

      it "returns error" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/ixc_tokens",
          "/client/v2/ixc_tokens"
        ].sample
        patch(path, params: new_attributes, headers:)

        expect(response).to have_http_status(:not_found)
      end
    end
  end

  describe "#destroy" do
    let!(:ixc_token) { create(:ixc_token, business:) }
    let!(:ixc_contracts) { create(:ixc_contract, ixc_token:) }

    it "destroys the requested ixc_token" do
      expect do
        path = [
          "/client/v2/businesses/#{business.cnpj}/ixc_tokens",
          "/client/v2/ixc_tokens"
        ].sample
        delete(path, headers:)

        expect(response).to be_no_content
      end.to change(IxcToken, :count).by(-1).and change(IxcContract, :count).by(-1)
    end

    context "without client employee authorization" do
      it "renders unauthorized" do
        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/ixc_tokens",
            "/client/v2/ixc_tokens"
          ].sample
          delete path

          expect(response).to be_unauthorized
        end.not_to change(IxcToken, :count)
      end
    end

    context "with business unrelated to client employee" do
      context "when is lecupon admin" do
        let!(:client_employee) { create(:client_employee, :admin_lecupon) }

        it "destroys the requested ixc_token" do
          expect do
            path = [
              "/client/v2/businesses/#{business.cnpj}/ixc_tokens",
              "/client/v2/ixc_tokens"
            ].sample
            delete(path, headers:)

            expect(response).to be_no_content
          end.to change(IxcToken, :count).by(-1)
        end
      end

      context "when is not lecupon admin" do
        let(:business_two) { create(:business) }

        let!(:client_employee) { create(:client_employee, businesses: [business_two]) }

        it "renders not_found" do
          expect do
            path = [
              "/client/v2/businesses/#{business.cnpj}/ixc_tokens",
              "/client/v2/ixc_tokens"
            ].sample
            delete(path, headers:)

            expect(response).to have_http_status(:not_found)
            expect(response_hash["error"]).to eq("Business não encontrado")
          end.not_to change(IxcToken, :count)
        end
      end
    end

    context "without ixc token" do
      before { ixc_token.destroy! }

      it "returns error" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/ixc_tokens",
          "/client/v2/ixc_tokens"
        ].sample
        delete(path, headers:)

        expect(response).to have_http_status(:not_found)
      end
    end
  end
end
