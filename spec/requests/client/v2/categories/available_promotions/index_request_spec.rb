require "rails_helper"

RSpec.describe Client::V2::Categories::AvailablePromotionsController, type: :request do
  let!(:business) { create(:business) }
  let!(:client_employee) { create(:client_employee, :admin_lecupon) }

  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end
  let(:serializer_keys) { %w[id description] }
  let(:json_parse_response_body) { JSON.parse(response.body) }

  describe "#index" do
    it "returns all available promotions to business" do
      organization = create(:organization, :active)
      promotion = create(:promotion, :exclusive, :available, organization:, business:)
      branch = create(:branch, :active, organization:)
      create(:cupon, :cpf, branch:, promotion:, organization:)

      params = {organization_id: organization.id}
      path = [
        "/client/v2/businesses/#{business.cnpj}/categories/available_promotions",
        "/client/v2/categories/available_promotions"
      ].sample
      get path,
        headers:,
        params:,
        as: :json

      expect(response).to be_ok
      expect(json_parse_response_body.map(&:keys)).to all(match_array(serializer_keys))
      expect(json_parse_response_body.count).to eq(1)
      expect(json_parse_response_body.pluck("id")).to eq([promotion.id])
    end

    context "when filtering by description" do
      it "returns promotion filtered by description" do
        organization = create(:organization, :active)
        promotion_one = create(:promotion, :exclusive, description: "foo", organization:, business:)
        _promotion_two = create(:promotion, :exclusive, description: "bar", organization:, business:)

        params = {organization_id: organization.id, term: "foo"}
        path = [
          "/client/v2/businesses/#{business.cnpj}/categories/available_promotions",
          "/client/v2/categories/available_promotions"
        ].sample
        get(path,
          as: :json,
          headers:,
          params:)

        expect(response).to be_ok
        expect(json_parse_response_body.map(&:keys)).to all(match_array(serializer_keys))
        expect(json_parse_response_body.count).to eq(1)
        expect(json_parse_response_body.pluck("id")).to eq([promotion_one.id])
      end
    end

    context "when promotion is not exclusive" do
      it "returns all available promotions" do
        organization = create(:organization, :active)
        promotion = create(:promotion, :available, :non_exclusive, organization:)
        branch = create(:branch, :active, organization:)
        create(:cupon, :cpf, branch:, promotion:, organization:)

        params = {organization_id: organization.id}
        path = [
          "/client/v2/businesses/#{business.cnpj}/categories/available_promotions",
          "/client/v2/categories/available_promotions"
        ].sample
        get path,
          headers:,
          params:,
          as: :json

        expect(response).to be_ok
        expect(json_parse_response_body.map(&:keys)).to all(match_array(serializer_keys))
        expect(json_parse_response_body.count).to eq(1)
        expect(json_parse_response_body.pluck("id")).to eq([promotion.id])
      end
    end

    context "when promotion does not have branches" do
      it "returns all available promotions" do
        organization = create(:organization, :active)
        promotion = create(:promotion, :available, :non_exclusive, organization:)

        params = {organization_id: organization.id}
        path = [
          "/client/v2/businesses/#{business.cnpj}/categories/available_promotions",
          "/client/v2/categories/available_promotions"
        ].sample
        get path,
          headers:,
          params:,
          as: :json

        expect(response).to be_ok
        expect(json_parse_response_body.map(&:keys)).to all(match_array(serializer_keys))
        expect(json_parse_response_body.count).to eq(1)
        expect(json_parse_response_body.pluck("id")).to eq([promotion.id])
      end
    end

    context "when promotion does not have a active coupon" do
      it "returns all available promotions" do
        organization = create(:organization, :active)
        promotion = create(:promotion, :available, :non_exclusive, organization:)
        branch = create(:branch, :inactive, organization:)
        create(:cupon, :cpf, branch:, promotion:, organization:)

        params = {organization_id: organization.id}
        path = [
          "/client/v2/businesses/#{business.cnpj}/categories/available_promotions",
          "/client/v2/categories/available_promotions"
        ].sample
        get path,
          headers:,
          params:,
          as: :json

        expect(response).to be_ok
        expect(json_parse_response_body.map(&:keys)).to all(match_array(serializer_keys))
        expect(json_parse_response_body.count).to eq(1)
        expect(json_parse_response_body.pluck("id")).to eq([promotion.id])
      end
    end

    context "when organization is blocklisted" do
      it "returns empty" do
        organization = create(:organization, :active)
        create(:organization_blocklist, business:, organization:)
        create(:promotion, :available, :non_exclusive, organization:)

        params = {organization_id: organization.id}
        path = [
          "/client/v2/businesses/#{business.cnpj}/categories/available_promotions",
          "/client/v2/categories/available_promotions"
        ].sample
        get path,
          headers:,
          params:,
          as: :json

        expect(response).to have_http_status(:ok)
        expect(json_parse_response_body.count).to eq(0)
      end
    end

    context "when organization is inactive" do
      it "returns empty" do
        organization = create(:organization, :inactive)
        create(:promotion, :available, :non_exclusive, organization:)

        params = {organization_id: organization.id}
        path = [
          "/client/v2/businesses/#{business.cnpj}/categories/available_promotions",
          "/client/v2/categories/available_promotions"
        ].sample
        get path,
          headers:,
          params:,
          as: :json

        expect(response).to have_http_status(:ok)
        expect(json_parse_response_body.count).to eq(0)
      end
    end

    context "when organization_id param is empty" do
      it "returns empty" do
        organization = create(:organization, :inactive)
        create(:promotion, :available, :non_exclusive, organization:)

        params = {organization_id: nil}
        path = [
          "/client/v2/businesses/#{business.cnpj}/categories/available_promotions",
          "/client/v2/categories/available_promotions"
        ].sample
        get path,
          headers:,
          params:,
          as: :json

        expect(response).to have_http_status(:precondition_failed)
        expect(json_parse_response_body["error"]).to eq("A requisição falhou devido a ausência de parâmetro: organization_id")
      end
    end
  end
end
