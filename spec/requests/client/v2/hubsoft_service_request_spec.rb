require "rails_helper"
require "support/shared_examples/client_employee_request_shared_examples"

RSpec.describe Client::V2::HubsoftServicesController, type: :request do
  let(:business) { create(:business, :hubsoft_business) }

  let(:valid_attributes) {
    {
      name: "Plano X",
      external_id: "70",
      hubsoft_token_id: business.hubsoft_token.id
    }
  }

  let(:invalid_attributes) {
    {
      name: "Plano X",
      external_id: nil,
      hubsoft_token_id: business.hubsoft_token.id
    }
  }

  let(:response_hash) { JSON.parse(response.body) }
  let(:serialized_keys) { %w[id name external_id] }

  let!(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  describe "#index" do
    let!(:hubsoft_services) { create_list(:hubsoft_service, 2, hubsoft_token: business.hubsoft_token) }

    it "renders a successful response" do
      path = [
        "/client/v2/businesses/#{business.cnpj}/hubsoft_services",
        "/client/v2/hubsoft_services"
      ].sample
      get(path, params: {cnpj: business.cnpj}, headers:)

      expect(response).to be_successful
      expect(response_hash.map(&:keys)).to all(match_array(serialized_keys))
      expect(JSON.parse(response.body).pluck("external_id")).to match_array(hubsoft_services.pluck(:external_id))
    end

    context "without client employee authorization" do
      before do
        path = [
          "/client/v2/businesses/#{business.cnpj}/hubsoft_services",
          "/client/v2/hubsoft_services"
        ].sample
        get path
      end

      it_behaves_like "unauthorized client_employee"
    end

    context "when business unrelated to the current user" do
      let(:client_employee) { create(:client_employee, :admin_client) }

      it "returns not found" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/hubsoft_services",
          "/client/v2/hubsoft_services"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:not_found)
        expect(response_hash["error"]).to eq("Business não encontrado")
      end
    end
  end

  describe "#create" do
    let(:business) { create(:business, :hubsoft_business) }

    context "with valid parameters" do
      it "creates a new HubsoftToken" do
        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/hubsoft_services",
            "/client/v2/hubsoft_services"
          ].sample
          post(path, params: {hubsoft_service: valid_attributes}, headers:)

          expect(response).to be_successful
          expect(response_hash.keys).to match_array(serialized_keys)
          business.reload
        end.to change(HubsoftService, :count).by(1).and change(business.hubsoft_services, :count).by(1)
      end
    end

    context "with invalid parameters" do
      it "does not create a new HubsoftToken" do
        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/hubsoft_services",
            "/client/v2/hubsoft_services"
          ].sample
          post path, params: {hubsoft_service: invalid_attributes}, headers:
        end.to change(HubsoftService, :count).by(0)
      end

      it "renders a error" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/hubsoft_services",
          "/client/v2/hubsoft_services"
        ].sample
        post(path, params: {hubsoft_service: invalid_attributes}, headers:)

        expect(response).to be_unprocessable
      end
    end

    context "without client employee authorization" do
      before do
        path = [
          "/client/v2/businesses/#{business.cnpj}/hubsoft_services",
          "/client/v2/hubsoft_services"
        ].sample
        post path, params: {hubsoft_service: valid_attributes}
      end

      it_behaves_like "unauthorized client_employee"
    end

    context "with business unrelated to client employee" do
      context "when is lecupon admin" do
        let!(:client_employee) { create(:client_employee, :admin_lecupon) }

        it "creates a new HubsoftToken" do
          expect do
            path = [
              "/client/v2/businesses/#{business.cnpj}/hubsoft_services",
              "/client/v2/hubsoft_services"
            ].sample
            post path, params: {hubsoft_service: valid_attributes}, headers:
          end.to change(HubsoftService, :count).by(1)
        end
      end

      context "when is not lecupon admin" do
        let(:client_employee) { create(:client_employee, :admin_client) }

        it "returns not found" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/hubsoft_services",
            "/client/v2/hubsoft_services"
          ].sample
          post(path, params: {hubsoft_service: valid_attributes}, headers:)

          expect(response).to have_http_status(:not_found)
          expect(response_hash["error"]).to eq("Business não encontrado")
        end
      end
    end
  end

  describe "#destroy" do
    let!(:hubsoft_service) { create(:hubsoft_service, hubsoft_token: business.hubsoft_token) }

    it "destroys the requested hubsoft_service" do
      expect do
        path = [
          "/client/v2/businesses/#{business.cnpj}/hubsoft_services/#{hubsoft_service.id}",
          "/client/v2/hubsoft_services/#{hubsoft_service.id}"
        ].sample
        delete(path, headers:)

        expect(response).to be_no_content
      end.to change(HubsoftService, :count).by(-1)
    end

    context "without client employee authorization" do
      before do
        path = [
          "/client/v2/businesses/#{business.cnpj}/hubsoft_services/#{hubsoft_service.id}",
          "/client/v2/hubsoft_services/#{hubsoft_service.id}"
        ].sample
        delete path
      end

      it_behaves_like "unauthorized client_employee"
    end

    context "with business unrelated to client employee" do
      context "when is lecupon admin" do
        let!(:client_employee) { create(:client_employee, :admin_lecupon) }

        it "destroys the requested hubsoft_service" do
          expect {
            path = [
              "/client/v2/businesses/#{business.cnpj}/hubsoft_services/#{hubsoft_service.id}",
              "/client/v2/hubsoft_services/#{hubsoft_service.id}"
            ].sample
            delete(path, headers:)

            expect(response).to be_no_content
          }.to change(HubsoftService, :count).by(-1)
        end
      end

      context "when is not lecupon admin" do
        let(:client_employee) { create(:client_employee, :admin_client) }

        it "returns not found" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/hubsoft_services/#{hubsoft_service.id}",
            "/client/v2/hubsoft_services/#{hubsoft_service.id}"
          ].sample
          delete(path, headers:)

          expect(response).to have_http_status(:not_found)
          expect(response_hash["error"]).to eq("Business não encontrado")
        end
      end
    end
  end
end
