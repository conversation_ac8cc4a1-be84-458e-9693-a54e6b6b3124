require "rails_helper"
require "support/shared_examples/client_employee_request_shared_examples"

RSpec.describe Client::V2::VoallePlansController, type: :request do
  let(:response_hash) { JSON.parse(response.body) }

  let(:business) { create(:business, :voalle_business) }

  let(:valid_attributes) {
    {
      name: "Plano X",
      external_id: "70",
      voalle_token_id: business.voalle_token.id
    }
  }

  let(:invalid_attributes) {
    {
      name: "Plano X",
      external_id: nil,
      voalle_token_id: business.voalle_token.id
    }
  }

  let(:serialized_keys) { %w[id name external_id] }

  let!(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  describe "GET /index" do
    let!(:voalle_plans) { create_list(:voalle_plan, 2, voalle_token: business.voalle_token) }

    it "renders a successful response" do
      path = [
        "/client/v2/businesses/#{business.cnpj}/voalle_plans",
        "/client/v2/voalle_plans"
      ].sample
      get(path, params: {cnpj: business.cnpj}, headers:)

      expect(response_hash.map(&:keys)).to all(match_array(serialized_keys))
      expect(response).to be_successful
      expect(response_hash.pluck("external_id")).to match_array(voalle_plans.pluck(:external_id))
    end

    context "without client employee authorization" do
      before do
        path = [
          "/client/v2/businesses/#{business.cnpj}/voalle_plans",
          "/client/v2/voalle_plans"
        ].sample
        get path
      end

      it_behaves_like "unauthorized client_employee"
    end

    context "when business unrelated to the current user" do
      let(:client_employee) { create(:client_employee, :admin_client) }

      it "returns not found" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/voalle_plans",
          "/client/v2/voalle_plans"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:not_found)
        expect(response_hash["error"]).to eq("Business não encontrado")
      end
    end
  end

  describe "#show" do
    let!(:voalle_plan) { create(:voalle_plan, voalle_token: business.voalle_token) }

    it "renders a successful response" do
      path = [
        "/client/v2/businesses/#{business.cnpj}/voalle_plans/#{voalle_plan.id}",
        "/client/v2/voalle_plans/#{voalle_plan.id}"
      ].sample
      get(path, params: {cnpj: business.cnpj}, headers:)

      expect(response_hash.keys).to match_array(serialized_keys)
      expect(response).to be_successful
      expect(response_hash["external_id"]).to eq(voalle_plan.external_id)
    end

    context "without client employee authorization" do
      it "renders unauthorized" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/voalle_plans/#{voalle_plan.id}",
          "/client/v2/voalle_plans/#{voalle_plan.id}"
        ].sample
        get path

        expect(response).to be_unauthorized
      end
    end

    context "with unrelated client employee authorization" do
      let(:business_two) { create(:business) }
      let!(:client_employee) { create(:client_employee, businesses: [business_two]) }

      it "renders forbidden" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/voalle_plans/#{voalle_plan.id}",
          "/client/v2/voalle_plans/#{voalle_plan.id}"
        ].sample
        get(path, headers:)

        expect(response).to be_not_found
        expect(response_hash["error"]).to eq("Business não encontrado")
      end
    end
  end

  describe "#create" do
    let(:business) { create(:business, :voalle_business) }

    context "with valid parameters" do
      it "creates a new VoalleToken" do
        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/voalle_plans",
            "/client/v2/voalle_plans"
          ].sample
          post(path, params: {voalle_plan: valid_attributes}, headers:)

          expect(response_hash.keys).to match_array(serialized_keys)
          expect(response).to be_successful
          business.reload
        end.to change(VoallePlan, :count).by(1).and change(business.voalle_plans, :count).by(1)
      end
    end

    context "with invalid parameters" do
      it "does not create a new VoalleToken" do
        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/voalle_plans",
            "/client/v2/voalle_plans"
          ].sample
          post path, params: {voalle_plan: invalid_attributes}, headers:
        end.to change(VoallePlan, :count).by(0)
      end

      it "renders a error" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/voalle_plans",
          "/client/v2/voalle_plans"
        ].sample
        post(path, params: {voalle_plan: invalid_attributes}, headers:)

        expect(response).to be_unprocessable
      end
    end

    context "without client employee authorization" do
      it "renders unauthorized" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/voalle_plans",
          "/client/v2/voalle_plans"
        ].sample
        post path, params: {voalle_plan: valid_attributes}

        expect(response).to be_unauthorized
      end
    end

    context "with business unrelated to client employee" do
      context "when is lecupon admin" do
        let!(:client_employee) { create(:client_employee, :admin_lecupon) }

        it "creates a new VoalleToken" do
          expect do
            path = [
              "/client/v2/businesses/#{business.cnpj}/voalle_plans",
              "/client/v2/voalle_plans"
            ].sample
            post path, params: {voalle_plan: valid_attributes}, headers:
          end.to change(VoallePlan, :count).by(1)
        end
      end

      context "when is not lecupon admin" do
        let(:business_two) { create(:business) }
        let!(:client_employee) { create(:client_employee, businesses: [business_two]) }

        it "renders forbidden" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/voalle_plans",
            "/client/v2/voalle_plans"
          ].sample
          post(path, params: {voalle_plan: valid_attributes}, headers:)

          expect(response).to be_not_found
          expect(response_hash["error"]).to eq("Business não encontrado")
        end
      end
    end
  end

  describe "#update" do
    let!(:voalle_plan) { create(:voalle_plan, voalle_token: business.voalle_token) }

    let(:new_attributes) do
      {name: "Plano Z"}
    end

    context "with valid parameters" do
      it "updates the requested voalle_plan" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/voalle_plans/#{voalle_plan.id}",
          "/client/v2/voalle_plans/#{voalle_plan.id}"
        ].sample
        patch(path, params: {voalle_plan: new_attributes}, headers:)

        expect(response_hash.keys).to match_array(serialized_keys)
        voalle_plan.reload
        expect(voalle_plan.name).to eq("Plano Z")
      end
    end

    context "with invalid parameters" do
      it "renders error" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/voalle_plans/#{voalle_plan.id}",
          "/client/v2/voalle_plans/#{voalle_plan.id}"
        ].sample
        patch(path, params: {voalle_plan: invalid_attributes}, headers:)

        expect(response).to be_unprocessable
      end
    end

    context "without client employee authorization" do
      it "renders unauthorized" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/voalle_plans/#{voalle_plan.id}",
          "/client/v2/voalle_plans/#{voalle_plan.id}"
        ].sample
        patch path, params: {voalle_plan: new_attributes}

        expect(response).to be_unauthorized
      end
    end

    context "with business_id unrelated to client employee" do
      context "when is lecupon admin" do
        let!(:client_employee) { create(:client_employee, :admin_lecupon) }

        it "updates the requested voalle_plan" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/voalle_plans/#{voalle_plan.id}",
            "/client/v2/voalle_plans/#{voalle_plan.id}"
          ].sample
          patch(path, params: {voalle_plan: new_attributes}, headers:)
          voalle_plan.reload

          expect(voalle_plan.name).to eq("Plano Z")
        end
      end

      context "when is not lecupon admin" do
        let(:business_two) { create(:business) }
        let!(:client_employee) { create(:client_employee, businesses: [business_two]) }

        it "renders forbidden" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/voalle_plans/#{voalle_plan.id}",
            "/client/v2/voalle_plans/#{voalle_plan.id}"
          ].sample
          patch(path, params: {voalle_plan: new_attributes}, headers:)

          expect(response).to be_not_found
          expect(response_hash["error"]).to eq("Business não encontrado")
        end
      end
    end
  end

  describe "#destroy" do
    let!(:voalle_plan) { create(:voalle_plan, voalle_token: business.voalle_token) }

    it "destroys the requested voalle_plan" do
      expect {
        path = [
          "/client/v2/businesses/#{business.cnpj}/voalle_plans/#{voalle_plan.id}",
          "/client/v2/voalle_plans/#{voalle_plan.id}"
        ].sample
        delete(path, headers:)

        expect(response).to be_no_content
      }.to change(VoallePlan, :count).by(-1)
    end

    context "without client employee authorization" do
      it "renders unauthorized" do
        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/voalle_plans/#{voalle_plan.id}",
            "/client/v2/voalle_plans/#{voalle_plan.id}"
          ].sample
          delete path

          expect(response).to be_unauthorized
        end.not_to change(VoallePlan, :count)
      end
    end

    context "with business unrelated to client employee" do
      context "when is lecupon admin" do
        let!(:client_employee) { create(:client_employee, :admin_lecupon) }

        it "destroys the requested voalle_plan" do
          expect do
            path = [
              "/client/v2/businesses/#{business.cnpj}/voalle_plans/#{voalle_plan.id}",
              "/client/v2/voalle_plans/#{voalle_plan.id}"
            ].sample
            delete(path, headers:)
            expect(response).to be_no_content
          end.to change(VoallePlan, :count).by(-1)
        end
      end

      context "when is not lecupon admin" do
        let(:business_two) { create(:business) }
        let!(:client_employee) { create(:client_employee, businesses: [business_two]) }

        it "renders forbidden" do
          expect do
            path = [
              "/client/v2/businesses/#{business.cnpj}/voalle_plans/#{voalle_plan.id}",
              "/client/v2/voalle_plans/#{voalle_plan.id}"
            ].sample
            delete(path, headers:)

            expect(response).to be_not_found
            expect(response_hash["error"]).to eq("Business não encontrado")
          end.not_to change(VoallePlan, :count)
        end
      end
    end
  end
end
