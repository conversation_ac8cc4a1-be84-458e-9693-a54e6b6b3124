require "rails_helper"
require "sidekiq/testing"

RSpec.describe Client::V2::AuthorizedUsersController, type: :request do
  let!(:business) { create(:business, main_business:) }
  let!(:main_business) { create(:business) }
  let!(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end
  let(:serialized_keys) do
    %w[id name taxpayer_number email phone active created_at updated_at activated_at default_auth_flow
      main_business_id user_tags custom_field_1 custom_field_2 custom_field_3 custom_field_4
      custom_field_5 custom_field_6 custom_field_7 custom_field_8 telemedicine tags]
  end
  let(:response_hash) { JSON.parse(response.body) }

  describe "#create" do
    let(:attributes) {
      {
        name: "New name",
        cpf: "***********",
        email: "<EMAIL>",
        phone: "3198877665",
        custom_field_1: "metadata",
        business_id: business.id,
        main_business_id: business.main_business_id,
        default_auth_flow: true,
        user_tags: ["any_group_tag"]
      }
    }
    let(:sync_mock) { double("sync_mock", call: true) }

    it "creates authorized_user of the business" do
      path = [
        "/client/v2/businesses/#{business.cnpj}/authorized_users",
        "/client/v2/authorized_users"
      ].sample
      post(path, params: attributes, headers:)

      expect(response).to have_http_status(:ok)
      expect(response_hash.keys).to match_array(serialized_keys)
      expect(response_hash["name"]).to eq(Utils::NameNormalizer.call(attributes[:name]))
      expect(response_hash["user_tags"][0]["name"]).to eq(attributes[:user_tags][0])
    end

    context "when creating authorized_user with banned cpf" do
      before do
        BannedCpf.create!(cpf: attributes[:cpf])
      end

      it "must render error" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/authorized_users",
          "/client/v2/authorized_users"
        ].sample
        post(path, params: attributes, headers:)

        expect(response_hash["error"]).to include(I18n.t("user.sign_up.banned_cpf"))
      end
    end

    context "when creating an already existing user" do
      let!(:authorized_user) do
        create(:authorized_user, business:, active: false, **attributes)
      end

      it "reactivates the authorized user" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/authorized_users",
          "/client/v2/authorized_users"
        ].sample
        post(path, params: attributes, headers:)

        expect(response).to have_http_status(:ok)
        expect(response_hash.keys).to match_array(serialized_keys)
        expect(response_hash["name"]).to eq(Utils::NameNormalizer.call(attributes[:name]))
        authorized_user.reload
        expect(authorized_user.active).to eq(true)
        expect(authorized_user.default_auth_flow).to eq(true)
      end
    end
  end
end
