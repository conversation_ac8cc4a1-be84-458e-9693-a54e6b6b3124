require "rails_helper"
require "sidekiq/testing"

RSpec.describe Client::V2::AuthorizedUsersController, type: :request do
  let!(:business) { create(:business, main_business:) }
  let!(:main_business) { create(:business) }
  let!(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end
  let(:serialized_keys) do
    %w[id name taxpayer_number email phone active created_at updated_at activated_at default_auth_flow
      main_business_id user_tags custom_field_1 custom_field_2 custom_field_3 custom_field_4
      custom_field_5 custom_field_6 custom_field_7 custom_field_8 telemedicine tags]
  end
  let(:response_hash) { JSON.parse(response.body) }

  describe "#index" do
    let!(:authorized_user_group) { create(:authorized_user_group, business:, name: "Grupo") }
    let!(:authorized_user) do
      create(:authorized_user, name: "Any Name", business:, authorized_user_group:)
    end
    let!(:authorized_user_registered) do
      create(:authorized_user, business:, authorized_user_group:, registered_at: 1.day.ago)
    end
    let!(:user) { create(:user, business:, authorized_user: authorized_user_registered, cpf: authorized_user_registered.cpf) }
    let(:params) do
      {
        active: "true",
        term: "any",
        user_tags: [authorized_user_group.slug],
        page: 1
      }
    end

    it "returns all authorized_user of the business" do
      path = [
        "/client/v2/businesses/#{business.cnpj}/authorized_users",
        "/client/v2/authorized_users"
      ].sample
      get(path, headers:, params:)

      expect(response).to have_http_status(:ok)
      expect(response_hash.map(&:keys)).to all(match_array(serialized_keys))
      expect(response_hash.pluck("id")).to match_array([authorized_user].pluck(:id))
      expect(response_hash.first["user_tags"].first).to include({"name" => "Grupo"})
    end
  end
end
