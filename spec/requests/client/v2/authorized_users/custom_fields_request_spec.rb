require "rails_helper"
require "support/shared_examples/client_employee_request_shared_examples"

RSpec.describe Client::V2::AuthorizedUsers::CustomFieldsController, type: :request do
  let!(:business) { create(:business) }

  let(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end
  let(:response_hash) { JSON.parse(response.body) }
  let(:path) do
    [
      "/client/v2/businesses/#{business.cnpj}/authorized_users/custom_fields",
      "/client/v2/authorized_users/custom_fields"
    ].sample
  end

  describe "GET /client/v2/businesses/:identifier/authorized_users/custom_fields" do
    let!(:authorized_user_one) do
      create(:authorized_user,
        business:,
        custom_field_1: "metadata_1",
        custom_field_2: "metadata_2",
        custom_field_3: "metadata_3",
        custom_field_4: "metadata_4",
        custom_field_5: "metadata_5",
        custom_field_6: "metadata_6",
        custom_field_7: "metadata_7",
        custom_field_8: "metadata_8")
    end
    let!(:authorized_user_two) do
      create(:authorized_user,
        business:,
        custom_field_1: "metadata_a",
        custom_field_2: "metadata_b",
        custom_field_3: "metadata_c",
        custom_field_4: "metadata_d",
        custom_field_5: "metadata_e",
        custom_field_6: "metadata_f",
        custom_field_7: "metadata_g",
        custom_field_8: "metadata_h")
    end
    let!(:authorized_user_three) do
      create(:authorized_user,
        business:,
        custom_field_1: "metadata_1",
        custom_field_2: nil,
        custom_field_3: "metadata_e",
        custom_field_4: "metadata_f")
    end

    it "returns all existing custom fields for business" do
      get(path, headers:)

      expect(response).to have_http_status(:ok)

      expect(response_hash).to eq({
        "custom_fields_1" => ["metadata_1", "metadata_a"],
        "custom_fields_2" => ["metadata_2", "metadata_b"],
        "custom_fields_3" => ["metadata_3", "metadata_c", "metadata_e"],
        "custom_fields_4" => ["metadata_4", "metadata_d", "metadata_f"],
        "custom_fields_5" => ["metadata_5", "metadata_e"],
        "custom_fields_6" => ["metadata_6", "metadata_f"],
        "custom_fields_7" => ["metadata_7", "metadata_g"],
        "custom_fields_8" => ["metadata_8", "metadata_h"]
      })
    end

    context "when client_employee not related to business" do
      let(:client_employee) { create(:client_employee, :admin_client) }

      it "returns not found" do
        get(path, headers:)

        expect(response).to have_http_status(:not_found)
        expect(response_hash["error"]).to eq("Business não encontrado")
      end
    end

    context "without client_employee authentication" do
      before do
        get path
      end

      it_behaves_like "unauthorized client_employee"
    end
  end
end
