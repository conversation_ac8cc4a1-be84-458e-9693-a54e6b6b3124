require "rails_helper"
require "sidekiq/testing"

RSpec.describe Client::V2::AuthorizedUsersController, type: :request do
  let!(:business) { create(:business, main_business:) }
  let!(:main_business) { create(:business) }
  let!(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end
  let(:response_hash) { JSON.parse(response.body) }

  describe "#sync" do
    let(:auth_user_list_params) do
      (1..4).collect do
        {
          name: FFaker::Name.name,
          email: FFaker::Internet.email,
          cpf: CPF.new(FFaker::IdentificationBR.cpf).stripped,
          custom_field_1: "metadata"
        }
      end
    end
    let(:bulk_serialized_keys) { %w[list_size valid_list_size invalid_list_size created_count updated_count] }

    context "when without required authorized_user params" do
      it "must render error" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/authorized_users/sync",
          "/client/v2/authorized_users/sync"
        ].sample
        post(path, params: {}, headers:)
        expect(response).to be_precondition_failed
      end
    end

    it "creates authorized_user of the business" do
      expect(AuthorizedUser::BulkImportWorker).to receive(:perform_async)

      path = [
        "/client/v2/businesses/#{business.cnpj}/authorized_users/sync",
        "/client/v2/authorized_users/sync"
      ].sample
      post(path, params: {authorized_users: auth_user_list_params}, headers:)

      expect(response).to have_http_status(:created)
      expect(response_hash.keys).to match_array(bulk_serialized_keys)
      expect(response_hash["list_size"]).to eq(4)
      expect(response_hash["valid_list_size"]).to eq(4)
      expect(response_hash["created_count"]).to eq(4)
      expect(response_hash["updated_count"]).to eq(0)
      expect(response_hash["invalid_list_size"]).to eq(0)
    end

    context "when cpf + business already exists" do
      let!(:authorized_user) { create(:authorized_user, business:, active: false) }
      let(:existing_params) { authorized_user.slice(:name, :email, :cpf).merge(active: true) }
      let(:auth_user_list_params) do
        (1..3).collect do
          {
            name: FFaker::Name.name,
            email: FFaker::Internet.email,
            cpf: CPF.new(FFaker::IdentificationBR.cpf).stripped,
            active: true
          }
        end
      end
      let(:params) { {authorized_users: auth_user_list_params + [existing_params]} }
      let(:bulk_serialized_keys) { %w[list_size valid_list_size invalid_list_size created_count updated_count] }

      it "returns successful" do
        Sidekiq::Testing.inline! do
          path = [
            "/client/v2/businesses/#{business.cnpj}/authorized_users/sync",
            "/client/v2/authorized_users/sync"
          ].sample
          post(path, params:, headers:)

          expect(response).to have_http_status(:created)
          expect(response_hash.keys).to match_array(bulk_serialized_keys)
          expect(response_hash["list_size"]).to eq(4)
          expect(response_hash["valid_list_size"]).to eq(4)
          expect(response_hash["created_count"]).to eq(3)
          expect(response_hash["updated_count"]).to eq(1)
          expect(response_hash["invalid_list_size"]).to eq(0)

          authorized_user.reload
          expect(authorized_user.active).to eq(true)
        end
      end
    end

    context "with invalid params" do
      let(:invalid_auth_user_params) do
        {
          name: nil,
          email: nil,
          cpf: nil
        }
      end
      let(:auth_user_list_params) do
        (1..3).collect do
          {
            name: FFaker::Name.name,
            email: FFaker::Internet.email,
            cpf: CPF.new(FFaker::IdentificationBR.cpf).stripped,
            custom_field_1: "metadata"
          }
        end
      end
      let(:params) { {authorized_users: auth_user_list_params + [invalid_auth_user_params]} }
      let(:bulk_serialized_keys) { %w[list_size valid_list_size invalid_list_size created_count updated_count] }

      it "returns successful" do
        expect(AuthorizedUser::BulkImportWorker).to receive(:perform_async)

        path = [
          "/client/v2/businesses/#{business.cnpj}/authorized_users/sync",
          "/client/v2/authorized_users/sync"
        ].sample
        post(path, params:, headers:)

        expect(response).to have_http_status(:created)
        expect(response_hash.keys).to match_array(bulk_serialized_keys)
        expect(response_hash["list_size"]).to eq(4)
        expect(response_hash["valid_list_size"]).to eq(3)
        expect(response_hash["created_count"]).to eq(3)
        expect(response_hash["updated_count"]).to eq(0)
        expect(response_hash["invalid_list_size"]).to eq(1)
      end
    end

    context "when passing authorized_user_groups slugs" do
      let!(:authorized_user_group) { create(:authorized_user_group, business:) }
      let(:params) do
        {
          authorized_users: auth_user_list_params,
          user_tags: [authorized_user_group.slug]
        }
      end

      before do
        allow(AuthorizedUser::BulkImportWorker).to receive(:perform_async)
      end

      it "must call BulkImportWorker with first authorized user group found" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/authorized_users/sync",
          "/client/v2/authorized_users/sync"
        ].sample
        post(path, params:, headers:)

        expect(AuthorizedUser::BulkImportWorker).to have_received(:perform_async).with(anything, business.id, [authorized_user_group.slug])
      end
    end

    context "when passing tags" do
      let(:auth_user_list_params) do
        (1..4).collect do
          {
            name: FFaker::Name.name,
            email: FFaker::Internet.email,
            cpf: CPF.new(FFaker::IdentificationBR.cpf).stripped,
            custom_field_1: "metadata",
            tags: ["Tag 1", "Tag 2", "Tag 3"]
          }
        end
      end

      let(:params) do
        {
          authorized_users: auth_user_list_params
        }
      end

      it "must call BulkImportWorker with first authorized user group found" do
        Sidekiq::Testing.inline! do
          path = [
            "/client/v2/businesses/#{business.cnpj}/authorized_users/sync",
            "/client/v2/authorized_users/sync"
          ].sample
          post(path, params:, headers:)

          expect(response).to have_http_status(:created)
          expect(response_hash.keys).to match_array(bulk_serialized_keys)
          expect(response_hash["list_size"]).to eq(4)
          expect(response_hash["valid_list_size"]).to eq(4)
          expect(response_hash["created_count"]).to eq(4)
          expect(response_hash["updated_count"]).to eq(0)
          expect(response_hash["invalid_list_size"]).to eq(0)
          created_authorized_users = AuthorizedUser.all
          expect(created_authorized_users.all? { _1.tags.count == 3 }).to eq(true)

          authorized_user = AuthorizedUser.first
          expect(authorized_user.tags).to match_array(["Tag 1", "Tag 2", "Tag 3"])
        end
      end
    end
  end
end
