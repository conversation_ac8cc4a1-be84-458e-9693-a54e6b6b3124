require "rails_helper"
require "sidekiq/testing"

RSpec.describe Client::V2::AuthorizedUsersController, type: :request do
  let!(:business) { create(:business, main_business:) }
  let!(:main_business) { create(:business) }
  let!(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end
  let(:serialized_keys) do
    %w[id name taxpayer_number email phone active created_at updated_at activated_at default_auth_flow
      main_business_id user_tags custom_field_1 custom_field_2 custom_field_3 custom_field_4
      custom_field_5 custom_field_6 custom_field_7 custom_field_8 telemedicine tags]
  end
  let(:response_hash) { JSON.parse(response.body) }

  describe "#show" do
    let!(:authorized_user) { create(:authorized_user, business:) }
    let!(:another_authorized_user) { create(:authorized_user) }

    context "filter by id" do
      it "returns authorized_user of the business" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/authorized_users/#{authorized_user.id}",
          "/client/v2/authorized_users/#{authorized_user.id}"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:ok)
        expect(response_hash.keys).to match_array(serialized_keys)
        expect(response_hash["id"]).to eq(authorized_user.id)
      end
    end

    context "filter by taxpayer_number" do
      it "returns authorized_user of the business" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/authorized_users/#{authorized_user.cpf}",
          "/client/v2/authorized_users/#{authorized_user.cpf}"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:ok)
        expect(response_hash.keys).to match_array(serialized_keys)
        expect(response_hash["taxpayer_number"]).to eq(CPF.new(authorized_user.cpf).formatted)
      end
    end

    it "does not return authorized_user from another business" do
      path = [
        "/client/v2/businesses/#{business.cnpj}/authorized_users/#{another_authorized_user.id}",
        "/client/v2/authorized_users/#{another_authorized_user.id}"
      ].sample
      get(path, headers:)

      expect(response).to have_http_status(:not_found)
    end
  end
end
