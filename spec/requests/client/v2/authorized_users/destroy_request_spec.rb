require "rails_helper"
require "sidekiq/testing"

RSpec.describe Client::V2::AuthorizedUsersController, type: :request do
  let!(:business) { create(:business, main_business:) }
  let!(:main_business) { create(:business) }
  let!(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  describe "#destroy" do
    let!(:authorized_user_group) { create(:authorized_user_group, business:) }
    let!(:authorized_user) do
      create(:authorized_user, business:, authorized_user_group:, active: true)
    end
    let!(:another_authorized_user) { create(:authorized_user) }
    let(:sync_mock) { double("sync_mock", call: true) }

    context "when found authorized_user" do
      context "delete by id" do
        it "deletes authorized_user of the business" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/authorized_users/#{authorized_user.id}",
            "/client/v2/authorized_users/#{authorized_user.id}"
          ].sample
          delete(path, headers:)

          expect(response).to have_http_status(:no_content)
          authorized_user.reload
          expect(authorized_user.active).to eq(false)
        end
      end

      context "delete by taxpayer_number" do
        it "deletes authorized_user of the business" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/authorized_users/#{authorized_user.cpf}",
            "/client/v2/authorized_users/#{authorized_user.cpf}"
          ].sample
          delete(path, headers:)

          expect(response).to have_http_status(:no_content)
          authorized_user.reload
          expect(authorized_user.active).to eq(false)
        end
      end
    end

    context "when not found authorized_user" do
      it "raise error and does not delete" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/authorized_users/anyfoo",
          "/client/v2/authorized_users/anyfoo"
        ].sample
        delete(path, headers:)

        expect(response).to have_http_status(:not_found)
      end
    end

    it "does not delete authorized_user from another business" do
      path = [
        "/client/v2/businesses/#{business.cnpj}/authorized_users/#{another_authorized_user.cpf}",
        "/client/v2/authorized_users/#{another_authorized_user.cpf}"
      ].sample
      delete(path, headers:)

      authorized_user.reload
      expect(authorized_user.active).to eq(true)
    end
  end
end
