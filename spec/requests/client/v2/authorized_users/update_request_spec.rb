require "rails_helper"
require "sidekiq/testing"

RSpec.describe Client::V2::AuthorizedUsersController, type: :request do
  let!(:business) { create(:business, main_business:) }
  let!(:main_business) { create(:business) }
  let!(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end
  let(:serialized_keys) do
    %w[id name taxpayer_number email phone active created_at updated_at activated_at default_auth_flow
      main_business_id user_tags custom_field_1 custom_field_2 custom_field_3 custom_field_4
      custom_field_5 custom_field_6 custom_field_7 custom_field_8 telemedicine tags]
  end
  let(:response_hash) { JSON.parse(response.body) }

  describe "#update" do
    let!(:new_business) { create(:business, main_business: new_main_business) }
    let!(:new_main_business) { create(:business) }
    let!(:authorized_user) { create(:authorized_user, business:, tags: "old-tag") }
    let!(:another_authorized_user) { create(:authorized_user) }
    let(:new_attributes) { {name: "New name", cpf: "***********", active: true, business_id: new_business.id, tags: ["New Tag 1", "New Tag 2"]} }
    let(:sync_mock) { double("sync_mock", call: true) }

    context "when found authorized_user" do
      context "when activating a banned cpf" do
        before do
          BannedCpf.create!(cpf: authorized_user.cpf)
        end

        it "must render error" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/authorized_users/#{authorized_user.id}",
            "/client/v2/authorized_users/#{authorized_user.id}"
          ].sample
          patch(path, params: new_attributes, headers:)

          expect(response_hash["error"]).to include(I18n.t("user.sign_up.banned_cpf"))
        end
      end

      context "update by id" do
        it "updates authorized_user of the business" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/authorized_users/#{authorized_user.id}",
            "/client/v2/authorized_users/#{authorized_user.id}"
          ].sample
          expect(authorized_user.tags).to include("old-tag")
          patch(path, params: new_attributes, headers:)

          expect(response).to have_http_status(:ok)
          expect(response_hash.keys).to match_array(serialized_keys)
          expect(response_hash["id"]).to eq(authorized_user.id)
          expect(response_hash["name"]).to eq(Utils::NameNormalizer.call(new_attributes[:name]))
          expect(response_hash["active"]).to eq(new_attributes[:active])
          expect(response_hash["taxpayer_number"]).to eq(CPF.new(authorized_user.cpf).formatted)
          expect(response_hash["main_business_id"]).to eq(new_main_business.id)

          expect(response_hash["tags"]).not_to include("Old Tag")
          expect(response_hash["tags"]).to eq(["New Tag 1", "New Tag 2"])
        end
      end

      context "update by taxpayer_number" do
        it "updates authorized_user of the business" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/authorized_users/#{authorized_user.cpf}",
            "/client/v2/authorized_users/#{authorized_user.cpf}"
          ].sample
          patch(path, params: new_attributes, headers:)

          expect(response).to have_http_status(:ok)
          expect(response_hash.keys).to match_array(serialized_keys)
          expect(response_hash["id"]).to eq(authorized_user.id)
          expect(response_hash["name"]).to eq(Utils::NameNormalizer.call(new_attributes[:name]))
          expect(response_hash["taxpayer_number"]).to eq(CPF.new(authorized_user.cpf).formatted)
        end
      end
    end

    context "when not found authorized_user" do
      it "raise error and does not update" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/authorized_users/anyfoo",
          "/client/v2/authorized_users/anyfoo"
        ].sample
        patch(path, params: new_attributes, headers:)

        expect(response).to have_http_status(:not_found)
      end
    end

    it "does not update authorized_user from another business" do
      path = [
        "/client/v2/businesses/#{business.cnpj}/authorized_users/#{another_authorized_user.id}",
        "/client/v2/authorized_users/#{another_authorized_user.id}"
      ].sample
      patch(path, params: new_attributes, headers:)

      expect(response).to have_http_status(:not_found)
    end

    context "when passing user_tags" do
      let!(:authorized_user_group) { create(:authorized_user_group, business:) }
      let(:new_attributes) { {name: "New name", cpf: "***********", active: false, user_tags: [authorized_user_group.slug]} }

      it "does update authorized_user_group based on user_tags" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/authorized_users/#{authorized_user.id}",
          "/client/v2/authorized_users/#{authorized_user.id}"
        ].sample
        patch(path, params: new_attributes, headers:)

        expect(response).to be_ok
        authorized_user.reload
        expect(authorized_user.authorized_user_group).to eq(authorized_user_group)
      end
    end

    context "when sync_user_updates is enabled" do
      let!(:authorized_user) { create(:authorized_user, business: business, name: "Old Name", email: "<EMAIL>", phone: "1234567890") }
      let!(:user) { create(:user, business: business, authorized_user: authorized_user, name: "Old Name", email: "<EMAIL>", cellphone: "1234567890") }

      let(:update_params) { {name: "New Name", email: "<EMAIL>", phone: "9876543210"} }

      before do
        business.update!(sync_user_updates: true)
      end

      it "updates both authorized_user and user" do
        path = "/client/v2/authorized_users/#{authorized_user.id}"

        expect {
          patch(path, headers: headers, params: update_params)
          authorized_user.reload
          user.reload
        }.to change(authorized_user, :name).to("New Name")
          .and change(authorized_user, :email).to("<EMAIL>")
          .and change(authorized_user, :phone).to("9876543210")
          .and change(user, :name).to("New Name")
          .and change(user, :email).to("<EMAIL>")
          .and change(user, :cellphone).to("9876543210")

        expect(response).to have_http_status(:ok)
      end
    end

    context "when sync_user_updates is disabled" do
      let!(:authorized_user) { create(:authorized_user, business: business, name: "Old Name", email: "<EMAIL>", phone: "1234567890") }
      let!(:user) { create(:user, business: business, authorized_user: authorized_user, name: "Old Name", email: "<EMAIL>", cellphone: "1234567890") }

      let(:update_params) { {name: "New Name", email: "<EMAIL>", phone: "9876543210"} }

      before do
        business.update!(sync_user_updates: false)
      end

      it "updates only authorized_user but not user" do
        path = "/client/v2/authorized_users/#{authorized_user.id}"

        expect {
          patch(path, headers: headers, params: update_params)
          authorized_user.reload
          user.reload
        }.to change(authorized_user, :name).to("New Name")
          .and change(authorized_user, :email).to("<EMAIL>")
          .and change(authorized_user, :phone).to("9876543210")
          .and not_change(user, :name)
          .and not_change(user, :email)
          .and not_change(user, :cellphone)

        expect(response).to have_http_status(:ok)
      end
    end
  end
end
