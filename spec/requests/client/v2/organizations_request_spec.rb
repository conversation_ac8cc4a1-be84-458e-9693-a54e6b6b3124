# frozen_string_literal: true

require "rails_helper"

RSpec.describe Client::V2::OrganizationsController, type: :request do
  let!(:category_one) { create(:category) }
  let!(:subcategory) { create(:category) }

  let!(:organization_one) { create(:organization, giftcard_redeemable: true, cnpj: FFaker::IdentificationBR.cnpj) }
  let!(:organization_two) { create(:organization, promotion_redeemable: true) }
  let!(:organization_three) { create(:organization) }
  let!(:organization_four) { create(:organization, categories: [category_one]) }
  let!(:organization_five) { create(:organization) }
  let!(:organization_six) { create(:organization, categories: [subcategory]) }
  let!(:organization_seven) { create(:organization) }
  let!(:parent_business) do
    create(:business, :publicar, :with_cashback)
  end
  let!(:business) do
    create(:business, :oab, :with_cashback, cnpj: "71288357000165", main_business: parent_business)
  end
  let!(:blocklist) { create(:organization_blocklist, business:, organization: organization_three) }
  let!(:blocklist_parent) { create(:organization_blocklist, business: parent_business, organization: organization_seven) }
  let!(:blocklist_in_category) { create(:category_blocklist, business:, category: category_one) }
  let!(:blocklist_in_subcategory) { create(:category_blocklist, business:, category: subcategory) }
  let!(:organization_without_approval) do
    create(:organization_blocklist, business:, organization: organization_two)
  end

  let!(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end
  let(:serializer_keys) do
    %w[id name cover_picture blocklisted logo_image banner_image description short_description
      instagram facebook contact_telephone categories organization_profile_id cnpj]
  end
  let(:response_hash) { JSON.parse(response.body) }

  describe "#index" do
    it "returns all organizations for business, page 1" do
      profile_org_two = create(:organization_profile, organization: organization_two, business:)
      profile_org_seven = create(:organization_profile, organization: organization_seven, business:)

      path = [
        "/client/v2/businesses/#{business.cnpj}/organizations?page=1",
        "/client/v2/organizations?page=1"
      ].sample
      get(path, headers:)

      expect(response).to have_http_status(:ok)
      expect(response_hash.pluck("id", "organization_profile_id")).to match_array([
        [organization_one.id, nil],
        [organization_two.id, profile_org_two.id],
        [organization_three.id, nil],
        [organization_four.id, nil],
        [organization_five.id, nil],
        [organization_six.id, nil],
        [organization_seven.id, profile_org_seven.id]
      ])
      expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
    end

    it "returns all organizations for business, page 2" do
      path = [
        "/client/v2/businesses/#{business.cnpj}/organizations?page=2",
        "/client/v2/organizations?page=2"
      ].sample
      get(path, headers:)

      expect(response).to have_http_status(:ok)
      expect(response_hash).to be_empty
    end

    context "when filtering by name" do
      let(:params) { {name: organization_one.name, page: 1} }

      it "must return only orgnaizations with that name" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/organizations",
          "/client/v2/organizations"
        ].sample
        get(path, headers:, params:)

        expect(response).to have_http_status(:ok)
        expect(response_hash.pluck("id")).to match_array([organization_one].pluck(:id))
        expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
      end
    end

    context "when filtering by organization cnpj" do
      let(:params) { {cnpj: organization_one.cnpj, page: 1} }

      it "returns organizations with that cnpj" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/organizations",
          "/client/v2/organizations"
        ].sample
        get(path, headers:, params:)

        expect(response).to have_http_status(:ok)
        expect(response_hash.pluck("id")).to match_array([organization_one].pluck(:id))
        expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
      end
    end

    it "returns empty when organization is inactive" do
      organization_one.update_columns(active: false)

      params = {cnpj: organization_one.cnpj, page: 1}
      path = [
        "/client/v2/businesses/#{business.cnpj}/organizations",
        "/client/v2/organizations"
      ].sample
      get(path, headers:, params:)

      expect(response).to have_http_status(:ok)
      expect(response_hash).to be_empty
    end

    context "when filtering by branch cnpj" do
      let!(:branch) { create(:branch, organization: organization_one, cnpj: FFaker::IdentificationBR.cnpj) }
      let(:params) { {cnpj: branch.cnpj, page: 1} }

      it "returns organizations with branch with that cnpj" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/organizations",
          "/client/v2/organizations"
        ].sample
        get(path, headers:, params:)

        expect(response).to have_http_status(:ok)
        expect(response_hash.pluck("id")).to match_array([organization_one].pluck(:id))
        expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
      end
    end

    context "when filtering by blocklisted" do
      let!(:allowed_organization) { create(:organization) }
      let!(:blocklisted_organization) { create(:organization) }
      let!(:blocklist) { create(:organization_blocklist, business:, organization: blocklisted_organization) }

      context "when blocklisted filter is false" do
        let(:params) { {blocklisted: false} }

        it "returns only non blocklisted organizations" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/organizations",
            "/client/v2/organizations"
          ].sample
          get(path, headers:, params:)

          expect(response).to be_ok
          expect(response_hash.pluck("id")).to include(allowed_organization.id)
        end

        context "when only category is blocklisted" do
          it "must show organization anyway because it is not direct blocked" do
            path = [
              "/client/v2/businesses/#{business.cnpj}/organizations",
              "/client/v2/organizations"
            ].sample
            get(path, headers:)

            expect(response).to be_ok
            expect(response_hash.pluck("id")).to include(organization_four.id)
          end
        end
      end

      context "when blocklisted filter is true" do
        let(:params) { {blocklisted: true} }

        it "returns only blocklisted organizations" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/organizations",
            "/client/v2/organizations"
          ].sample
          get(path, headers:, params:)

          expect(response).to be_ok
          expect(response_hash.pluck("id")).to include(blocklisted_organization.id)
        end

        context "when only category is blocklisted" do
          it "must not show organization cause it is not direct blocked" do
            path = [
              "/client/v2/businesses/#{business.cnpj}/organizations",
              "/client/v2/organizations"
            ].sample
            get(path, headers:, params:)

            expect(response).to be_ok
            expect(response_hash.pluck("id")).not_to include(organization_four.id)
          end
        end
      end

      context "when blocklisted filter is not passed" do
        it "returns all organizations the business can list" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/organizations",
            "/client/v2/organizations"
          ].sample
          get(path, headers:)

          expect(response).to be_ok
          expect(response_hash.pluck("id", "blocklisted")).to include(
            [organization_one.id, false],
            [allowed_organization.id, false],
            [blocklisted_organization.id, true]
          )
        end
      end
    end

    context "when filtering by product" do
      context "when product is promotion" do
        it "returns all organizations with promotion" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/organizations?page=1",
            "/client/v2/organizations?page=1"
          ].sample
          get(path, params: {product_type: :giftcard}, headers:)

          expect(response).to have_http_status(:ok)
          expect(response_hash.pluck("id")).to match_array([organization_one].pluck(:id))
          expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
        end
      end
      context "when product is giftcard" do
        it "returns all organizations with giftcard" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/organizations?page=1",
            "/client/v2/organizations?page=1"
          ].sample
          get(path, params: {product_type: :promotion}, headers:)

          expect(response).to have_http_status(:ok)
          expect(response_hash.pluck("id")).to match_array([organization_two].pluck(:id))
          expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
        end
      end
    end
  end
end
