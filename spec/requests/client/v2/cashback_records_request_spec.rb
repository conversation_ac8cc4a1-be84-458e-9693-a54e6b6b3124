# frozen_string_literal: true

require "rails_helper"

RSpec.describe Client::V2::CashbackRecordsController, type: :request do
  describe "#index" do
    let!(:business) { create(:business, :with_cashback, cnpj: "03814381000130") }
    let!(:business_two) { create(:business, :with_cashback, cnpj: "32839587000113") }
    let!(:client_employee) { create(:client_employee, businesses: [business]) }
    let(:headers) do
      {
        "X-ClientEmployee-Email": client_employee.email,
        "X-ClientEmployee-Token": client_employee.authentication_token,
        "Tenant-id": business.cnpj
      }
    end
    let!(:user_one) { create(:user, business:, cpf: "***********", name: "<PERSON><PERSON><PERSON><PERSON>") }
    let!(:user_two) { create(:user, business:, cpf: "***********", name: "<PERSON>lan<PERSON>") }
    let!(:user_three) { create(:user, business: business_two, cpf: "***********", name: "<PERSON><PERSON>") }
    let!(:order_one) { create(:order, :online, user: user_one) }
    let!(:cashback_one) do
      create(:cashback_record,
        order: order_one,
        user: user_one,
        transaction_status: ["approved", "available_lecupon", "in_transfer_lecupon"].sample,
        order_amount: 107.5,
        total_commission_amount: 10.75,
        business_spread_amount: 1.08,
        cashback_amount: 9.67,
        created_at: "2022-04-10 09:32:08")
    end
    let!(:order_two) { create(:order, :online, user: user_one) }
    let!(:cashback_two) do
      create(:cashback_record,
        order: order_two,
        user: user_one,
        transaction_status: Enums::CashbackRecordStatus::PENDING,
        order_amount: 127.5,
        total_commission_amount: 12.75,
        business_spread_amount: 1.28,
        cashback_amount: 11.47,
        created_at: "2022-05-01 10:44:13")
    end
    let!(:order_three) { create(:order, :online, user: user_two) }
    let!(:cashback_three) do
      create(:cashback_record,
        order: order_three,
        user: user_two,
        transaction_status: Enums::CashbackRecordStatus::AVAILABLE,
        order_amount: 100,
        total_commission_amount: 10,
        business_spread_amount: 1,
        cashback_amount: 9,
        created_at: "2022-04-02 07:51:40")
    end
    let!(:order_four) { create(:order, :online, user: user_three) }
    let!(:cashback_four) do
      create(:cashback_record,
        order: order_four,
        user: user_three,
        transaction_status: Enums::CashbackRecordStatus::AVAILABLE,
        order_amount: 500,
        total_commission_amount: 50,
        business_spread_amount: 5,
        cashback_amount: 45,
        created_at: "2022-04-12 19:05:01")
    end
    let(:response_hash) { JSON.parse(response.body) }

    context "without filter" do
      it "returns all cashback records for current business" do
        path = [
          "/client/v2/businesses/03814381000130/cashback_records",
          "/client/v2/cashback_records"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:ok)
        expect(response_hash).to eq([
          {
            "id" => cashback_two.id,
            "order_id" => cashback_two.order_id,
            "user_id" => cashback_two.user_id,
            "user_name" => "Ciclano",
            "user_taxpayer_number" => "***********",
            "order_amount" => 127.5,
            "cashback_amount" => 11.47,
            "transaction_status" => "Pendente",
            "raw_transaction_status" => "pending",
            "organization_name" => cashback_two.order.organization_name,
            "cupon_description" => cashback_two.order.description,
            "order_number" => cashback_two.order.number,
            "created_at" => "2022-05-01T10:44:13.000-03:00"
          },
          {
            "id" => cashback_one.id,
            "order_id" => cashback_one.order_id,
            "user_id" => cashback_one.user_id,
            "user_name" => "Ciclano",
            "user_taxpayer_number" => "***********",
            "order_amount" => 107.5,
            "cashback_amount" => 9.67,
            "transaction_status" => "Aprovado",
            "raw_transaction_status" => "approved",
            "organization_name" => cashback_one.order.organization_name,
            "cupon_description" => cashback_one.order.description,
            "order_number" => cashback_one.order.number,
            "created_at" => "2022-04-10T09:32:08.000-03:00"
          },
          {
            "id" => cashback_three.id,
            "order_id" => cashback_three.order_id,
            "user_id" => cashback_three.user_id,
            "user_name" => "Fulano",
            "user_taxpayer_number" => "***********",
            "order_amount" => 100,
            "cashback_amount" => 9.0,
            "transaction_status" => "Disponível",
            "raw_transaction_status" => "available",
            "organization_name" => cashback_three.order.organization_name,
            "cupon_description" => cashback_three.order.description,
            "order_number" => cashback_three.order.number,
            "created_at" => "2022-04-02T07:51:40.000-03:00"
          }
        ])
      end

      it "returns all cashback records for current business except those with business cashback disabled" do
        cashback_two.update_columns(computable: false)

        path = [
          "/client/v2/businesses/03814381000130/cashback_records",
          "/client/v2/cashback_records"
        ].sample
        get(path, headers:)

        expect(response).to be_ok
        expect(response_hash.pluck("id")).not_to include cashback_two.id
      end
    end

    context "when filtering by user taxpayer number" do
      let(:params) { {user_taxpayer_number: "***********"} }

      it "returns cashback records for current business filtered by user_taxpayer_number" do
        path = [
          "/client/v2/businesses/03814381000130/cashback_records",
          "/client/v2/cashback_records"
        ].sample
        get(path, headers:, params:)

        expect(response).to have_http_status(:ok)
        expect(response_hash).to eq([
          {
            "id" => cashback_two.id,
            "order_id" => cashback_two.order_id,
            "user_id" => cashback_two.user_id,
            "user_name" => "Ciclano",
            "user_taxpayer_number" => "***********",
            "order_amount" => 127.5,
            "cashback_amount" => 11.47,
            "transaction_status" => "Pendente",
            "raw_transaction_status" => "pending",
            "organization_name" => cashback_two.order.organization_name,
            "cupon_description" => cashback_two.order.description,
            "order_number" => cashback_two.order.number,
            "created_at" => "2022-05-01T10:44:13.000-03:00"
          },
          {
            "id" => cashback_one.id,
            "order_id" => cashback_one.order_id,
            "user_id" => cashback_one.user_id,
            "user_name" => "Ciclano",
            "user_taxpayer_number" => "***********",
            "order_amount" => 107.5,
            "cashback_amount" => 9.67,
            "transaction_status" => "Aprovado",
            "raw_transaction_status" => "approved",
            "organization_name" => cashback_one.order.organization_name,
            "cupon_description" => cashback_one.order.description,
            "order_number" => cashback_one.order.number,
            "created_at" => "2022-04-10T09:32:08.000-03:00"
          }
        ])
      end
    end

    context "when filtering by date range" do
      let(:params) { {start_date: "2022-04-01", end_date: "2022-04-10"} }

      it "returns cashback records for current business filtered by start_date and end_date" do
        path = [
          "/client/v2/businesses/03814381000130/cashback_records",
          "/client/v2/cashback_records"
        ].sample
        get(path, headers:, params:)

        expect(response).to have_http_status(:ok)
        expect(response_hash).to eq([
          {
            "id" => cashback_one.id,
            "order_id" => cashback_one.order_id,
            "user_id" => cashback_one.user_id,
            "user_name" => "Ciclano",
            "user_taxpayer_number" => "***********",
            "order_amount" => 107.5,
            "cashback_amount" => 9.67,
            "transaction_status" => "Aprovado",
            "raw_transaction_status" => "approved",
            "organization_name" => cashback_one.order.organization_name,
            "cupon_description" => cashback_one.order.description,
            "order_number" => cashback_one.order.number,
            "created_at" => "2022-04-10T09:32:08.000-03:00"
          },
          {
            "id" => cashback_three.id,
            "order_id" => cashback_three.order_id,
            "user_id" => cashback_three.user_id,
            "user_name" => "Fulano",
            "user_taxpayer_number" => "***********",
            "order_amount" => 100,
            "cashback_amount" => 9.0,
            "transaction_status" => "Disponível",
            "raw_transaction_status" => "available",
            "organization_name" => cashback_three.order.organization_name,
            "cupon_description" => cashback_three.order.description,
            "order_number" => cashback_three.order.number,
            "created_at" => "2022-04-02T07:51:40.000-03:00"
          }
        ])
      end
    end

    context "when filtering by approved" do
      let(:params) { {status: Enums::CashbackRecordStatus::APPROVED} }

      it "returns cashback records for current business that are approved from the end user point of view" do
        path = [
          "/client/v2/businesses/03814381000130/cashback_records",
          "/client/v2/cashback_records"
        ].sample
        get(path, headers:, params:)

        expect(response).to have_http_status(:ok)
        expect(response_hash).to eq([
          {
            "id" => cashback_one.id,
            "order_id" => cashback_one.order_id,
            "user_id" => cashback_one.user_id,
            "user_name" => "Ciclano",
            "user_taxpayer_number" => "***********",
            "order_amount" => 107.5,
            "cashback_amount" => 9.67,
            "transaction_status" => "Aprovado",
            "raw_transaction_status" => "approved",
            "organization_name" => cashback_one.order.organization_name,
            "cupon_description" => cashback_one.order.description,
            "order_number" => cashback_one.order.number,
            "created_at" => "2022-04-10T09:32:08.000-03:00"
          }
        ])
      end
    end

    context "when filtering by in_transfer" do
      let!(:order_five) do
        create(:order, :online, business:, user: user_one, user_cpf: user_one.cpf, user_name: user_one.name)
      end
      let!(:cashback_five) do
        create(:cashback_record,
          order: order_five,
          user: user_one,
          transaction_status: ["in_transfer", "locked"].sample,
          order_amount: 107.5,
          total_commission_amount: 10.75,
          business_spread_amount: 1.08,
          cashback_amount: 9.67,
          created_at: "2022-04-09 09:32:08")
      end
      let!(:order_six) do
        create(:order, :online, business:, user: user_one, user_cpf: user_one.cpf, user_name: user_one.name)
      end
      let!(:cashback_six) do
        create(:cashback_record,
          order: order_six,
          user: user_one,
          transaction_status: ["in_transfer", "locked"].sample,
          order_amount: 107.5,
          total_commission_amount: 10.75,
          business_spread_amount: 1.08,
          cashback_amount: 9.67,
          created_at: "2022-04-08 09:32:08")
      end
      let(:params) { {status: Enums::CashbackRecordStatus::IN_TRANSFER} }

      it "returns cashback records for current business that are in_transfer from the end user point of view" do
        path = [
          "/client/v2/businesses/03814381000130/cashback_records",
          "/client/v2/cashback_records"
        ].sample
        get(path, headers:, params:)

        expect(response).to have_http_status(:ok)
        expect(response_hash).to eq([
          {
            "id" => cashback_five.id,
            "order_id" => cashback_five.order_id,
            "user_id" => cashback_five.user_id,
            "user_name" => "Ciclano",
            "user_taxpayer_number" => "***********",
            "order_amount" => 107.5,
            "cashback_amount" => 9.67,
            "transaction_status" => "Em Transferência",
            "raw_transaction_status" => "in_transfer",
            "organization_name" => cashback_five.order.organization_name,
            "cupon_description" => cashback_five.order.description,
            "order_number" => cashback_five.order.number,
            "created_at" => "2022-04-09T09:32:08.000-03:00"
          },
          {
            "id" => cashback_six.id,
            "order_id" => cashback_six.order_id,
            "user_id" => cashback_six.user_id,
            "user_name" => "Ciclano",
            "user_taxpayer_number" => "***********",
            "order_amount" => 107.5,
            "cashback_amount" => 9.67,
            "transaction_status" => "Em Transferência",
            "raw_transaction_status" => "in_transfer",
            "organization_name" => cashback_six.order.organization_name,
            "cupon_description" => cashback_six.order.description,
            "order_number" => cashback_six.order.number,
            "created_at" => "2022-04-08T09:32:08.000-03:00"
          }
        ])
      end
    end

    context "when filtering by status not visible to business" do
      it "returns empty" do
        params = {
          status: (Enums::CashbackRecordStatus.all - Enums::CashbackRecordStatus.statuses_visible_to_business).sample
        }

        path = [
          "/client/v2/businesses/03814381000130/cashback_records",
          "/client/v2/cashback_records"
        ].sample
        get(path, headers:, params:)

        expect(response).to have_http_status(:ok)
        expect(response_hash).to be_empty
      end
    end

    context "when business has cashback disabled" do
      before do
        business.update!(cashback: false)
      end

      it "returns empty" do
        path = [
          "/client/v2/businesses/03814381000130/cashback_records",
          "/client/v2/cashback_records"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:forbidden)
        expect(response_hash["error"]).to eq(I18n.t("inactive_cashback"))
      end
    end
  end
end
