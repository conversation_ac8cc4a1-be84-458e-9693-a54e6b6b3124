require "rails_helper"

RSpec.describe Client::V2::WebApplications::DnsValidationController, type: :request do
  let!(:business) { create(:business, web_application:) }
  let(:web_application) { create(:web_application, cloudfront_distribution_id: "any_id", acm_certificate_arn: "any_arn") }
  let!(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  let(:serialized_keys) { %w[cloudfront_distribution_url certified_cnames valid_certificate certificate_attached_to_distribution setup] }
  let(:response_hash) { JSON.parse(response.body) }

  let(:aws_acm_client) do
    double("aws_acm_client",
      show: stubbed_certificate_response)
  end

  let(:aws_cloudfront_client) do
    double("aws_cloudfront_client", update_domain: true)
  end

  before do
    allow(AwsClient::Cloudfront::Distribution).to receive(:new).and_return(aws_cloudfront_client)
    allow(AwsClient::Acm::Certificate).to receive(:new).and_return(aws_acm_client)
  end

  describe "#update" do
    context "when certificate is validated on ACM" do
      let(:stubbed_certificate_response) do
        double("response",
          certificate: double("existing_certificate",
            domain_name: "newwebdomain.com",
            certificate_arn: "old_certificate_arn"),
          domain_validation_options: [
            {domain_name: "example.com",
             validation_domain: "example.com",
             validation_status: "SUCCESS",
             resource_record: {name: "_98fad938f20360cff077fb9b27aa1433.example.com.", type: "CNAME", value: "_a3ac3e557ddaeee6e42eb403c308a8fe.djqtsrsxkq.acm-validations.aws."},
             validation_method: "DNS"}
          ])
      end

      it "must update web_application dns validation related fields to be valid and attach certificate" do
        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/web_applications/dns_validation",
            "/client/v2/web_applications/dns_validation"
          ].sample
          patch(path, headers:)

          expect(response).to be_ok
          expect(response_hash.keys).to match_array(serialized_keys)
          expect(aws_acm_client).to have_received(:show)
          expect(aws_cloudfront_client).to have_received(:update_domain)
          web_application.reload
        end.to change(web_application, :valid_certificate).to(true)
          .and change(web_application, :certificate_attached_to_distribution).to(true)
          .and change(web_application, :certified_cnames)
      end

      context "trying to update when certificate change is in progress" do
        before do
          error_one = Aws::CloudFront::Errors::InvalidViewerCertificate.new("update_distribution", "The specified SSL certificate doesn't exist, isn't in us-east-1 region, isn't valid, or doesn't include a valid certificate chain.")
          error_two = Aws::CloudFront::Errors::IllegalUpdate.new("update_distribution", "Only one viewer certificate change may be in progress at a time.")
          allow(aws_cloudfront_client).to receive(:update_domain).and_raise([error_one, error_two].sample)
        end

        it "return error" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/web_applications/dns_validation",
            "/client/v2/web_applications/dns_validation"
          ].sample
          patch(path, headers:)

          expect(response).to have_http_status(:unprocessable_entity)
          expect(response_hash["error"]).to eq("Foi realizado uma atualização de certificado recentemente! Tente novamente mais tarde.")
        end
      end
    end

    context "when certificate is not validated on ACM" do
      let(:stubbed_certificate_response) do
        double("response",
          certificate: double("existing_certificate",
            domain_name: "newwebdomain.com",
            certificate_arn: "old_certificate_arn"),
          domain_validation_options: [
            {domain_name: "example.com",
             validation_domain: "example.com",
             validation_status: "PENDING",
             resource_record: {name: "_98fad938f20360cff077fb9b27aa1433.example.com.", type: "CNAME", value: "_a3ac3e557ddaeee6e42eb403c308a8fe.djqtsrsxkq.acm-validations.aws."},
             validation_method: "DNS"}
          ])
      end

      it "must set web_application dns validation related fields to not be valid" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/web_applications/dns_validation",
          "/client/v2/web_applications/dns_validation"
        ].sample
        patch(path, headers:)

        expect(response).to be_ok
        expect(response_hash.keys).to match_array(serialized_keys)
        expect(aws_acm_client).to have_received(:show)
        expect(aws_cloudfront_client).not_to have_received(:update_domain)
        web_application.reload
        expect(web_application.valid_certificate).to eq(false)
        expect(web_application.certificate_attached_to_distribution).to eq(false)
      end
    end
  end
end
