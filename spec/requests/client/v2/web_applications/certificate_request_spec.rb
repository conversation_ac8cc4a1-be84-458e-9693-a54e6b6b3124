require "rails_helper"

RSpec.describe Client::V2::WebApplications::CertificateController, type: :request do
  let!(:business) { create(:business, web_application:) }
  let!(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  let(:web_application) do
    create(:web_application,
      cloudfront_distribution_id: "any_id",
      acm_certificate_arn: "actual_arn",
      valid_certificate: true,
      certificate_attached_to_distribution: true)
  end

  let(:serialized_keys) { %w[cloudfront_distribution_url certified_cnames valid_certificate certificate_attached_to_distribution setup] }
  let(:response_hash) { JSON.parse(response.body) }

  let(:aws_acm_client) do
    double("aws_acm_client",
      create: "new_certificate_arn",
      show: stubbed_certificate_response,
      delete: true)
  end
  let(:stubbed_certificate_response) do
    double("response",
      certificate: double("existing_certificate",
        domain_name: "newwebdomain.com",
        certificate_arn: "ref_certificate_arn"),
      domain_validation_options: [
        {domain_name: "example.com",
         validation_domain: "example.com",
         validation_status: "PENDING",
         resource_record: {name: "_98fad938f20360cff077fb9b27aa1433.example.com.", type: "CNAME", value: "_a3ac3e557ddaeee6e42eb403c308a8fe.djqtsrsxkq.acm-validations.aws."},
         validation_method: "DNS"}
      ])
  end

  let(:aws_cloudfront_client) do
    double("aws_cloudfront_client",
      create: stubbed_distribution_response,
      update_domain: true,
      clear_domain: true)
  end
  let(:stubbed_distribution_response) do
    double("distribution",
      id: "ABCDEF123",
      domain_name: "deoqbnmvc4kwn.cloudfront.net",
      distribution_config: {
        enabled: true,
        aliases: {quantity: 1, items: ["newdomain.com.br"]},
        caller_reference: "original_caller_reference",
        all_other_params: {foo: "bar"}
      })
  end

  before do
    allow(AwsClient::Cloudfront::Distribution).to receive(:new).and_return(aws_cloudfront_client)
    allow(AwsClient::Acm::Certificate).to receive(:new).and_return(aws_acm_client)
  end

  describe "#create" do
    it "must regenerate web_application certificate" do
      expect do
        path = [
          "/client/v2/businesses/#{business.cnpj}/web_applications/certificate",
          "/client/v2/web_applications/certificate"
        ].sample
        post path, headers: headers

        expect(response).to be_ok
        expect(response_hash.keys).to match_array(serialized_keys)

        expect(aws_acm_client).to have_received(:delete).with("actual_arn")
        expect(aws_cloudfront_client).to have_received(:clear_domain)
        expect(aws_acm_client).to have_received(:create)
        web_application.reload
      end.to change(web_application, :acm_certificate_arn).to("new_certificate_arn")
        .and change(web_application, :valid_certificate).to(false)
        .and change(web_application, :certificate_attached_to_distribution).to(false)
    end

    context "trying to update when certificate change is in progress" do
      before do
        error = Aws::CloudFront::Errors::IllegalUpdate.new("update_distribution", "Only one viewer certificate change may be in progress at a time.")
        allow(aws_cloudfront_client).to receive(:clear_domain).and_raise(error)
      end

      it "return error" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/web_applications/certificate",
          "/client/v2/web_applications/certificate"
        ].sample
        post path, headers: headers

        expect(response).to have_http_status(:unprocessable_entity)
        expect(response_hash["error"]).to eq("Foi realizado uma atualização de certificado recentemente! Tente novamente mais tarde.")
      end
    end
  end
end
