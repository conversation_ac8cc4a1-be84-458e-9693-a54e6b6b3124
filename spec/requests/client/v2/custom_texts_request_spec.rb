# frozen_string_literal: true

require "rails_helper"

RSpec.describe Client::V2::CustomTextsController, type: :request do
  let!(:business) do
    create(:business, :oab, :with_cashback)
  end
  let(:parsed_response) { JSON.parse(response.body) }
  let!(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end
  let(:serialized_keys) { %w[message_type label value] }
  let(:cashback_discount_types) { %w[cashback_discount_pending cashback_discount_in_transfer cashback_discount_requestable cashback_discount_transfer_to_user] }
  let(:auth_integration_types) { %w[auth_integration_inactive_cpf auth_integration_cpf_not_found auth_integration_error] }
  let(:payout_types) { %w[cashback_batch_pending cashback_batch_in_transfer cashback_batch_requestable cashback_batch_transfer_to_user] }
  let(:wallet_point_types) { %w[wallet_point_redemption wallet_point_expiration wallet_point_availability] }
  let(:wallet_cashback_types) { %w[wallet_cashback_redemption wallet_cashback_availability] }

  let!(:custom_text) do
    create(:custom_text,
      business:,
      message_type: Enums::CustomTextType::CONTACT_MANAGER,
      message: "Some message")
  end

  let!(:business_2) { create(:business, :with_cashback, cashback_wallet_destination: Wallet::Kind::MEMBERSHIP) }

  let!(:custom_text_2) do
    create(:custom_text,
      business: business_2,
      message_type: Enums::CustomTextType::PROMOTION_CUSTOM_TEXT,
      message: "Any other message")
  end

  describe "#index" do
    context "when businness cashback transfer receiver is user" do
      it "returns all messages visible to the business" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/custom_texts",
          "/client/v2/custom_texts"
        ].sample
        get(path, as: :json, headers:)

        expect(response).to have_http_status(:ok)
        expect(parsed_response.pluck("message_type")).to match_array(
          Enums::CustomTextType.all - cashback_discount_types - auth_integration_types - wallet_point_types
        )
        expect(parsed_response.find { _1["message_type"] == "contact_manager" }["value"]).to eq(custom_text.message)
        expect(parsed_response.find { _1["message_type"] == "promotion_custom_text" }["value"]).not_to eq(custom_text_2.message)
        expect(parsed_response.map(&:keys)).to all(match_array(serialized_keys))
      end
    end

    context "when businness cashback transfer receiver is business" do
      let!(:client_employee) { create(:client_employee, businesses: [business_2]) }
      let(:headers) do
        {
          "X-ClientEmployee-Email": client_employee.email,
          "X-ClientEmployee-Token": client_employee.authentication_token,
          "Tenant-id": business_2.cnpj
        }
      end

      it "returns all messages visible to the business" do
        path = [
          "/client/v2/businesses/#{business_2.cnpj}/custom_texts",
          "/client/v2/custom_texts"
        ].sample
        get(path, as: :json, headers:)

        expect(response).to have_http_status(:ok)
        expect(parsed_response.pluck("message_type")).to match_array(
          Enums::CustomTextType.all - payout_types - auth_integration_types - wallet_point_types
        )
        expect(parsed_response.find { _1["message_type"] == "promotion_custom_text" }["value"]).to eq(custom_text_2.message)
        expect(parsed_response.find { _1["message_type"] == "contact_manager" }["value"]).not_to eq(custom_text.message)
        expect(parsed_response.map(&:keys)).to all(match_array(serialized_keys))
      end
    end

    context "when business has custom auth integration type" do
      before do
        business.project_config.update(auth_integration_type: Enums::ProjectConfig::AuthIntegrationType::CUSTOM)
      end

      it "must return auth integration related messages" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/custom_texts",
          "/client/v2/custom_texts"
        ].sample
        get(path, as: :json, headers:)

        expect(response).to have_http_status(:ok)
        expect(parsed_response.pluck("message_type")).to include(*auth_integration_types)
      end
    end

    it "must not change default messages" do
      expect do
        path = [
          "/client/v2/businesses/#{business_2.cnpj}/custom_texts",
          "/client/v2/custom_texts"
        ].sample
        get path, as: :json, headers:
      end.not_to change(Enums::CustomTextType, :default_texts)
    end

    context "when business has cashback enabled but currency is not points" do
      before { business.update_columns(cashback: true, currency: "BRL") }

      it "renders ok" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/custom_texts",
          "/client/v2/custom_texts"
        ].sample
        get(path, as: :json, headers:)

        expect(response).to be_ok
        expect(parsed_response.pluck("message_type")).to include(*wallet_cashback_types)
        expect(parsed_response.pluck("message_type")).not_to include(*wallet_point_types)
        expect(parsed_response.map(&:keys)).to all(match_array(serialized_keys))
      end
    end

    context "when business has cashback enabled and currency is points" do
      before { business.update_columns(cashback: true, currency: "points") }

      it "renders ok" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/custom_texts",
          "/client/v2/custom_texts"
        ].sample
        get(path, as: :json, headers:)

        expect(response).to be_ok
        expect(parsed_response.pluck("message_type")).to include(*wallet_point_types)
        expect(parsed_response.pluck("message_type")).not_to include(*wallet_cashback_types)
        expect(parsed_response.map(&:keys)).to all(match_array(serialized_keys))
      end
    end

    context "when the business has no message" do
      before do
        custom_text.destroy!
      end

      it "returns all messages" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/custom_texts",
          "/client/v2/custom_texts"
        ].sample
        get(path, as: :json, headers:)

        expect(response).to have_http_status(:ok)
        expect(parsed_response.pluck("message_type")).to match_array(
          Enums::CustomTextType.all - cashback_discount_types - auth_integration_types - wallet_point_types
        )

        parsed_response.each do |message|
          message_type = message["message_type"]

          expect(message["value"]).to eq(Enums::CustomTextType.translate(message_type)[:value])
        end
      end
    end
  end

  describe "#upsert" do
    let(:message) { "Changed message" }
    let(:params) do
      [
        {
          message_type: Enums::CustomTextType::CONTACT_MANAGER,
          value: message
        }
      ]
    end

    it "upserts the business messages and returns all messages" do
      path = [
        "/client/v2/businesses/#{business.cnpj}/custom_texts",
        "/client/v2/custom_texts"
      ].sample
      put(path, params:, as: :json, headers:)

      expect(response).to have_http_status(:ok)
      expect(parsed_response.pluck("message_type")).to match_array(
        Enums::CustomTextType.all - cashback_discount_types - auth_integration_types - wallet_point_types
      )
      expect(parsed_response.find { _1["message_type"] == "contact_manager" }["value"]).to eq(message)
      expect(parsed_response.map(&:keys)).to all(match_array(serialized_keys))
    end

    context "when the business has all the messages defined" do
      let!(:custom_text_two) do
        create(:custom_text,
          business:,
          message_type: Enums::CustomTextType::INACTIVE_BUSINESS,
          message: "Some message 2")
      end
      let(:message_two) { "Changed message 2" }
      let(:params) do
        [
          {
            message_type: Enums::CustomTextType::CONTACT_MANAGER,
            value: message
          },
          {
            message_type: Enums::CustomTextType::INACTIVE_BUSINESS,
            value: message_two
          }
        ]
      end

      it "upserts the business messages and returns all messages" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/custom_texts",
          "/client/v2/custom_texts"
        ].sample
        put(path, params:, as: :json, headers:)

        expect(response).to have_http_status(:ok)
        expect(parsed_response.pluck("message_type")).to match_array(
          Enums::CustomTextType.all - cashback_discount_types - auth_integration_types - wallet_point_types
        )
        expect(parsed_response.find { _1["message_type"] == "contact_manager" }["value"]).to eq(message)
        expect(parsed_response.find { _1["message_type"] == "inactive_business" }["value"]).to eq(message_two)
        expect(parsed_response.map(&:keys)).to all(match_array(serialized_keys))
      end
    end
  end
end
