require "rails_helper"

RSpec.describe Client::V2::OrganizationProfilesController, type: :request do
  let!(:business) { create(:business) }
  let!(:client_employee) { create(:client_employee, :admin_client, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  describe "#create" do
    let(:serializer_keys) do
      %w[
        id
        top_background_image
        cashback_manage
        categories
        cnpj
        contact_telephone
        description
        editable
        facebook
        instagram
        logo_image
        name
        organization_id
        short_description
      ]
    end

    it "renders created and creates full profile for existing organization" do
      category = create(:category, business_id: business.id)
      organization = create(
        :organization,
        description: FFaker::Lorem.phrase(10),
        short_description: FFaker::Lorem.characters(70),
        logo_image: sample_file,
        top_background_image: sample_file
      )
      params = {
        organization_id: organization.id,
        description: FFaker::Lorem.phrase(8),
        short_description: FFaker::Lorem.characters(60),
        logo_image: sample_file,
        top_background_image: sample_file,
        category_ids: "[#{category.id}]"
      }

      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organization_profiles",
          "/client/v2/organization_profiles"
        ].sample
        post(path, headers:, params:)
      end.to change(OrganizationProfile, :count).by(1)
        .and not_change { organization.reload.attributes }

      expect(response).to be_created
      expect(response_hash.keys).to match_array(serializer_keys)
      organization_profile = OrganizationProfile.last
      expect(response_hash).to eq(
        "id" => organization_profile.id,
        "top_background_image" => organization_profile.top_background_image.url,
        "cashback_manage" => organization_profile.cashback_manage,
        "contact_telephone" => organization.contact_telephone,
        "description" => organization_profile.description,
        "facebook" => organization.facebook,
        "instagram" => organization.instagram,
        "logo_image" => organization_profile.logo_image.url,
        "name" => organization.name,
        "organization_id" => organization.id,
        "short_description" => organization_profile.short_description,
        "categories" => [{
          "id" => category.id,
          "title" => "#{category.title} - #{business.name}"
        }],
        "editable" => business.id == organization&.creator_id,
        "cnpj" => organization.cnpj
      )
    end

    it "renders created and creates partial profile for existing organization" do
      category = create(:category, business_id: business.id)
      organization = create(
        :organization,
        description: FFaker::Lorem.phrase(10),
        short_description: FFaker::Lorem.characters(70),
        logo_image: sample_file,
        top_background_image: sample_file
      )
      params = {
        organization_id: organization.id,
        description: FFaker::Lorem.phrase(8),
        category_ids: "[#{category.id}]"
      }

      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organization_profiles",
          "/client/v2/organization_profiles"
        ].sample
        post(path, headers:, params:)
      end.to change(OrganizationProfile, :count).by(1)
        .and not_change { organization.reload.attributes }

      expect(response).to be_created
      expect(response_hash.keys).to match_array(serializer_keys)
      organization_profile = OrganizationProfile.last
      expect(response_hash).to eq(
        "id" => organization_profile.id,
        "top_background_image" => organization.top_background_image.url,
        "cashback_manage" => organization_profile.cashback_manage,
        "contact_telephone" => organization.contact_telephone,
        "description" => organization_profile.description,
        "facebook" => organization.facebook,
        "instagram" => organization.instagram,
        "logo_image" => organization.logo_image.url,
        "name" => organization.name,
        "organization_id" => organization.id,
        "short_description" => organization.short_description,
        "categories" => [{
          "id" => category.id,
          "title" => "#{category.title} - #{business.name}"
        }],
        "editable" => business.id == organization&.creator_id,
        "cnpj" => organization.cnpj
      )
    end

    it "renders created and creates both organization and its profile" do
      category = create(:category, business_id: business.id)
      params = {
        category_ids: "[#{category.id}]",
        cnpj: FFaker::IdentificationBR.cnpj,
        description: FFaker::LoremBR.phrase(8),
        short_description: FFaker::LoremBR.characters(60),
        name: FFaker::Company.name,
        instagram: FFaker::Internet.http_url,
        facebook: FFaker::Internet.http_url,
        contact_telephone: FFaker::PhoneNumberBR.phone_number,
        logo_image: sample_file,
        top_background_image: sample_file
      }

      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organization_profiles",
          "/client/v2/organization_profiles"
        ].sample
        post(path, headers:, params:)
      end.to change(Organization, :count).by(1)
        .and change(OrganizationProfile, :count).by(1)

      expect(response).to be_created
      expect(response_hash.keys).to match_array(serializer_keys)
      organization = Organization.last
      expect(organization.all_projects).to be false
      organization_profile = OrganizationProfile.last
      expect(response_hash).to eq(
        "id" => organization_profile.id,
        "top_background_image" => organization.top_background_image.url,
        "cashback_manage" => organization_profile.cashback_manage,
        "contact_telephone" => organization.contact_telephone,
        "description" => organization.description,
        "facebook" => organization.facebook,
        "instagram" => organization.instagram,
        "logo_image" => organization.logo_image.url,
        "name" => organization.name,
        "organization_id" => organization.id,
        "short_description" => organization.short_description,
        "categories" => [{
          "id" => category.id,
          "title" => "#{category.title} - #{business.name}"
        }],
        "editable" => business.id == organization&.creator_id,
        "cnpj" => organization.cnpj
      )
    end

    it "renders errors with missing params when creating both organization and its profile" do
      category = create(:category)
      params = {
        category_id: category.id,
        description: FFaker::LoremBR.phrase(8),
        short_description: FFaker::LoremBR.characters(60),
        name: FFaker::Company.name,
        instagram: FFaker::Internet.http_url,
        facebook: FFaker::Internet.http_url,
        contact_telephone: FFaker::PhoneNumberBR.phone_number,
        logo_image: sample_file,
        top_background_image: sample_file
      }

      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organization_profiles",
          "/client/v2/organization_profiles"
        ].sample
        post(path, headers:, params:)
      end.to change(Organization, :count).by(0)
        .and change(OrganizationProfile, :count).by(0)

      expect(response).to have_http_status(:precondition_failed)
      expect(response_hash["error"]).to eq("A requisição falhou devido a ausência de parâmetro: cnpj")
    end
  end

  def response_hash
    JSON.parse(response.body)
  end

  def sample_file
    Rack::Test::UploadedFile.new(Rails.root.join("spec/fixtures/samplefile.png"))
  end
end
