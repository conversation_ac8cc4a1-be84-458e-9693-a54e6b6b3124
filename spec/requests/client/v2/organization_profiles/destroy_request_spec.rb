require "rails_helper"

RSpec.describe Client::V2::OrganizationProfilesController, type: :request do
  let!(:business) { create(:business) }
  let!(:client_employee) { create(:client_employee, :admin_client, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  let(:json_parse_response_body) { JSON.parse(response.body) }

  describe "#destroy" do
    it "renders no content and deletes organization profile" do
      organization = create(:organization)
      organization_profile = create(:organization_profile, organization:, business:)

      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organization_profiles/#{organization_profile.id}",
          "/client/v2/organization_profiles/#{organization_profile.id}"
        ].sample
        delete(path, headers:)
      end.to change(OrganizationProfile, :count).by(-1)

      expect(response).to be_no_content
    end

    context "when business unrelated to the current user" do
      let(:unrelated_business) { create(:business) }
      let(:headers) do
        {
          "X-ClientEmployee-Email": client_employee.email,
          "X-ClientEmployee-Token": client_employee.authentication_token,
          "Tenant-id": unrelated_business.cnpj
        }
      end

      it "renders forbidden" do
        organization = create(:organization)
        organization_profile = create(:organization_profile, organization:, business:)

        path = [
          "/client/v2/businesses/#{unrelated_business.id}/organization_profiles/#{organization_profile.id}",
          "/client/v2/organization_profiles/#{organization_profile.id}"
        ].sample
        delete(path, headers:)

        expect(response).to have_http_status(:not_found)
        expect(json_parse_response_body["error"]).to eq("Business não encontrado")
      end
    end

    context "when client employee does not have access to business of organization profile" do
      it "renders not found" do
        unrelated_business = create(:business)
        organization = create(:organization)
        organization_profile = create(:organization_profile, organization:, business: unrelated_business)

        path = [
          "/client/v2/businesses/#{business.id}/organization_profiles/#{organization_profile.id}",
          "/client/v2/organization_profiles/#{organization_profile.id}"
        ].sample
        delete(path, headers:)

        expect(response).to be_not_found
      end
    end
  end
end
