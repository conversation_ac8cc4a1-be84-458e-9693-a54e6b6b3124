require "rails_helper"

RSpec.describe Client::V2::OrganizationProfiles::CashbackImportsController, type: :request do
  describe "#update" do
    context "when client_employee is admin_client" do
      let!(:business) { create(:business, cashback_manage: true) }
      let!(:client_employee) { create(:client_employee, :admin_client, businesses: [business]) }
      let(:headers) do
        {
          "X-ClientEmployee-Email": client_employee.email,
          "X-ClientEmployee-Token": client_employee.authentication_token,
          "Tenant-id": business.cnpj
        }
      end

      let(:active_organization) { create(:organization, active: true) }
      let(:create_organization_profile) { create(:organization_profile, organization: active_organization, business:, cashback_manage: true) }

      specify do
        organization_profile = create_organization_profile

        path = [
          "/client/v2/businesses/#{business.cnpj}/organization_profiles/#{organization_profile.id}/cashback_imports",
          "/client/v2/organization_profiles/#{organization_profile.id}/cashback_imports"
        ].sample
        patch path, headers:, as: :json

        expect(response).to have_http_status(:forbidden)
      end
    end

    context "when client_employee is admin_lecupon" do
      let(:business) { create(:business, cashback_manage: true) }
      let!(:client_employee) { create(:client_employee, :admin_lecupon) }
      let(:headers) do
        {
          "X-ClientEmployee-Email": client_employee.email,
          "X-ClientEmployee-Token": client_employee.authentication_token,
          "Tenant-id": business.cnpj
        }
      end

      let(:json_parse_response_body) { JSON.parse(response.body) }

      context "given a active organization" do
        let(:active_organization) { create(:organization, active: true) }
        let(:create_org_profile_with_cashback_manage_true) { create(:organization_profile, organization: active_organization, business:, cashback_manage: true) }
        let(:create_org_profile_with_cashback_manage_false) { create(:organization_profile, organization: active_organization, business:, cashback_manage: false) }

        it "expected to change the cashback_manage to true" do
          organization_profile = create_org_profile_with_cashback_manage_false
          params = {cashback_manage: true}

          expect do
            path = [
              "/client/v2/businesses/#{business.cnpj}/organization_profiles/#{organization_profile.id}/cashback_imports",
              "/client/v2/organization_profiles/#{organization_profile.id}/cashback_imports"
            ].sample
            patch path, headers:, params:, as: :json

            organization_profile.reload
          end.to change(organization_profile, :cashback_manage).from(false).to(true)
          expect(response).to have_http_status(:ok)
        end

        it "expected to change the cashback_manage to false" do
          organization_profile = create_org_profile_with_cashback_manage_true
          params = {cashback_manage: false}

          expect do
            path = [
              "/client/v2/businesses/#{business.cnpj}/organization_profiles/#{organization_profile.id}/cashback_imports",
              "/client/v2/organization_profiles/#{organization_profile.id}/cashback_imports"
            ].sample
            patch path, headers:, params:, as: :json

            organization_profile.reload
          end.to change(organization_profile, :cashback_manage).from(true).to(false)
          expect(response).to have_http_status(:ok)
        end

        context "when params is invalid" do
          it "expected to change the cashback_manage to false" do
            organization_profile = create_org_profile_with_cashback_manage_true
            params = {cashback_manage: nil}

            expect do
              path = [
                "/client/v2/businesses/#{business.cnpj}/organization_profiles/#{organization_profile.id}/cashback_imports",
                "/client/v2/organization_profiles/#{organization_profile.id}/cashback_imports"
              ].sample
              patch path, headers:, params:, as: :json

              organization_profile.reload
            end.to not_change(organization_profile, :cashback_manage)
            expect(response).to have_http_status(:precondition_failed)
            expect(json_parse_response_body["error"]).to eq("A requisição falhou devido a ausência de parâmetro: cashback_manage")
          end
        end

        context "when business is inactve" do
          specify do
            business.update!(status: Business::Status::SUSPENDED_BY_OVERDUE)
            organization_profile = create_org_profile_with_cashback_manage_true

            path = [
              "/client/v2/businesses/#{business.cnpj}/organization_profiles/#{organization_profile.id}/cashback_imports",
              "/client/v2/organization_profiles/#{organization_profile.id}/cashback_imports"
            ].sample
            patch path, headers:, as: :json

            expect(response).to have_http_status(:not_found)
          end
        end

        context "when profile is from another business" do
          let(:another_business) { create(:business, cashback_manage: true) }
          let(:create_organization_profile) { create(:organization_profile, organization: active_organization, business: another_business, cashback_manage: false) }

          specify do
            organization_profile = create_organization_profile

            path = [
              "/client/v2/businesses/#{business.cnpj}/organization_profiles/#{organization_profile.id}/cashback_imports",
              "/client/v2/organization_profiles/#{organization_profile.id}/cashback_imports"
            ].sample
            patch path, headers:, as: :json

            expect(response).to have_http_status(:not_found)
          end
        end

        context "when business cannot manage cashback" do
          let(:business) { create(:business, cashback_manage: false) }

          it "renders error" do
            organization_profile = create_org_profile_with_cashback_manage_true

            path = [
              "/client/v2/businesses/#{business.cnpj}/organization_profiles/#{organization_profile.id}/cashback_imports",
              "/client/v2/organization_profiles/#{organization_profile.id}/cashback_imports"
            ].sample
            patch path, headers:, as: :json

            expect(response).to have_http_status(:forbidden)
            expect(json_parse_response_body["error"]).to eq("Gerenciamento de cashback não permitido")
          end
        end
      end

      context "given a blocklisted organization" do
        let(:blocklisted_organization) do
          organization = create(:organization, active: true)
          create(:organization_blocklist, business:, organization:)

          organization
        end
        let(:create_organization_profile) { create(:organization_profile, organization: blocklisted_organization, business:, cashback_manage: false) }

        it "is expected response to be ok" do
          organization_profile = create_organization_profile
          params = {cashback_manage: true}

          path = [
            "/client/v2/businesses/#{business.cnpj}/organization_profiles/#{organization_profile.id}/cashback_imports",
            "/client/v2/organization_profiles/#{organization_profile.id}/cashback_imports"
          ].sample
          patch path, headers:, params:, as: :json

          expect(response).to have_http_status(:ok)
        end
      end

      context "given a inactive organization" do
        let(:inactive_organization) { create(:organization, active: false) }
        let(:create_organization_profile) { create(:organization_profile, organization: inactive_organization, business:, cashback_manage: false) }

        specify do
          organization_profile = create_organization_profile

          path = [
            "/client/v2/businesses/#{business.cnpj}/organization_profiles/#{organization_profile.id}/cashback_imports",
            "/client/v2/organization_profiles/#{organization_profile.id}/cashback_imports"
          ].sample
          patch path, headers:, as: :json

          expect(response).to have_http_status(:not_found)
        end
      end
    end
  end
end
