require "rails_helper"

RSpec.describe Client::V2::OrganizationProfilesController, type: :request do
  let!(:business) { create(:business) }
  let!(:client_employee) { create(:client_employee, :admin_client, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  describe "#show" do
    let(:serializer_keys) do
      %w[
        id
        top_background_image
        cashback_manage
        categories
        cnpj
        contact_telephone
        description
        editable
        facebook
        instagram
        logo_image
        name
        organization_id
        short_description
      ]
    end

    it "renders ok" do
      category = create(:category, business_id: business.id)
      organization = create(:organization, categories: [category])
      organization_profile = create(
        :organization_profile,
        organization:,
        business:,
        description: FFaker::Lorem.phrase(10),
        short_description: FFaker::LoremBR.characters(60),
        logo_image: sample_file,
        top_background_image: sample_file
      )

      path = [
        "/client/v2/businesses/#{business.id}/organization_profiles/#{organization_profile.id}",
        "/client/v2/organization_profiles/#{organization_profile.id}"
      ].sample
      get(path, headers:)

      expect(response).to be_ok
      expect(response_hash.keys).to match_array(serializer_keys)
      expect(response_hash).to eq(
        "id" => organization_profile.id,
        "top_background_image" => organization_profile.top_background_image.url,
        "cashback_manage" => organization_profile.cashback_manage,
        "cnpj" => organization.cnpj,
        "contact_telephone" => organization.contact_telephone,
        "description" => organization_profile.description,
        "facebook" => organization.facebook,
        "instagram" => organization.instagram,
        "logo_image" => organization_profile.logo_image.url,
        "name" => organization.name,
        "organization_id" => organization.id,
        "short_description" => organization_profile.short_description,
        "categories" => [{
          "id" => category.id,
          "title" => "#{category.title} - #{business.name}"
        }],
        "editable" => business.id == organization&.creator_id
      )
    end

    it "renders not found for unexisting profile" do
      path = [
        "/client/v2/businesses/#{business.id}/organization_profiles/0",
        "/client/v2/organization_profiles/0"
      ].sample
      get(path, headers:)

      expect(response).to be_not_found
    end

    context "when profile of another business" do
      let(:unrelated_business) { create(:business) }

      it "renders not found" do
        organization = create(:organization)
        organization_profile = create(:organization_profile, organization:, business: unrelated_business)

        path = [
          "/client/v2/businesses/#{business.id}/organization_profiles/#{organization_profile.id}",
          "/client/v2/organization_profiles/#{organization_profile.id}"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:not_found)
        expect(response_hash["error"]).to eq("Registro(s) não encontrado(s).")
      end
    end

    context "when business unrelated to the current user" do
      let(:unrelated_business) { create(:business) }
      let(:headers) do
        {
          "X-ClientEmployee-Email": client_employee.email,
          "X-ClientEmployee-Token": client_employee.authentication_token,
          "Tenant-id": unrelated_business.cnpj
        }
      end

      it "renders not_found" do
        organization = create(:organization)
        organization_profile = create(:organization_profile, organization:, business: unrelated_business)

        path = [
          "/client/v2/businesses/#{unrelated_business.id}/organization_profiles/#{organization_profile.id}",
          "/client/v2/organization_profiles/#{organization_profile.id}"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:not_found)
        expect(response_hash["error"]).to eq("Business não encontrado")
      end
    end
  end

  def response_hash
    JSON.parse(response.body)
  end

  def sample_file
    Rack::Test::UploadedFile.new(Rails.root.join("spec/fixtures/samplefile.png"))
  end
end
