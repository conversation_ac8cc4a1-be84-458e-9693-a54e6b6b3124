require "rails_helper"

RSpec.describe Client::V2::OrganizationProfilesController, type: :request do
  let!(:business) { create(:business) }
  let!(:client_employee) { create(:client_employee, :admin_client, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  describe "#update" do
    let(:serializer_keys) do
      %w[
        id
        top_background_image
        cashback_manage
        categories
        cnpj
        contact_telephone
        description
        editable
        facebook
        instagram
        logo_image
        name
        organization_id
        short_description
      ]
    end

    it "renders ok and updates organization profile" do
      category = create(:category, business_id: business.id)
      organization = create(:organization, creator: business, categories: [category])
      organization_profile = create(
        :organization_profile,
        organization:,
        business:,
        description: FFaker::Lorem.phrase(10),
        short_description: FFaker::LoremBR.characters(60),
        logo_image: sample_file,
        top_background_image: sample_file
      )
      new_category = create(:category, :organization_category, business_id: business.id)
      params = {
        description: FFaker::Lorem.phrase(12),
        short_description: FFaker::LoremBR.characters(70),
        logo_image: sample_file,
        top_background_image: sample_file,
        category_ids: [new_category.id],
        contact_telephone: FFaker::PhoneNumberBR.phone_number,
        facebook: "https://#{FFaker::Internet.domain_name}",
        instagram: "https://#{FFaker::Internet.domain_name}",
        name: FFaker::Lorem.sentence
      }

      expect do
        path = [
          "/client/v2/businesses/#{business.id}/organization_profiles/#{organization_profile.id}",
          "/client/v2/organization_profiles/#{organization_profile.id}"
        ].sample
        put path, headers:, params:
      end.to change { organization_profile.reload.description }.to(params[:description])
        .and change { organization_profile.short_description }.to(params[:short_description])
        .and change { organization_profile.logo_image.url }
        .and change { organization_profile.top_background_image.url }
        .and change { organization_profile.organization.reload.categories.ids }.to([new_category.id])
        .and change { organization_profile.organization.contact_telephone }.to(params[:contact_telephone])
        .and change { organization_profile.organization.facebook }.to(params[:facebook])
        .and change { organization_profile.organization.instagram }.to(params[:instagram])
        .and change { organization_profile.organization.name }.to(params[:name])

      expect(response).to be_ok
      expect(response_hash.keys).to match_array(serializer_keys)
      expect(response_hash).to eq(
        "id" => organization_profile.id,
        "top_background_image" => organization_profile.top_background_image.url,
        "cashback_manage" => organization_profile.cashback_manage,
        "cnpj" => organization.cnpj,
        "contact_telephone" => params[:contact_telephone],
        "description" => organization_profile.description,
        "facebook" => params[:facebook],
        "instagram" => params[:instagram],
        "logo_image" => organization_profile.logo_image.url,
        "name" => params[:name],
        "organization_id" => organization.id,
        "short_description" => organization_profile.short_description,
        "categories" => [{
          "id" => new_category.id,
          "title" => "#{new_category.title} - #{business.name}"
        }],
        "editable" => business.id == organization&.creator_id
      )
    end

    context "when organization was created by another business" do
      let(:create_organization_with_profile) {
        organization = create(:organization)
        create(
          :organization_profile,
          organization:,
          business:,
          description: FFaker::Lorem.phrase(10)
        )
      }

      it "expected not update organization attributes" do
        organization = create(:organization)
        organization_profile = create(
          :organization_profile,
          organization:,
          business:,
          description: FFaker::Lorem.phrase(10),
          short_description: FFaker::LoremBR.characters(60),
          logo_image: sample_file,
          top_background_image: sample_file
        )
        new_category = create(:category, :organization_category)
        params = {
          description: FFaker::Lorem.phrase(12),
          short_description: FFaker::LoremBR.characters(70),
          logo_image: sample_file,
          top_background_image: sample_file,
          category_ids: [new_category.id],
          contact_telephone: FFaker::PhoneNumberBR.phone_number,
          facebook: "https://#{FFaker::Internet.domain_name}",
          instagram: "https://#{FFaker::Internet.domain_name}",
          name: FFaker::Lorem.sentence
        }

        expect do
          path = [
            "/client/v2/businesses/#{business.id}/organization_profiles/#{organization_profile.id}",
            "/client/v2/organization_profiles/#{organization_profile.id}"
          ].sample
          put path, headers:, params:
        end.to change { organization_profile.reload.description }.to(params[:description])
          .and change { organization_profile.short_description }.to(params[:short_description])
          .and change { organization_profile.logo_image.url }
          .and change { organization_profile.top_background_image.url }
          .and not_change { organization_profile.organization.reload.attributes }
      end
    end

    context "when business unrelated to the current user" do
      let(:unrelated_business) { create(:business) }
      let(:headers) do
        {
          "X-ClientEmployee-Email": client_employee.email,
          "X-ClientEmployee-Token": client_employee.authentication_token,
          "Tenant-id": unrelated_business.cnpj
        }
      end

      it "renders not_found" do
        organization = create(:organization)
        organization_profile = create(
          :organization_profile,
          organization:,
          business:,
          description: FFaker::Lorem.phrase(10)
        )

        path = [
          "/client/v2/businesses/#{unrelated_business.id}/organization_profiles/#{organization_profile.id}",
          "/client/v2/organization_profiles/#{organization_profile.id}"
        ].sample
        put(path, headers:)

        expect(response).to have_http_status(:not_found)
        expect(response_hash["error"]).to eq("Business não encontrado")
      end
    end

    context "when client employee does not have access to business of organization profile" do
      it "renders not found" do
        unrelated_business = create(:business)
        organization = create(:organization)
        organization_profile = create(
          :organization_profile,
          organization:,
          business: unrelated_business,
          description: FFaker::Lorem.phrase(10)
        )

        path = [
          "/client/v2/businesses/#{business.id}/organization_profiles/#{organization_profile.id}",
          "/client/v2/organization_profiles/#{organization_profile.id}"
        ].sample
        put(path, headers:)

        expect(response).to be_not_found
      end
    end
  end

  def response_hash
    JSON.parse(response.body)
  end

  def sample_file
    Rack::Test::UploadedFile.new(Rails.root.join("spec/fixtures/samplefile.png"))
  end
end
