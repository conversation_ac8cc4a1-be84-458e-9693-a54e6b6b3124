require "rails_helper"

RSpec.describe Client::V2::OrganizationProfilesController, type: :request do
  let!(:business) { create(:business) }
  let!(:client_employee) { create(:client_employee, :admin_client, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  describe "#index" do
    let(:serializer_keys) { %w[id organization_id name logo_image] }

    it "returns organization profiles ordered by name" do
      organization_alphabetically_first = create(:organization, name: "Amazon", logo_image: sample_file)
      organization_alphabetically_first_profile = create(
        :organization_profile,
        business:,
        organization: organization_alphabetically_first,
        logo_image: sample_file
      )
      organization_alphabetically_second = create(:organization, name: "Britânia", logo_image: sample_file)
      organization_alphabetically_second_profile = create(
        :organization_profile,
        business:,
        organization: organization_alphabetically_second,
        logo_image: sample_file
      )
      unrelated_business = create(:business)
      organization_another_business = create(:organization, name: "Ce<PERSON>uro", logo_image: sample_file)
      create(
        :organization_profile,
        business: unrelated_business,
        organization: organization_another_business,
        logo_image: sample_file
      )
      inactive_organization = create(:organization, name: "Americanas", active: false)
      create(
        :organization_profile,
        business:,
        organization: inactive_organization,
        logo_image: sample_file
      )

      path = [
        "/client/v2/businesses/#{business.id}/organization_profiles",
        "/client/v2/organization_profiles"
      ].sample
      get(path, headers:)

      expect(response).to be_ok
      expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
      expect(response_hash).to eq([
        {
          "id" => organization_alphabetically_first_profile.id,
          "organization_id" => organization_alphabetically_first.id,
          "name" => organization_alphabetically_first.name,
          "logo_image" => organization_alphabetically_first_profile.logo_image.url
        },
        {
          "id" => organization_alphabetically_second_profile.id,
          "organization_id" => organization_alphabetically_second.id,
          "name" => organization_alphabetically_second.name,
          "logo_image" => organization_alphabetically_second_profile.logo_image.url
        }
      ])
    end

    it "filters organization profiles by name" do
      organization = create(:organization, name: "Amazon")
      organization_profile = create(:organization_profile, business:, organization:)
      organization_not_filtered = create(:organization, name: "Americanas")
      organization_not_filtered_profile = create(
        :organization_profile,
        business:,
        organization: organization_not_filtered
      )
      params = {term: " ama  "}

      path = [
        "/client/v2/businesses/#{business.id}/organization_profiles",
        "/client/v2/organization_profiles"
      ].sample
      get(path, params:, headers:)

      expect(response).to be_ok
      expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
      expect(response_hash.pluck("id"))
        .to include(organization_profile.id)
        .and not_include(organization_not_filtered_profile.id)
    end

    it "filters organization profiles by organization cnpj" do
      organization = create(:organization, cnpj: FFaker::IdentificationBR.cnpj)
      organization_profile = create(:organization_profile, business:, organization:)
      organization_not_filtered = create(:organization, cnpj: FFaker::IdentificationBR.cnpj)
      organization_not_filtered_profile = create(
        :organization_profile,
        business:,
        organization: organization_not_filtered
      )
      params = {term: CNPJ.new(organization.cnpj).formatted}

      path = [
        "/client/v2/businesses/#{business.id}/organization_profiles",
        "/client/v2/organization_profiles"
      ].sample
      get(path, params:, headers:)

      expect(response).to be_ok
      expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
      expect(response_hash.pluck("id"))
        .to include(organization_profile.id)
        .and not_include(organization_not_filtered_profile.id)
    end

    it "filters organization profiles by branch cnpj" do
      organization = create(:organization)
      branch = create(:branch, organization:, cnpj: FFaker::IdentificationBR.cnpj)
      organization_profile = create(:organization_profile, business:, organization:)
      organization_not_filtered = create(:organization)
      create(:branch, organization: organization_not_filtered, cnpj: FFaker::IdentificationBR.cnpj)
      organization_not_filtered_profile = create(
        :organization_profile,
        business:,
        organization: organization_not_filtered
      )
      params = {term: CNPJ.new(branch.cnpj).formatted}

      path = [
        "/client/v2/businesses/#{business.id}/organization_profiles",
        "/client/v2/organization_profiles"
      ].sample
      get(path, params:, headers:)

      expect(response).to be_ok
      expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
      expect(response_hash.pluck("id"))
        .to include(organization_profile.id)
        .and not_include(organization_not_filtered_profile.id)
    end

    context "when business unrelated to the current user" do
      let(:unrelated_business) { create(:business) }
      let(:headers) do
        {
          "X-ClientEmployee-Email": client_employee.email,
          "X-ClientEmployee-Token": client_employee.authentication_token,
          "Tenant-id": unrelated_business.cnpj
        }
      end

      it "renders not_found" do
        path = [
          "/client/v2/businesses/#{unrelated_business.id}/organization_profiles",
          "/client/v2/organization_profiles"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:not_found)
        expect(response_hash["error"]).to eq("Business não encontrado")
      end
    end
  end

  def response_hash
    JSON.parse(response.body)
  end

  def sample_file
    Rack::Test::UploadedFile.new(Rails.root.join("spec/fixtures/samplefile.png"))
  end
end
