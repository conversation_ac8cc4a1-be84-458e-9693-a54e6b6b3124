# frozen_string_literal: true

require "rails_helper"
require "support/shared_examples/client_employee_request_shared_examples"

RSpec.describe Client::V2::CustomFieldsController, type: :request do
  let(:business) { create(:business) }
  let(:another_business) { create(:business) }

  let!(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end
  let(:response_hash) { JSON.parse(response.body) }
  let(:serialized_keys) do
    %w[id name param_name label description input_type placeholder
      sign_in_param sign_up_param account_update_param]
  end

  describe "#index" do
    let!(:custom_field) { create(:custom_field, business_id: business.id) }
    let!(:custom_field_2) { create(:custom_field, business_id: business.id) }
    let!(:custom_field_another_business) { create(:custom_field, business_id: another_business.id) }

    it "returns all business' custom_fields" do
      path = [
        "/client/v2/businesses/#{business.cnpj}/custom_fields",
        "/client/v2/custom_fields"
      ].sample
      get(path, headers:)

      expect(response).to be_ok
      expect(response_hash.map(&:keys)).to all(match_array(serialized_keys))
      expect(response_hash.pluck("id")).to match_array([custom_field.id, custom_field_2.id])
    end

    context "when client_employee not related to business" do
      let(:client_employee) { create(:client_employee, :admin_client) }

      it "returns not found" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/custom_fields",
          "/client/v2/custom_fields"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:not_found)
        expect(response_hash["error"]).to eq("Business não encontrado")
      end
    end

    context "without client_employee authentication" do
      before do
        path = [
          "/client/v2/businesses/#{business.cnpj}/custom_fields",
          "/client/v2/custom_fields"
        ].sample
        get path
      end

      it_behaves_like "unauthorized client_employee"
    end
  end

  describe "#show" do
    let!(:custom_field) { create(:custom_field, business_id: business.id) }

    context "when custom_field belongs to business" do
      it "returns custom_field" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/custom_fields/#{custom_field.id}",
          "/client/v2/custom_fields/#{custom_field.id}"
        ].sample
        get(path, headers:)

        expect(response).to be_ok
        expect(response_hash.keys).to match_array(serialized_keys)
      end
    end

    context "when custom_field doest not belong to business" do
      let!(:custom_field) { create(:custom_field, business_id: another_business.id) }

      it "renders not found" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/custom_fields/#{custom_field.id}",
          "/client/v2/custom_fields/#{custom_field.id}"
        ].sample
        get(path, headers:)

        expect(response).to be_not_found
      end
    end

    context "when client_employee not related to business" do
      let(:client_employee) { create(:client_employee, :admin_client) }

      it "returns not found" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/custom_fields/#{custom_field.id}",
          "/client/v2/custom_fields/#{custom_field.id}"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:not_found)
        expect(response_hash["error"]).to eq("Business não encontrado")
      end
    end

    context "without client_employee authentication" do
      before do
        path = [
          "/client/v2/businesses/#{business.cnpj}/custom_fields/#{custom_field.id}",
          "/client/v2/custom_fields/#{custom_field.id}"
        ].sample
        get path
      end

      it_behaves_like "unauthorized client_employee"
    end
  end

  describe "#create" do
    let(:params) do
      {
        name: "Numero de inscrição",
        param_name: "number",
        label: "number",
        description: "Insira o seu número de inscrição da OAB",
        input_type: "number",
        placeholder: "123456",
        sign_in_param: true,
        sign_up_param: true
      }
    end

    it "creates business custom_field" do
      path = [
        "/client/v2/businesses/#{business.cnpj}/custom_fields",
        "/client/v2/custom_fields"
      ].sample
      post(path, headers:, params:)

      expect(response).to be_created
      expect(response_hash.keys).to match_array(serialized_keys)
    end

    context "without client_employee authentication" do
      before do
        path = [
          "/client/v2/businesses/#{business.cnpj}/custom_fields",
          "/client/v2/custom_fields"
        ].sample
        post path
      end

      it_behaves_like "unauthorized client_employee"
    end
  end

  describe "#update" do
    let!(:custom_field) { create(:custom_field, business_id: business.id) }

    let(:params) do
      {
        name: "Numero de Insc."
      }
    end

    context "when custom_field belongs to business" do
      it "updates custom_field" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/custom_fields/#{custom_field.id}",
          "/client/v2/custom_fields/#{custom_field.id}"
        ].sample
        patch(path, headers:, params:)

        expect(response).to be_ok
        expect(response_hash.keys).to match_array(serialized_keys)

        expect(response_hash["name"]).to eq(params[:name])
      end
    end

    context "when custom_field doest not belong to business" do
      let!(:custom_field) { create(:custom_field, business_id: another_business.id) }

      it "renders not found" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/custom_fields/#{custom_field.id}",
          "/client/v2/custom_fields/#{custom_field.id}"
        ].sample
        patch(path, headers:)

        expect(response).to be_not_found
      end
    end

    context "when client_employee not related to business" do
      let(:client_employee) { create(:client_employee, :admin_client) }

      it "returns not found" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/custom_fields/#{custom_field.id}",
          "/client/v2/custom_fields/#{custom_field.id}"
        ].sample
        patch(path, headers:)

        expect(response).to have_http_status(:not_found)
        expect(response_hash["error"]).to eq("Business não encontrado")
      end
    end

    context "without client_employee authentication" do
      before do
        path = [
          "/client/v2/businesses/#{business.cnpj}/custom_fields/#{custom_field.id}",
          "/client/v2/custom_fields/#{custom_field.id}"
        ].sample
        patch path
      end

      it_behaves_like "unauthorized client_employee"
    end
  end

  describe "#destroy" do
    let!(:custom_field) { create(:custom_field, business_id: business.id) }

    context "when custom_field belongs to business" do
      it "destroys custom_field" do
        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/custom_fields/#{custom_field.id}",
            "/client/v2/custom_fields/#{custom_field.id}"
          ].sample
          delete(path, headers:)

          expect(response).to be_no_content
        end.to change(CustomField, :count).by(-1)
      end
    end

    context "when custom_field doest not belong to business" do
      let!(:custom_field) { create(:custom_field, business_id: another_business.id) }

      it "renders not found" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/custom_fields/#{custom_field.id}",
          "/client/v2/custom_fields/#{custom_field.id}"
        ].sample
        delete(path, headers:)

        expect(response).to be_not_found
      end
    end

    context "when client_employee not related to business" do
      let(:client_employee) { create(:client_employee, :admin_client) }

      it "returns not found" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/custom_fields/#{custom_field.id}",
          "/client/v2/custom_fields/#{custom_field.id}"
        ].sample
        delete(path, headers:)

        expect(response).to have_http_status(:not_found)
        expect(response_hash["error"]).to eq("Business não encontrado")
      end
    end

    context "without client_employee authentication" do
      before do
        path = [
          "/client/v2/businesses/#{business.cnpj}/custom_fields/#{custom_field.id}",
          "/client/v2/custom_fields/#{custom_field.id}"
        ].sample
        delete path
      end

      it_behaves_like "unauthorized client_employee"
    end
  end
end
