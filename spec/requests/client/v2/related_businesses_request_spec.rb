# frozen_string_literal: true

require "rails_helper"

RSpec.describe Client::V2::RelatedBusinessesController, type: :request do
  let(:serialized_keys) do
    %w[id name cnpj active]
  end
  let(:current_client_employee) { create(:client_employee, :admin_lecupon) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": current_client_employee.email,
      "X-ClientEmployee-Token": current_client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  let(:response_hash) { JSON.parse(response.body) }

  describe "#index" do
    let(:client_employee) { create(:client_employee, :admin_client, businesses: [business]) }

    let!(:business) { create(:business) }
    let!(:sub_business_one) { create(:business, main_business: business) }
    let!(:sub_business_two) { create(:business, main_business: business) }

    let!(:unrelated_businesses) { create_list(:business, 2) }

    it "must return all businesses related to the specified one" do
      path = [
        "/client/v2/businesses/#{business.cnpj}/related_businesses",
        "/client/v2/related_businesses"
      ].sample
      get(path, headers:)

      expect(response).to be_successful
      expect(response_hash.map(&:keys)).to all(match_array(serialized_keys))
      expect(response_hash.count).to eq(3)
      expect(response_hash.pluck("id")).to match_array([business.id, sub_business_one.id, sub_business_two.id])
    end

    context "when current business is not related to current client employee" do
      let(:businesses) { create_list(:business, 2) }

      context "when current client employee is admin client" do
        let(:current_client_employee) { create(:client_employee, :admin_client, businesses:) }

        it "must render forbidden" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/related_businesses",
            "/client/v2/related_businesses"
          ].sample
          get(path, headers:)

          expect(response).to have_http_status(:not_found)
          expect(response_hash["error"]).to eq("Business não encontrado")
        end
      end
    end
  end
end
