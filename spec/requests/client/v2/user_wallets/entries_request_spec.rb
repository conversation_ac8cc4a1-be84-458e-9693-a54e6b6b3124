require "rails_helper"

RSpec.describe Client::V2::UserWallets::EntriesController, type: :request do
  let(:business) { create(:business, :with_cashback, cashback_wallet_destination: :membership) }
  let(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:user) { create(:user, business:, cpf: "***********") }

  let!(:business_wallet) { create(:wallet, :membership, business:, balance: 845210) }
  let!(:user_wallet) { create(:wallet, :membership, user:, balance: 1298) }

  let!(:unrelated_business_wallet) { create(:wallet, :cashback, business:) }
  let!(:unrelated_user_wallet) { create(:wallet, :cashback, user:) }

  let!(:entry_one) do
    create(
      :wallet_entry,
      :credit,
      wallet: user_wallet,
      amount: 852,
      kind: Wallet::Entry::Kind::CASHBACK_AVAILABLE,
      created_at: "2022-03-09T09:21:38.000-03:00"
    )
  end
  let!(:entry_two) do
    create(
      :wallet_entry,
      :debit,
      wallet: user_wallet,
      amount: 852,
      kind: Wallet::Entry::Kind::MEMBERSHIP_PAYMENT,
      created_at: "2022-03-22T18:49:50.000-03:00"
    )
  end
  let!(:entry_three) do
    create(
      :wallet_entry,
      :credit,
      wallet: user_wallet,
      amount: 1298,
      kind: Wallet::Entry::Kind::CASHBACK_AVAILABLE,
      created_at: "2022-04-01T16:33:09.000-03:00"
    )
  end

  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end
  let(:response_hash) { JSON.parse(response.body) }

  describe "#index" do
    it "renders ok" do
      path = [
        "/client/v2/businesses/#{business.id}/user_wallets/entries",
        "/client/v2/user_wallets/entries"
      ].sample
      get(path, headers:)

      expect(response).to be_ok
      expect(response_hash).to eq([
        {
          "amount" => 852,
          "cpf" => "238.950.780-85",
          "created_at" => "2022-03-22T18:49:50.000-03:00"
        }
      ])
    end
  end
end
