require "rails_helper"
require "sidekiq/testing"

RSpec.describe User::BulkImportWorker do
  describe "#perform" do
    let(:business) { create(:business, :with_cashback) }
    let!(:authorized_user_group) { create(:authorized_user_group, business:) }

    context "when authorized_user already exists" do
      let!(:authorized_user) { create(:authorized_user, business:) }

      let(:user_params_list) {
        [{
          name: FFaker::Name.name,
          email: FFaker::Internet.email,
          cellphone: FFaker::PhoneNumberBR.phone_number,
          cpf: authorized_user.cpf,
          business_id: authorized_user.business_id
        }]
      }

      it "must update authorized_user" do
        expect do
          described_class.new.perform(user_params_list, authorized_user.business_id, [authorized_user_group.slug])
          authorized_user.reload
        end.to change(authorized_user, :name).to(Utils::NameNormalizer.call(user_params_list[0][:name]))
          .and change(authorized_user, :email).to(user_params_list[0][:email])
          .and change(authorized_user, :authorized_user_group_id).to(authorized_user_group.id)
      end
    end

    context "when authorized_user does not exists" do
      let(:user_params_list) {
        [{
          name: FFaker::Name.name,
          email: FFaker::Internet.email,
          cellphone: FFaker::PhoneNumberBR.phone_number,
          cpf: FFaker::IdentificationBR.cpf,
          business_id: business.id
        }]
      }

      it "must create authorized_user" do
        expect do
          described_class.new.perform(user_params_list, business.id, [authorized_user_group.slug])
        end.to change(AuthorizedUser, :count).by(1)
        authorized_user = AuthorizedUser.last
        expect(authorized_user.name).to eq(Utils::NameNormalizer.call(user_params_list[0][:name]))
        expect(authorized_user.email).to eq(user_params_list[0][:email])
        expect(authorized_user.phone).to eq(user_params_list[0][:cellphone].delete("^0-9"))
        expect(authorized_user.authorized_user_group_id).to eq(authorized_user_group.id)
      end
    end

    context "when user already exists" do
      let!(:user) { create(:user, business:) }
      let(:authorized_user) { user.authorized_user }

      let(:user_params_list) {
        [{
          name: FFaker::Name.name,
          email: FFaker::Internet.email,
          cellphone: FFaker::PhoneNumberBR.phone_number,
          cpf: user.cpf,
          business_id: user.business_id
        }]
      }

      it "must update user" do
        expect do
          described_class.new.perform(user_params_list, user.business_id, [authorized_user_group.slug])
          user.reload
          authorized_user.reload
        end.to change(user, :name).to(Utils::NameNormalizer.call(user_params_list[0][:name]))
          .and change(user, :email).to(user_params_list[0][:email])
          .and change(authorized_user, :authorized_user_group_id).to(authorized_user_group.id)
      end
    end

    context "when user does not exists" do
      let(:user_params_list) {
        [{
          name: FFaker::Name.name,
          email: FFaker::Internet.email,
          cellphone: FFaker::PhoneNumberBR.phone_number,
          cpf: FFaker::IdentificationBR.cpf,
          business_id: business.id
        }]
      }

      it "must create user" do
        expect do
          described_class.new.perform(user_params_list, business.id, [authorized_user_group.slug])
        end.to change(User, :count).by(1)
        user = User.order(:id).last
        expect(user.name).to eq(Utils::NameNormalizer.call(user_params_list[0][:name]))
        expect(user.email).to eq(user_params_list[0][:email])
        expect(user.cellphone).to eq(user_params_list[0][:cellphone].delete("^0-9"))
        expect(user.authorized_user.authorized_user_group_id).to eq(authorized_user_group.id)
        expect(user.wallets).to exist
      end
    end

    context "when authorized_user_group was not found by slug" do
      let(:user_params_list) {
        [{
          name: FFaker::Name.name,
          email: FFaker::Internet.email,
          cellphone: FFaker::PhoneNumberBR.phone_number,
          cpf: FFaker::IdentificationBR.cpf,
          business_id: business.id
        }]
      }

      it "must create and update authorized_users with no group" do
        described_class.new.perform(user_params_list, business.id, ["no-existing-group"])
        user = User.last
        expect(user.authorized_user.authorized_user_group).to eq(nil)
      end
    end
  end
end
