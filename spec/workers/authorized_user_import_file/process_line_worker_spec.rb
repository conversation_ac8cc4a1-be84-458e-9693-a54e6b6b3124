require "rails_helper"

RSpec.describe AuthorizedUserImportFile::ProcessLineWorker do
  describe "#perform" do
    let!(:business) { create(:business) }
    let(:import_temp_file) { create(:import_temp_file, file: auth_user_list_csv) }
    let(:authorized_user_import_file) { create(:authorized_user_import_file, import_temp_file:, business:) }

    let(:auth_user_list_csv) do
      tempfile = Tempfile.new(["auth_user_test", ".csv"], "tmp")
      CSV.open(tempfile, "w", headers: true, col_sep: ",") do |csv|
        csv << ["name", "cpf", "email", "custom_field_1", "user_tags"]
        csv << [auth_user_params[:name], auth_user_params[:cpf], auth_user_params[:email], auth_user_params[:custom_field_1], auth_user_params[:user_tags]]
      end

      tempfile
    end

    let!(:auth_user_params) do
      {
        name: FFaker::Name.name,
        cpf: CPF.new(FFaker::IdentificationBR.cpf).stripped,
        email: FFaker::Internet.email,
        custom_field_1: "metadata"
      }
    end

    let(:user_tags) { ["New Group", "Ignored Group"] }
    let(:header_line) { 1 }
    let(:line) { 1 }

    context "when authorized user is valid" do
      it "must create authorized user" do
        expect do
          described_class.new.perform(
            user_tags,
            auth_user_params,
            authorized_user_import_file.id,
            header_line + line
          )
        end.to change(AuthorizedUser, :count)

        auth_user = AuthorizedUser.first
        expect(auth_user.name).to eq(Utils::NameNormalizer.call(auth_user_params[:name]))
        expect(auth_user.cpf).to eq(auth_user_params[:cpf])
        expect(auth_user.email).to eq(auth_user_params[:email])
        expect(auth_user.custom_field_1).to eq(auth_user_params[:custom_field_1])
        expect(auth_user.authorized_user_group.slug).to eq("new-group")
        expect(auth_user.business).to eq(business)
        expect(auth_user.synced).to eq(false)
      end

      it "must increment import count" do
        described_class.new.perform(
          user_tags,
          auth_user_params,
          authorized_user_import_file.id,
          header_line + line
        )
        authorized_user_import_file.reload
        expect(authorized_user_import_file.import_count).to eq(1)
      end

      it "must not create import file error" do
        expect do
          described_class.new.perform(
            user_tags,
            auth_user_params,
            authorized_user_import_file.id,
            header_line + line
          )
        end.not_to change(AuthorizedUserImportError, :count)
      end

      context "when cpf is invalid but fixable" do
        context "when cpf is less than 11 digits" do
          context "when adding a 0 on the left validates the cpf" do
            let(:cpf) { "676115675" }
            let!(:auth_user_params) do
              {
                name: FFaker::Name.name,
                cpf: cpf.clone,
                email: FFaker::Internet.email,
                custom_field_1: "metadata"
              }
            end

            it "must create authorized user's cpf with 0 on the left" do
              expect do
                described_class.new.perform(
                  user_tags,
                  auth_user_params,
                  authorized_user_import_file.id,
                  header_line + line
                )
              end.to change(AuthorizedUser, :count)

              auth_user = AuthorizedUser.first
              expect(auth_user.cpf).to eq(cpf.prepend("00"))
            end
          end

          context "when adding a 0 on the left does not validate the cpf" do
            let(:cpf) { "8748201020" }

            let!(:auth_user_params) do
              {
                name: FFaker::Name.name,
                cpf: cpf.clone,
                email: FFaker::Internet.email,
                custom_field_1: "metadata"
              }
            end

            it "must not create authorized user" do
              expect do
                described_class.new.perform(
                  user_tags,
                  auth_user_params,
                  authorized_user_import_file.id,
                  header_line + line
                )
              end.not_to change(AuthorizedUser, :count)
            end
          end
        end

        context "when cpf is not stripped" do
          let(:cpf) { "87.482.010-30" }
          let!(:auth_user_params) do
            {
              name: FFaker::Name.name,
              cpf: cpf.clone,
              email: FFaker::Internet.email,
              custom_field_1: "metadata"
            }
          end

          it "must create authorized user with stripped cpf" do
            expect do
              described_class.new.perform(
                user_tags,
                auth_user_params,
                authorized_user_import_file.id,
                header_line + line
              )
            end.to change(AuthorizedUser, :count)

            auth_user = AuthorizedUser.first
            expect(auth_user.cpf).to eq(CPF.new(cpf.prepend("0")).stripped)
          end
        end
      end

      context "with existing authorized user" do
        let!(:authorized_user) do
          create(
            :authorized_user,
            business:,
            cpf: auth_user_params[:cpf],
            name: "old name",
            email: "<EMAIL>"
          )
        end

        it "updates the authorized user" do
          expect do
            described_class.new.perform(
              user_tags,
              auth_user_params,
              authorized_user_import_file.id,
              header_line + line
            )
            authorized_user.reload
          end.to change(authorized_user, :name).from(Utils::NameNormalizer.call("old name")).to(Utils::NameNormalizer.call(auth_user_params[:name]))
            .and change(authorized_user, :email).from("<EMAIL>").to(auth_user_params[:email])
            .and change(authorized_user, :authorized_user_group).from(nil).to(be_present)
            .and change(AuthorizedUser, :count).by(0)
        end

        context "when there is an authorized_user with same cpf on main_business" do
          let(:sub_business) { create(:business, main_business: business) }
          let!(:authorized_user) { create(:authorized_user, business: sub_business) }
          let!(:auth_user_params) do
            {
              name: FFaker::Name.name,
              cpf: authorized_user.cpf
            }
          end

          it "updates authorized user and transfer it to new business" do
            expect do
              described_class.new.perform(
                user_tags,
                auth_user_params,
                authorized_user_import_file.id,
                header_line + line
              )

              authorized_user.reload
            end.to change(authorized_user, :name).to(Utils::NameNormalizer.call(auth_user_params[:name]))
              .and change(authorized_user, :business_id).from(sub_business.id).to(business.id)
              .and change(AuthorizedUser, :count).by(0)
          end
        end
      end
    end

    context "when authorized user is not valid" do
      let!(:auth_user_params) do
        {
          name: FFaker::Name.name,
          cpf: nil,
          email: FFaker::Internet.email,
          custom_field_1: "metadata"
        }
      end

      it "must create import file error for specified line" do
        expect do
          described_class.new.perform(
            user_tags,
            auth_user_params,
            authorized_user_import_file.id,
            header_line + line
          )
        end.to change(AuthorizedUserImportError, :count)

        import_error = AuthorizedUserImportError.first
        expect(import_error.line).to eq(2)
        expect(import_error.authorized_user_import_file).to eq(authorized_user_import_file)
        expect(import_error.error_details).to include("Cpf inválido")
      end
    end

    context "when process count is equal to total amount after increment" do
      before do
        authorized_user_import_file.process_count = authorized_user_import_file.total_amount
        allow(AuthorizedUserImportFile::ExportErrorsWorker).to receive(:perform_async)
      end

      it "must schedule ExportErrorsWorker" do
        described_class.new.perform(
          user_tags,
          auth_user_params,
          authorized_user_import_file.id,
          header_line + line
        )

        expect(AuthorizedUserImportFile::ExportErrorsWorker).to have_received(:perform_async).with(authorized_user_import_file.id)
      end
    end
  end
end
