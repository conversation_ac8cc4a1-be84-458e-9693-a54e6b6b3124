# frozen_string_literal: true

require "rails_helper"

module Maintenance
  RSpec.describe MigrateVantagensClubeToPointsTask do
    describe "#process" do
      let!(:business) do
        create(
          :business,
          :with_cashback,
          cnpj: "84932664000189",
          currency: "BRL",
          fair_value: nil,
          days_to_expire_points: nil
        )
      end
      let!(:user) { create(:user, business:) }
      let!(:user_wallet) do
        create(:wallet, kind: Wallet::Kind::CASHBACK, currency: "BRL", user:, balance: 3779)
      end

      it "migrates Vantagens Clube to points" do
        expect { described_class.process }
          .to change(Wallet.inflow.where(currency: "points"), :count).by(1)
          .and change(Wallet.outflow.where(currency: "points"), :count).by(1)
          .and change(business.wallets.where(currency: "points"), :count).by(1)
          .and change(user.wallets.where(currency: "points", balance: 1260), :count).by(1)
          .and change(WalletEntry.credit.where(amount: 1260), :count).by(1)
          .and change { business.reload.fair_value }.to(0.03)
          .and change { business.reload.currency }.to("points")
          .and change { business.reload.days_to_expire_points }.to(365)
      end
    end
  end
end
