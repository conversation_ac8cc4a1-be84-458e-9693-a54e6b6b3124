# frozen_string_literal: true

require "rails_helper"

module Maintenance
  RSpec.describe ChangeBusinessToSubBusinessTask do
    let(:main_project_config) { create(:project_config) }
    let(:current_project_config) { create(:project_config) }
    let(:main_business) { create(:business, main_business: nil, project_config: main_project_config) }
    let(:current_business) { create(:business, main_business: nil, project_config: current_project_config) }
    let(:cashback_transfer_frequency) { create(:cashback_transfer_frequency, business: current_business) }
    let!(:menu) { create(:menu, business: current_business, exclusive_business: false) }
    let!(:exclusive_menu) { create(:menu, business: current_business, exclusive_business: current_business) }
    let(:user) { create(:user, business_id: current_business.id, main_business_id: current_business.id) }
    let(:authorized_user) { create(:authorized_user, business_id: current_business.id, main_business_id: current_business.id) }
    let(:task) { described_class.new }

    before do
      current_business.cashback_transfer_frequency = cashback_transfer_frequency
      task.business_to_turn_into_sub_business_id = current_business.id
      task.business_that_will_be_the_main_business_id = main_business.id
    end

    it "updates main_business_id and deletes project_config" do
      expect {
        task.process
        current_business.reload
      }.to change(current_business, :main_business_id).from(nil).to(main_business.id)
    end

    it "deletes the default configs" do
      expect {
        task.process
        current_business.reload
      }.to change(ProjectConfig, :count).by(-1)
        .and change(GiftcardConfig, :count).by(-1)
        .and change(MailerConfig, :count).by(-1)
        .and change(WebhookConfig, :count).by(-1)
        .and change(MemberReferralConfig, :count).by(-1)
        .and change(WebApplication, :count).by(-1)
        .and change(CashbackTransferFrequency, :count).by(-1)
        .and change(current_business, :project_config).from(current_project_config).to(main_project_config)
    end

    it "deletes non exclusive menus" do
      expect {
        task.process
      }.to change(Navigation, :count).by(-1)
    end

    it "updates users" do
      expect {
        task.process
        user.reload
        authorized_user.reload
      }.to change(user, :main_business_id).from(current_business.id).to(main_business.id)
        .and change(authorized_user, :main_business_id).from(current_business.id).to(main_business.id)
    end

    context "when business has open payouts with balance" do
      it "raises an error" do
        create(:payout, business: current_business, status:  Payout::Status::OPEN, total_amount: 100)
        expect { task.process }
          .to raise_error("Business has payouts with balance")
          .and not_change(ProjectConfig, :count)
      end
    end

    context "when business has wallets with balance" do
      let(:wallet) { create(:wallet, business: current_business, balance: 100, currency: "BRL", kind: Wallet::Kind::CASHBACK) }

      before { current_business.wallets << wallet }

      it "raises an error" do
        expect { task.process }
          .to raise_error("Business has wallets with balance")
          .and not_change(ProjectConfig, :count)
      end
    end

    context "when business has sub business" do
      it "raises an error" do
        create(:business, main_business: current_business)
        expect { task.process }
          .to raise_error("Business has sub business")
          .and not_change(ProjectConfig, :count)
      end
    end
  end
end
